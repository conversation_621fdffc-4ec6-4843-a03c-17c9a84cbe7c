import React, {useEffect, useRef, useState} from 'react';
import {CommonActions, NavigationContainer} from '@react-navigation/native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {Icon} from 'react-native-elements';
import analytics from '@react-native-firebase/analytics';
import {Adjust, AdjustConfig} from 'react-native-adjust';
import {LogBox, Linking, AppState} from 'react-native';
import * as i18n from './src/lang/i18n';
import LocalizationContext from './src/context/LocalizationContext';
import * as RNLocalize from 'react-native-localize';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Provider as UserProvider} from './src/context/UserContext';
import SettingsNavigator from './src/navigation/SettingsNavigator';
import OrdersNavigator from './src/navigation/OrdersNavigator';
import BasketNavigator from './src/navigation/BasketNavigator';
import BranchNavigator from './src/navigation/BrancNavigator';
import LoginNavigator from './src/navigation/LoginNavigator';
import HomeNavigator from './src/navigation/HomeNavigator';
import {navigationRef, navigate} from './src/navigationRef';
import {WirFloatingButton} from 'react-native-wir';
import * as Sentry from '@sentry/react-native';
import {
  routesWithoutAiAssistant,
  aiAssistantId,
  sentryDsn,
  operatingSystem,
} from './src/constants';

import {getDeviceInfo, readClipboardAndParse, safeParse} from './src/utils';
import mobileApi from './src/api/mobileApi';
import Clipboard from '@react-native-clipboard/clipboard';
Sentry.init({
  dsn: sentryDsn,
  enableSpotlight: __DEV__,
  beforeSend(event) {
    event.tags = {
      ...event.tags,
      platform: operatingSystem,
    };
    return event;
  },
});
LogBox.ignoreAllLogs();

const Tab = createBottomTabNavigator();

const App = () => {
  const appState = useRef(AppState.currentState);

  const routeRef = React.useRef();
  const [locale, setLocale] = React.useState();
  const [badge, setBadge] = React.useState(0);
  const [code, setCode] = React.useState('');
  const [currentRouteName, setCurrentRouteName] = useState('');
  const adjustConfig = new AdjustConfig(
    'lesa41070fls',
    AdjustConfig.EnvironmentProduction,
  );
  adjustConfig.setLogLevel(AdjustConfig.LogLevelDebug);
  Adjust.initSdk(adjustConfig);

  const localizationContext = React.useMemo(
    () => ({
      t: (scope, options) => i18n.t(scope, {locale, ...options}),
      locale,
      setLocale,
    }),
    [locale],
  );

  const handleLocalizationChange = React.useCallback(
    newLocale => {
      const newSetLocale = i18n.setI18nConfig(newLocale);
      setLocale(newSetLocale);
      AsyncStorage.setItem('@locale', newSetLocale)
        .then(() => {})
        .catch(e => {});
    },
    [locale],
  );

  const handleBadge = React.useCallback(
    newBadge => {
      setBadge(newBadge);
    },
    [badge],
  );

  const startFirebase = async () => {
    await analytics().logEvent('app_start');
  };

  const isPromoComplete = useRef(false);

  React.useEffect(() => {
    if (!locale) return;
    Linking.getInitialURL()
      .then(handleOpenURL())
      .catch(err => console.error('An error occurred', err));

    const linkingSubscription = Linking.addEventListener('url', event => {
      handleOpenURL(event.url);
    });

    return () => {
      linkingSubscription.remove();
    };
  }, [locale]);

  useEffect(() => {
    if (!locale) return;
    handleDeferredDeepLink();
    const handleAppStateChange = nextAppState => {
      if (
        (appState.current === 'inactive' ||
          appState.current === 'background') &&
        nextAppState === 'active'
      ) {
        handleDeferredDeepLink(2);
      }
      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, [locale]);

  const isSharedPromoRequestCompleted = useRef(false);

  const handleDeferredDeepLink = async () => {
    if (isSharedPromoRequestCompleted.current) {
      isSharedPromoRequestCompleted.current = false;
      return;
    }
    const {deviceId} = await getDeviceInfo();

    isSharedPromoRequestCompleted.current = true;
    const copiedData = await readClipboardAndParse();

    const memberId = await AsyncStorage.getItem('@memberid');
    try {
      const payload = {
        codeId: copiedData?.id,
        kod: copiedData?.kod,
        hash: copiedData?.hash,
        action: memberId ? 'login' : 'start',
        memberId,
        deviceId,
      };
      console.log(
        'payload =>',
        payload,
        'url=>',
        `/${locale}/checkSharedPromo`,
      );
      const response = await mobileApi.post(
        `/${locale}/checkSharedPromo`,
        payload,
      );
      console.log('RESPONSE =>', response.data);
      if (!response?.data?.campaign?.hash) return;
      if (memberId) {
        navigationRef?.current?.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [
              {
                name: 'Settings',
                state: {
                  index: 0,
                  routes: [{name: 'SettingsCoupons'}],
                },
              },
            ],
          }),
        );
        Clipboard.setString('');
      } else {
        const campaignDetails = response?.data?.campaign;
        await AsyncStorage.setItem(
          'promoInProgress',
          JSON.stringify({...campaignDetails, deviceId}),
        );
        navigate('LoginStack', {
          screen: 'Login',
          params: {refCode: code ? code : ''},
        });
      }
    } catch (error) {
      console.log('error occured =>', error);
    }
  };

  async function handleOpenURL(url) {
    if (url !== null) {
      const route = url?.replace(/.*?:\/\//g, '');
      const routeName = route?.split('/')[0];

      if (routeName === 'register') {
        const code = route?.split('/')[1];

        setCode(code);

        navigate('LoginStack', {
          screen: 'Register',
          params: {refCode: code ? code : ''},
        });
      }
    }
  }

  React.useEffect(() => {
    startFirebase().catch(console.error);
  }, []);

  React.useEffect(() => {
    AsyncStorage.getItem('@locale')
      .then(value => {
        if (value) {
          handleLocalizationChange(value);
        } else {
          handleLocalizationChange(i18n.DEFAULT_LANGUAGE);
        }
      })
      .catch(e => {
        handleLocalizationChange(i18n.DEFAULT_LANGUAGE);
      });
    RNLocalize.addEventListener('change', handleLocalizationChange);
    return () => {
      RNLocalize.removeEventListener('change', handleLocalizationChange);
    };
  }, []);

  return (
    <>
      <UserProvider>
        <LocalizationContext.Provider value={localizationContext}>
          <NavigationContainer
            ref={navigationRef}
            onReady={() => {
              routeRef.current = navigationRef.current.getCurrentRoute().name;
              setCurrentRouteName(routeRef.current);
            }}
            onStateChange={async () => {
              const previousRouteName = routeRef.current;
              const currentRouteName =
                navigationRef.current.getCurrentRoute().name;
              if (previousRouteName !== currentRouteName) {
                await analytics().logScreenView({
                  screen_name: currentRouteName,
                  screen_class: currentRouteName,
                });
              }
              routeRef.current = currentRouteName;
              setCurrentRouteName(routeRef.current);
            }}>
            <Tab.Navigator
              screenOptions={({route}) => ({
                tabBarActiveTintColor: '#78BAE7',
                tabBarInactiveTintColor: '#fff',
                
                
                tabBarLabelStyle: {
                  fontSize: 12,
                  fontFamily: 'AndaleMono',
                  letterSpacing: -1,
                },
                
                  tabBarStyle: {
                  backgroundColor: '#000000'
                },

                tabBarIcon: ({focused, color, size}) => {
                  let iconName;
                  if (route.name === 'Home') {
                    iconName = 'home';
                  } else if (route.name === 'Orders') {
                    iconName = 'action-redo';
                  } else if (route.name === 'Basket') {
                    iconName = 'bag';
                  } else if (route.name === 'Branch') {
                    iconName = 'location-pin';
                  } else if (route.name === 'Settings') {
                    iconName = 'user';
                  }
                  return (
                    <Icon
                      name={iconName}
                      type="simple-line-icon"
                      color={color}
                      size={20}
                    />
                  );
                },
              })}>
              <Tab.Screen
                name="Home"
                children={props => (
                  <HomeNavigator {...props} handleBadge={handleBadge} />
                )}
                initialParams={{refCodee: code ? code : ''}}
                options={{
                  headerShown: false,
                  tabBarLabel: i18n.t('menu.home'),
                  popToTopOnBlur: true,
                }}
                listeners={({navigation, route}) => ({
                  tabPress: e => {
                    e.preventDefault();
                    navigation.navigate('Home', {});
                  },
                })}
              />
              <Tab.Screen
                name="Orders"
                children={() => <OrdersNavigator handleBadge={handleBadge} />}
                options={{
                  headerShown: false,
                  tabBarLabel: i18n.t('menu.orders'),
                  popToTopOnBlur: true,
                }}
                listeners={({navigation, route}) => ({
                  tabPress: e => {
                    e.preventDefault();
                    navigation.navigate('Orders', {
                      params: {menu: ''},
                    });
                  },
                })}
              />
              <Tab.Screen
                name="Basket"
                children={() => <BasketNavigator handleBadge={handleBadge} />}
                options={{
                  headerShown: false,
                  tabBarLabel: i18n.t('menu.basket'),
                  tabBarBadge: badge ? badge : '0',
                  popToTopOnBlur: true,
                }}
              />
              <Tab.Screen
                name="Branch"
                component={BranchNavigator}
                options={{
                  headerShown: false,
                  tabBarLabel: i18n.t('menu.branch'),
                  popToTopOnBlur: true,
                }}
                listeners={({navigation, route}) => ({
                  tabPress: e => {
                    navigation.navigate('Branch', {
                      params: {menu: '', ex: ''},
                    });
                  },
                })}
              />
              <Tab.Screen
                name="Settings"
                children={() => (
                  <SettingsNavigator
                    localizationChange={handleLocalizationChange}
                    handleBadge={handleBadge}
                  />
                )}
                options={{
                  headerShown: false,
                  tabBarLabel: i18n.t('menu.settings'),
                  popToTopOnBlur: true,
                }}
                listeners={({navigation, route}) => ({
                  tabPress: e => {
                    e.preventDefault();
                    navigation.dispatch(
                      CommonActions.reset({
                        index: 0,
                        routes: [
                          {
                            name: 'Settings',
                            state: {
                              routes: [{name: 'SettingsMain'}],
                            },
                          },
                        ],
                      })
                    );
                  },
                })}
              />
              <Tab.Screen
                name="LoginStack"
                component={LoginNavigator}
                options={{
                  headerShown: false,
                  popToTopOnBlur: true,
                  tabBarItemStyle: {display: 'none'},
                }}
              />
            </Tab.Navigator>
            
            {/* {routesWithoutAiAssistant?.includes(currentRouteName) ? null : (
              <WirFloatingButton
                aiAssistantId={aiAssistantId}
                style={{
                  position: 'absolute',
                  bottom: '10%',
                  right: 16,
                }}
              />
            )} */}

          </NavigationContainer>
        </LocalizationContext.Provider>
      </UserProvider>
    </>
  );
};

export default App;
