<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>SushiCo</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.dxwavetech.sushico</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>sushico</string>
			</array>
		</dict>
		<dict/>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>QR Kod kullanabilmek için kamera erişimi gerekmektedir.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>En yakın şubemize kolay ulaşım için konum bilgisine gerekmektedir.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>En yakın şubemize kolay ulaşım için konum bilgisine gerekmektedir.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>En yakın şubemize kolay ulaşım için konum bilgisine gerekmektedir.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>QR Kod kullanabilmek için kamera erişimi gerekmektedir.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>QR Kod kullanabilmek için kamera erişimi gerekmektedir.</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>This identifier will be used to deliver personalized ads to you.</string>
	<key>RCTNewArchEnabled</key>
	<true/>
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Fontisto.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Zocial.ttf</string>
		<string>Arial.ttf</string>
		<string>IBMPlexSerif-Bold.ttf</string>
		<string>IBMPlexSerif-Regular.ttf</string>
		<string>IBMPlexMono-SemiBold.ttf</string>
		<string>IBMPlexMono-Light.ttf</string>
		<string>IBMPlexSans-Light.ttf</string>
		<string>IBMPlexSans-Medium.ttf</string>
		<string>IBMPlexSans-Regular.ttf</string>
		<string>IBMPlexSans-SemiBold.ttf</string>
		<string>AndaleBold.ttf</string>
		<string>AndaleMono.ttf</string>
		<string>FrontageCondensed-Regular.otf</string>
		<string>FrontageCondensed-Bold.otf</string>
		<string>FrontageCondensed-Outline.otf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen.storyboard</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
