<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="Hi5-xH-udk">
            <objects>
                <viewController id="3TF-Ce-aww" sceneMemberID="viewController">
                    <view key="view" opaque="NO" contentMode="scaleToFill" id="1a2-e8-DgW">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="sushico_logo.png" translatesAutoresizingMaskIntoConstraints="NO" id="Lw4-2W-NrD">
                                <rect key="frame" x="30" y="0.0" width="315" height="667"/>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="ldH-1t-2GV"/>
                        <color key="backgroundColor" red="0.96862745098039216" green="0.92549019607843142" blue="0.87058823529411766" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="ldH-1t-2GV" firstAttribute="bottom" secondItem="Lw4-2W-NrD" secondAttribute="bottom" id="FdB-rz-C8y"/>
                            <constraint firstItem="Lw4-2W-NrD" firstAttribute="top" secondItem="ldH-1t-2GV" secondAttribute="top" id="WWT-z6-xAg"/>
                            <constraint firstItem="ldH-1t-2GV" firstAttribute="trailing" secondItem="Lw4-2W-NrD" secondAttribute="trailing" constant="30" id="aHK-9w-EIG"/>
                            <constraint firstItem="Lw4-2W-NrD" firstAttribute="leading" secondItem="ldH-1t-2GV" secondAttribute="leading" constant="30" id="bB0-hI-F6u"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="D4d-yp-Pvo" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-428" y="154.27286356821591"/>
        </scene>
    </scenes>
    <resources>
        <image name="sushico_logo.png" width="393" height="143"/>
    </resources>
</document>
