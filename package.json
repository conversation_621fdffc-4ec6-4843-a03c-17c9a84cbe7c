{"name": "onlineorder", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "start-reset": "react-native start --reset-cache", "test": "jest", "postinstall": "patch-package"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-clipboard/clipboard": "^1.16.3", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/viewpager": "^5.0.11", "@react-native-firebase/analytics": "^22.2.0", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-native/new-app-screen": "0.80.2", "@react-navigation/bottom-tabs": "^7.4.5", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.24", "@react-navigation/stack": "^7.4.5", "@sentry/react-native": "^6.19.0", "@types/react-native-snap-carousel": "^3.8.10", "axios": "^1.7.7", "deprecated-react-native-prop-types": "^5.0.0", "i18n-js": "^3.8.0", "lodash.memoize": "^4.1.2", "moment-timezone": "^0.6.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "19.1.0", "react-native": "0.80.2", "react-native-adjust": "^5.0.2", "react-native-asset": "^2.1.1", "react-native-camera-kit": "^15.1.0", "react-native-confirmation-code-input": "^1.0.4", "react-native-date-picker": "^5.0.12", "react-native-device-info": "^14.0.2", "react-native-element-dropdown": "^2.12.2", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "^2.28.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-localize": "^2.2.6", "react-native-permissions": "^5.4.2", "react-native-reanimated": "4.0.0", "react-native-safe-area-context": "^5.6.0", "react-native-screens": "^4.14.1", "react-native-snap-carousel": "^3.9.1", "react-native-svg": "^15.12.1", "react-native-swipe-list-view": "^3.2.9", "react-native-tabs-section-list": "^1.0.1", "react-native-tracking-transparency": "^0.1.2", "react-native-vector-icons": "^10.0.3", "react-native-version-number": "^0.3.6", "react-native-webview": "13.15.0", "react-native-wir": "^1.0.3", "react-native-worklets": "^0.4.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.1.1", "@react-native-community/cli-platform-android": "19.1.1", "@react-native-community/cli-platform-ios": "19.1.1", "@react-native/babel-preset": "0.80.2", "@react-native/eslint-config": "0.80.2", "@react-native/metro-config": "0.80.2", "@react-native/typescript-config": "0.80.2", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}