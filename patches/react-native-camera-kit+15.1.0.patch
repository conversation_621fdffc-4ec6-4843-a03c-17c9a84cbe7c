diff --git a/node_modules/react-native-camera-kit/ios/ReactNativeCameraKit/CameraView.swift b/node_modules/react-native-camera-kit/ios/ReactNativeCameraKit/CameraView.swift
index ad161e5..52950b6 100644
--- a/node_modules/react-native-camera-kit/ios/ReactNativeCameraKit/CameraView.swift
+++ b/node_modules/react-native-camera-kit/ios/ReactNativeCameraKit/CameraView.swift
@@ -91,6 +91,17 @@ public class CameraView: UIView {
         }
     }
 
+    // Use constraints for FABRIC 0.80.0
+    private func addFullSizeSubview(_ subview: UIView) {
+        subview.translatesAutoresizingMaskIntoConstraints = false
+        addSubview(subview)
+        NSLayoutConstraint.activate([
+            subview.topAnchor.constraint(equalTo: self.topAnchor),
+            subview.bottomAnchor.constraint(equalTo: self.bottomAnchor),
+            subview.leadingAnchor.constraint(equalTo: self.leadingAnchor),
+            subview.trailingAnchor.constraint(equalTo: self.trailingAnchor)
+        ])
+    }
 
     // MARK: Lifecycle
 
@@ -123,6 +134,11 @@ public class CameraView: UIView {
         scannerInterfaceView.isHidden = true
 
         addSubview(focusInterfaceView)
+
+        addFullSizeSubview(camera.previewView)
+        addFullSizeSubview(scannerInterfaceView)
+        addFullSizeSubview(focusInterfaceView)
+
         focusInterfaceView.delegate = camera
 
         handleCameraPermission()
