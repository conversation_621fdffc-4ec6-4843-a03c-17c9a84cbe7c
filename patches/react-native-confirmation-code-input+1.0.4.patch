diff --git a/node_modules/react-native-confirmation-code-input/components/ConfirmationCodeInput.js b/node_modules/react-native-confirmation-code-input/components/ConfirmationCodeInput.js
index 239c85a..8a7b524 100644
--- a/node_modules/react-native-confirmation-code-input/components/ConfirmationCodeInput.js
+++ b/node_modules/react-native-confirmation-code-input/components/ConfirmationCodeInput.js
@@ -1,6 +1,7 @@
-import React, {Component} from 'react';
+import  {Component} from 'react';
 import PropTypes from 'prop-types';
-import { View, TextInput, StyleSheet, Dimensions, ViewPropTypes } from 'react-native';
+import { View, TextInput, StyleSheet } from 'react-native';
+import { ViewPropTypes } from 'deprecated-react-native-prop-types';
 import _ from 'lodash';
 
 // if ViewPropTypes is not defined fall back to View.propType (to support RN < 0.44)
@@ -19,7 +20,7 @@ export default class ConfirmationCodeInput extends Component {
     inactiveColor: PropTypes.string,
     ignoreCase: PropTypes.bool,
     autoFocus: PropTypes.bool,
-    codeInputStyle: TextInput.propTypes.style,
+    codeInputStyle: {},
     containerStyle: viewPropTypes.style,
     onFulfill: PropTypes.func,
   };
