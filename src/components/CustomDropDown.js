import React, {memo, useCallback, useEffect} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import {Icon as RNIcon} from 'react-native-elements';
import Animated, {
  interpolate,
  measure,
  runOnUI,
  useAnimatedRef,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
const baseSpringConfig = {
  mass: 1,
  overshootClamping: false,
  restDisplacementThreshold: 0.01,
  restSpeedThreshold: 2,
  damping: 15,
  stiffness: 100,
};
const rotationSpringConfig = {
  duration: 500,
};
const CustomDropDown = ({
  children,
  HeaderComponent,
  Icon,
  headerStyle = {},
  style = {},
  iconStyle = {},
  contentStyle = {},
  isExpanded = false,
  onPress = () => {},
}) => {
  const accordionRef = useAnimatedRef();
  const accordionHeight = useSharedValue(0);
  const rotation = useSharedValue(0);

  useEffect(() => {
    if (isExpanded) {
      handleAccordionExpand();
    } else {
      handleAccordionDismiss();
    }
  }, [isExpanded]);

  const animatedContainerStyle = useAnimatedStyle(() => {
    return {height: accordionHeight.value};
  });

  const animatedIconStyle = useAnimatedStyle(() => {
    const degree = interpolate(rotation.value, [0, 1], [0, 180]);
    return {
      transform: [{rotate: `${degree}deg`}],
    };
  });

  const handleAccordionExpand = useCallback(() => {
    runOnUI(() => {
      'worklet';
      accordionHeight.value = withSpring(
        measure(accordionRef)?.height,
        baseSpringConfig,
      );
      rotation.value = withSpring(1, rotationSpringConfig);
    })();
  }, []);

  const handleAccordionDismiss = useCallback(() => {
    runOnUI(() => {
      'worklet';
      accordionHeight.value = withTiming(0, {duration: 500});
      rotation.value = withSpring(0, rotationSpringConfig);
    })();
  }, []);

  return (
    <View style={[styles.container, style]}>
      <Pressable onPress={onPress} style={[styles.toggleBtn, headerStyle]}>
        {HeaderComponent && <HeaderComponent />}
        <Animated.View style={animatedIconStyle}>
          {Icon ? (
            <Icon />
          ) : (
            <RNIcon
              name="chevron-down"
              type="feather"
              iconStyle={[styles.icon, iconStyle]}
            />
          )}
        </Animated.View>
      </Pressable>

      <Animated.View style={animatedContainerStyle}>
        <Animated.View
          ref={accordionRef}
          style={[styles.contentContainer, contentStyle]}>
          {children}
        </Animated.View>
      </Animated.View>
    </View>
  );
};
export default memo(CustomDropDown);

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: '#f1f2f1',
  },
  toggleBtn: {
    alignItems: 'center',
    flexDirection: 'row',
    padding: 10,
  },

  contentContainer: {
    backgroundColor: '#F0F0F0',
    position: 'absolute',
    width: '100%',
    gap: 8,
  },
  icon: {color: '#000', fontSize: 24},
});
