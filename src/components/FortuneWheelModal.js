import React from 'react';
import {
  Alert,
  Dimensions,
  Modal,
  Pressable,
  StyleSheet,
  View,
  Text,
  Platform,
} from 'react-native';
import {Icon} from 'react-native-elements';
import WebView from 'react-native-webview';
import {useContext, useState} from 'react';
import LocalizationContext from '../context/LocalizationContext';
import styles from '../stylesheets/styles';
import * as sc from '../stylesheets/stylesShort';
import Loading from './Loading';
import {Context as UserContext} from '../context/UserContext';
import mobileApi from '../api/mobileApi';

const FortuneWheelModal = ({isVisible, setIsVisible, onPress = () => {}}) => {
  const [isWheelSpinned, setIsWheelSpinned] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const {t, locale} = useContext(LocalizationContext);
  const {state} = useContext(UserContext);
  const source = `${mobileApi.defaults.webURL}/fortuneWheel.php?lang=${locale}&token=${state?.token}`;
  const l = t('payment');

  const handleUrlChange = event => {
    const {url} = event;
    if (!url) return;
    if (url.includes('&status=success')) {
      setIsWheelSpinned(true);
    }
  };
  return (
    <Modal transparent visible={isVisible} animationType="fade">
      <View style={stylesRecommended.backDrop}>
        <View style={stylesRecommended.centeredView}>
          {isLoading && <Loading />}
          <View
            style={{
              flex: 1,
              borderRadius: 10,
              overflow: 'hidden',
              backgroundColor: '#fbf6ef',
            }}>
            <Pressable
              onPress={() => {
                Alert.alert(
                  t('settingsMenu.Coupons.warning'),
                  t('settingsMenu.Coupons.wheelWarning'),
                  [
                    {
                      text: t('alert.yesBtn'),
                      onPress: () => setIsVisible(false),
                    },
                    {text: t('alert.noBtn'), style: 'cancel'},
                  ],
                );
              }}
              style={stylesRecommended.backButton}>
              <Icon
                name="x"
                type="feather"
                iconStyle={{color: 'white', fontSize: 24}}
              />
            </Pressable>

            {!isLoading && (
              <View style={{alignItems: 'center'}}>
                <Text
                  style={[styles.text32, {color: '#29850d', marginTop: 20}]}>
                  {l.thankyou_txt1}
                </Text>
                <Text style={[styles.text20, styles.Txt_renk1]}>
                  {t('settingsMenu.Coupons.wheelEarned')}
                </Text>
              </View>
            )}

            <WebView
              style={{flex: 1}}
              source={{
                uri: source,
              }}
              onLoadEnd={() => setIsLoading(false)}
              onLoadStart={() => setIsLoading(true)}
              onNavigationStateChange={handleUrlChange}
            />

            {isWheelSpinned && (
              <Pressable style={[sc.PrimaryButton]} onPress={onPress}>
                <Text style={[sc.PrimaryButton_text, styles.ozelfont_medium]}>
                  {t('settingsMenu.Coupons.title1')}
                </Text>
              </Pressable>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};
export default FortuneWheelModal;
const {width, height} = Dimensions.get('screen');
const BUTTON_SIZE = 36;

const stylesRecommended = StyleSheet.create({
  backDrop: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  centeredView: {
    width: width * 0.95,
    height: width * 1.62,
  },
  backButton: {
    height: BUTTON_SIZE,
    width: BUTTON_SIZE,
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 999,
    backgroundColor: 'rgba(0,0,0,.7)',
    alignItems: 'center',
    alignSelf: 'flex-end',
    justifyContent: 'center',
    borderRadius: BUTTON_SIZE / 2,
    borderWidth: 2,
    borderColor: 'white',
  },
});
