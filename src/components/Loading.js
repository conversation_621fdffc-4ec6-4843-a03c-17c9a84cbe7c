import React, { useState, useEffect, useRef } from 'react';
import { Animated, Image, View, Dimensions, Text } from 'react-native';
import LocalizationContext from '../context/LocalizationContext';
import styles from '../stylesheets/styles';

const Loading = () => {
  const { t, locale } = React.useContext(LocalizationContext);

  return (
    <View
      style={{
        backgroundColor: 'white',
        flex: 1,
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
        position: 'absolute',
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        alignSelf: 'center',

        zIndex: 1,
      }}
    >
      <View style={{ width: 160, height: 160, overflow: 'hidden' }}>
        <Animated.Image
          source={require('../../assets/img/loading.gif')}
          style={{
            width: 160,
            height: 160,
            transform: [{ translateX: 0 }],
          }}
        />
      </View>
      <Text style={styles.text22}>{t('pleaseWait')}</Text>
    </View>
  );
};

export default Loading;
