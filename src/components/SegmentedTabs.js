import React, {useEffect, useMemo, useState} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {runOnJS} from 'react-native-worklets';

const SegmentedTab = ({title, children}) => {
  return <View style={{flex: 1}}>{children}</View>;
};

const SegmentedTabWrapper = ({children}) => {
  const [activeTab, setActiveTab] = useState(0);
  const tabPositionX = useSharedValue(0);
  const [dimensions, setDimensions] = useState({
    height: undefined,
    width: undefined,
  });

  const tabs = Array.isArray(children) ? children : [children];

  const buttonWidth = useMemo(
    () => dimensions.width / tabs.length,
    [dimensions, tabs.length],
  );

  const onTabBarLayout = e => {
    const {height, width} = e.nativeEvent.layout;
    setDimensions({height, width});
  };

  const handlePress = index => {
    setActiveTab(index);
  };

  const onTabPress = index => {
    tabPositionX.value = withTiming(buttonWidth * index, {}, () => {
      runOnJS(handlePress)(index);
    });
  };

  const animatedTabPoisitonStyle = useAnimatedStyle(() => ({
    transform: [{translateX: tabPositionX.value}],
  }));

  return (
    <View
      style={{
        flex: 1,
      }}>
      <View style={[styles.container]}>
        <Animated.View
          style={[
            animatedTabPoisitonStyle,
            styles.activeTab,
            {
              height: dimensions.height - 10,
              width: buttonWidth - 10,
            },
          ]}
        />
        <View onLayout={onTabBarLayout} style={[styles.tabButtonContainer]}>
          {tabs.map((child, index) => {
            const title = child?.props?.title;
            const isSelected = activeTab === index;

            return (
              <Pressable
                onPress={() => onTabPress(index)}
                key={index}
                style={styles.tabButton}>
                <Text
                  style={[
                    styles.tabTitle,
                    isSelected && styles.activeTabTitle,
                  ]}>
                  {title}
                </Text>
              </Pressable>
            );
          })}
        </View>
      </View>

      <View style={{flex: 1}}>{tabs[activeTab]}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    paddingVertical: 5,
  },
  activeTab: {
    backgroundColor: '#000',
    position: 'absolute',
    borderRadius: 2,
    marginHorizontal: 5,
  },
  tabButtonContainer: {
    flexDirection: 'row',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 16,
  },
  tabTitle: {
    alignSelf: 'center',
    fontFamily: 'AndaleMono',
    letterSpacing: -1.5,
    fontSize: 18,
    color: '#9f00aa',
  },
  activeTabTitle: {
    color: '#fff',
  },
});

export {SegmentedTabWrapper, SegmentedTab};
