import {Platform} from 'react-native';
import VersionNumber from 'react-native-version-number';

export const routesWithoutAiAssistant = [
  'MenuList',
  'DeliveryType',
  'MenuDetail',

  'BasketMain',
  'Payment',
  'PaymentDate',
  'PaymentAddress',
  'PaymentPromo',
  'PaymentMethod',
  'ThankYou',

  'Address',
  'AddressCity',
  'AddressTown',
  'AddressDistrict',
];

export const aiAssistantId = '4484c021-d947-4baa-834f-1625034fce12';
export const sentryDsn =
  'https://<EMAIL>/4508397400031312';
export const operatingSystem = Platform.OS;
export const appVersion = VersionNumber.appVersion;
