import AsyncStorage from '@react-native-async-storage/async-storage';
import createDataContext from './createDataContext';
import mobileApi from '../api/mobileApi';
import {navigate} from '../navigationRef';
import analytics from '@react-native-firebase/analytics';
import {Adjust, AdjustEvent} from 'react-native-adjust';
import {getDeviceInfo} from '../utils';
const userReducer = (state, action) => {
  switch (action.type) {
    case 'add_error':
      return {...state, err: action.payload.err, errormsg: action.payload.msg};

    case 'login':
      return {
        ...state,
        err: action.payload.err,
        errormsg: action.payload.msg,
        isLogin: action.payload.isLogin,
        token: action.payload.token,
        memberid: action.payload.memberid,
        firstname: action.payload.firstname,
        redirect: action.payload.redirect,
      };
    case 'login_check':
      return {
        ...state,
        err: '',
        firstname: action.payload.firstname,
        isLogin: action.payload.isLogin,
        token: action.payload.token,
        memberid: action.payload.memberid,
      };
    case 'save_basket':
      return {
        ...state,
        err: '',
        totalCount: action.payload.totalCount,
        totalAmount: action.payload.totalAmount,
      };
    case 'get_badge':
      return {
        ...state,
        totalCount: action.payload.totalCount,
        totalAmount: action.payload.totalAmount,
      };
    case 'save_address':
      return {...state, err: action.payload.err, errormsg: action.payload.msg};
    case 'save_orders':
      return {...state, err: action.payload.err, errormsg: action.payload.msg};
    case 'register':
      return {
        ...state,
        err: action.payload.err,
        errormsg: action.payload.msg,
        isLogin: action.payload.isLogin,
        token: action.payload.token,
        memberid: action.payload.memberid,
        firstname: action.payload.firstname,
      };
    case 'forgot':
      return {
        ...state,
        err: action.payload.err,
        errormsg: action.payload.msg,
        isLogin: false,
        code: action.payload.code,
        memberid: action.payload.memberid,
        email: action.payload.email,
      };
    case 'change_password':
      return {
        ...state,
        err: action.payload.err,
        errormsg: action.payload.msg,
        isLogin: false,
        code: '',
        memberid: '',
        email: '',
      };
    case 'user_save':
      return {...state, err: action.payload.err, errormsg: action.payload.msg};
    case 'logout':
      return {
        ...state,
        err: '',
        errormsg: '',
        isLogin: '',
        memberid: '',
        firstname: '',
        token: '',
        totalCount: '',
        totalAmount: '',
      };
    case 'send_activation':
      return {
        ...state,
        err: '',
        smsCode: action.payload.smsCode,
      };
    case 'clear_error_message':
      return {...state, err: null, errormsg: null};
    case 'update_activation':
      return {...state, err: ''};

    default:
      return state;
  }
};

const saveOrders = dispatch => async request => {
  const restaurant = request.data.restaurant;
  const memberId = request.data.memberId;
  const payment = request.data.payment;
  const token = request.data.token;
  const addressId = request.data.addressId;
  const amount = request.data.amount;
  const servistipi = request.data.servistipi;
  const locale = request.data.locale;
  const {deviceModel, appVersion} = await getDeviceInfo();

  if (!restaurant || !memberId || !token || !amount) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.systemError},
    });

    return {err: 1, msg: request.alert.systemError};
  } else if (!addressId && servistipi === 2) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.addressNull},
    });

    return {err: 1, msg: request.alert.addressNull};
  } else if (!payment) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.paymentNull},
    });
    return {err: 1, msg: request.alert.paymentNull};
  }
  try {
    const response = await mobileApi.post(`/${locale}/orderSave`, {
      ...request.data,
      version: appVersion,
      deviceInfo: deviceModel,
    });
    if (response.data.err === 0) {
      dispatch({
        type: 'save_orders',
        payload: {err: '', msg: ''},
      });
      return {msg: '', data: response.data, orderId: response.data.orderId};
    } else {
      // server error msg
      dispatch({
        type: 'add_error',
        payload: {err: 1, msg: response.data.msg},
      });
      return {err: 1, msg: response.data.msg};
    }
  } catch (err) {
    console.log(err);
    return {err: 1, msg: request.alert.systemError};
  }
};

const saveBasket = dispatch => async request => {
  const action = request.data?.action;
  const id = request.data?.id;
  const menu = request.data?.menu;
  const inx = request.data?.inx;
  const count = request.data?.count;
  const reset = request.data?.reset;
  const title = request.data?.title;
  const price = request.data?.price;
  const optionTotal = request.data?.optionTotal;
  const promoCode = request.data?.promoCode;
  const promoId = request.data?.promoId;
  const promoProductName = request.data?.promoProductName;
  const desc = request.data?.desc;
  const category = request.data?.category;
  const basketTime = new Date();
  const is_recommended = request.data?.is_recommended ? 1 : 0;
  const is_repeat = request.data?.is_repeat ? 1 : 0;

  let ga_count = 1;
  const basketItem = {
    id,
    title,
    price,
    count,
    menu,
    desc,
    category,
    optionTotal,
    promoCode,
    promoId,
    basketTime: basketTime,
    promoProductName,
    sos: request.data.sos,
    is_recommended,
    is_repeat,
  };

  await AsyncStorage.getItem('@basket')
    .then(response => {
      const r = response ? JSON.parse(response) : [];
      const index = r.findIndex(obj => obj.id === id);
      const promoIndex = r.findIndex(obj => obj.promoCode === promoCode);
      if (r.length == 0) {
        AsyncStorage.multiRemove([
          '@promoCode',
          '@promoId',
          '@promoPrice',
          '@promoProductName',
        ]);
      }
      if (action === 'add') {
        if (promoIndex > -1 && promoCode) {
          return;
        }
        if (index === -1 || menu.length > 0) {
          r.push(basketItem);
          AsyncStorage.setItem('@basket', JSON.stringify(r));
        } else {
          if (r[index].promoCode === '' && promoCode === '') {
            const currentCount = r[index].count;
            r[index].count = reset === 1 ? count : currentCount + 1;
          } else {
            r.push(basketItem);
          }
          AsyncStorage.setItem('@basket', JSON.stringify(r));
        }
      } else if (action === 'del') {
        // r.splice(inx, 1);
        r.splice(index, 1);
        AsyncStorage.setItem('@basket', JSON.stringify(r));
      } else {
        const currentCount = r[inx].count;
        const currentPromoId = r[inx].promoId;
        if (action == 'dec') {
          const fdatas = {
            items: [
              {
                item_id: id,
                item_name: title,
                item_category: category,
                item_brand: 'Sushico',
                quantity: 1,
                item_variant: desc,
                price: parseFloat(price),
              },
            ],
            currency: 'TRY',
            value: parseFloat(1 * price),
          };
          analytics().logEvent('remove_from_cart', fdatas);

          if (currentCount === 1) {
            r.splice(inx, 1);
            if (currentPromoId) {
              AsyncStorage.multiRemove([
                '@promoCode',
                '@promoId',
                '@promoPrice',
                '@promoProductName',
              ]);
            }
          } else {
            r[inx].count = currentCount - 1;
          }
        } else {
          r[inx].count = currentCount + 1;
        }
        AsyncStorage.setItem('@basket', JSON.stringify(r));
      }
      let totalAmount = 0;
      if (r.length > 0) {
        r.map(i => {
          totalAmount +=
            i.count * (parseFloat(i.optionTotal) + parseFloat(i.price));
        });
      } else {
        totalAmount = 0;
      }
      if (action === 'add' || action === 'inc') {
        ga_count = reset === 1 ? count : 1;

        const fdatas = {
          items: [
            {
              item_id: id,
              item_name: request.data.title,
              item_category: category,
              item_brand: 'Sushico',
              quantity: ga_count,
              item_variant: desc,
              price: parseFloat(price),
            },
          ],
          currency: 'TRY',
          value: parseFloat(ga_count * price),
        };
        analytics().logEvent('add_to_cart', fdatas);

        var adjustEvent = new AdjustEvent('nc35o9');
        Adjust.trackEvent(adjustEvent);
      }
      //console.log(totalAmount);
      dispatch({
        type: 'save_basket',
        payload: {totalCount: r.length, totalAmount: totalAmount},
      });
    })
    .catch(e => {
      // console.log(e);
      dispatch({type: 'save_basket', payload: {totalCount: 0, totalAmount: 0}});
    });
};

const setBranch =
  dispatch =>
  async (
    branchId,
    branchName,
    service,
    addressid,
    regionLimit,
    deliveryPrice,
    servisTime,
  ) => {
    const branchTime = new Date();
    branchId
      ? await AsyncStorage.multiSet([
          ['@branchId', branchId.toString()],
          ['@branchName', branchName],
          ['@service', service],
          ['@addressid', addressid],
          ['@regionLimit', regionLimit],
          ['@deliveryPrice', deliveryPrice],
          ['@servisTime', servisTime],
          ['@branchTime', branchTime.toString()],
        ])
      : await AsyncStorage.multiRemove([
          '@branchId',
          '@branchName',
          '@service',
          '@addressid',
          '@basket',
          '@regionLimit',
          '@deliveryPrice',
          '@servisTime',
          '@branchTime',
        ]);
    await AsyncStorage.removeItem('@isRecommendedProductsShown');
    dispatch({type: 'save_basket', payload: {totalCount: 0, totalAmount: 0}});
    return Promise.resolve({err: '', msg: ''});
  };

const loginCheck = dispatch => async () => {
  await AsyncStorage.multiGet(['@token', '@userdata'])
    .then(response => {
      const userData = response[1][1] ? JSON.parse(response[1][1]) : null;
      if (userData) {
        dispatch({
          type: 'login_check',
          payload: {
            isLogin: true,
            firstname: userData.firstname,
            token: userData.token,
            memberid: userData.memberID,
          },
        });
      } else {
        dispatch({
          type: 'login_check',
          payload: {isLogin: false},
        });
      }
    })
    .catch(e => {
      dispatch({type: 'login_check', payload: {isLogin: false}});
    });
};

const clearErrorMessage = dispatch => () => {
  dispatch({type: 'clear_error_message'});
};

const login = dispatch => async request => {
  const email = request.data.email;
  const password = request.data.password;
  const locale = request.data.locale;
  const basketreset = request.data.basketreset;
  const fast = request.data.fast;
  const {appVersion, deviceModel, operatingSystem} = await getDeviceInfo();

  if (!email || !password || !locale) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: fast === -1 ? request.alert.nullMsg : ''},
    });
    return {err: 1, msg: fast === -1 ? request.alert.nullMsg : ''};
  }
  if (email) {
    let regEmail =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!regEmail.test(email)) {
      dispatch({
        type: 'add_error',
        payload: {err: 1, msg: fast === -1 ? request.alert.emailNotValid : ''},
      });
      return {err: 1, msg: fast === -1 ? request.alert.emailNotValid : ''};
    }
  }

  try {
    const response = await mobileApi.post(`/${locale}/userLogin`, {
      ...request.data,
      version: appVersion,
      deviceInfo: deviceModel,
      source: operatingSystem,
    });

    if (response.data.err == 1) {
      // server error msg
      AsyncStorage.multiRemove([
        '@userdata',
        '@token',
        '@memberid',
        '@phone',
        '@vip',
        '@birthday',
        '@isLogin',
      ]);
      dispatch({
        type: 'add_error',
        payload: {err: 1, msg: fast === -1 ? response.data.msg : ''},
      });
      return {err: 1, msg: fast === -1 ? response.data.msg : ''};
    } else {
      if (basketreset === 1) {
        await AsyncStorage.clear();
      }
      // console.log(response.data);
      // server status ok
      await AsyncStorage.multiSet([
        ['@userdata', JSON.stringify(response.data)],
        ['@token', response.data.token],
        ['@memberid', response.data.memberID],
        ['@phone', response.data.phone],
        ['@vip', response.data.vip.toString()],
        ['@birthday', response.data.birthday ? response.data.birthday : ''],
        ['@email', email],
        ['@password', password],
        ['@isLogin', '1'],
      ]);
      if (fast === -1) {
        analytics().setUserProperty(
          'user_member_status',
          response.data.loyalty,
        );
        analytics().setUserProperty(
          'user_id_dimension',
          response.data.memberID,
        );
        analytics().setUserProperty(
          'user_email_permission',
          response.data.emsg == 1 ? 'true' : 'false',
        );
        analytics().setUserProperty(
          'user_sms_permission',
          response.data.sms == 1 ? 'true' : 'false',
        );
        analytics().setUserProperty(
          'user_point',
          response.data.point.toString(),
        );
        analytics().setUserProperty('user_last_session', response.data.session);
        analytics().setUserProperty(
          'user_last_purchase',
          response.data.purchase,
        );
        analytics().setUserProperty(
          'user_total_revenue',
          response.data.revenue.toString(),
        );
        analytics().setUserProperty(
          'user_total_purchase',
          response.data.totalpurchase.toString(),
        );
        analytics().setUserProperty(
          'user_register_date',
          response.data.register,
        );
        analytics().setUserProperty('customer_type', 'Registered');
        analytics().setUserId(response.data.memberID);

        await analytics().logEvent('login', {});

        var adjustEvent = new AdjustEvent('mw2ny2');
        Adjust.trackEvent(adjustEvent);
      }

      dispatch({
        type: 'login',
        payload: {
          err: '',
          msg: '',
          phone: response.data.phone,
          isLogin: true,
          token: response.data.token,
          vip: response.data.vip.toString(),
          memberid: response.data.memberID,
          firstname: response.data.firstname,
          redirect: 0,
        },
      });
      return {err: 0, msg: '', activation: response.data.sms_activation};
    }
  } catch (err) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.systemError},
    });
    console.log('cat', err);
    return {err: 1, msg: 'burada'};
  }
};

const register = dispatch => async request => {
  const {
    firstname,
    lastname,
    email,
    phone,
    password,
    repassword,
    agreement,
    locale,
    location,
  } = request.data;
  const {appVersion, deviceModel, operatingSystem} = await getDeviceInfo();
  if (!firstname || !lastname || !email || !phone || !password || !repassword) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.nullMsg},
    });
    return;
  } else if (password != repassword) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.passwordMatch},
    });
    return;
  } else if (!agreement) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.agreementErr},
    });
    return;
  }

  if (email) {
    let regEmail =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    if (!regEmail.test(email)) {
      dispatch({
        type: 'add_error',
        payload: {err: 1, msg: request.alert.emailNotValid},
      });
      return;
    }
  }

  try {
    const response = await mobileApi.post(`/${locale}/userSave`, {
      ...request.data,
      source: operatingSystem,
      deviceInfo: deviceModel,
      version: appVersion,
    });

    if (response.data.err == 1) {
      // server error msg
      dispatch({
        type: 'add_error',
        payload: {err: 1, msg: response.data.msg},
      });
    } else {
      await AsyncStorage.multiSet([
        ['@userdata', JSON.stringify(request.data)],
        ['@token', response.data.token],
        ['@memberid', response.data.memberid],
        ['@phone', response.data.phone],
        ['@email', email],
        ['@password', password],
        ['@isLogin', '1'],
      ]);
      //await firebase.analytics().logSignUp({method: ''});
      analytics().setUserProperty('user_id_dimension', response.data.memberid);
      analytics().setUserProperty('user_member_status', 'Ninja');
      analytics().setUserProperty(
        'user_email_permission',
        request.data.emsg == 1 ? 'true' : 'false',
      );
      analytics().setUserProperty(
        'user_sms_permission',
        request.data.sms == 1 ? 'true' : 'false',
      );
      analytics().setUserProperty('user_point', '0');
      analytics().setUserProperty('user_register_date', response.data.register);
      analytics().setUserProperty('customer_type', 'Registered');

      analytics().setUserId(response.data.memberid);

      await analytics().logEvent('sign_up', {});
      var adjustEvent = new AdjustEvent('42c2ne');
      Adjust.trackEvent(adjustEvent);

      dispatch({
        type: 'register',
        payload: {
          err: '',
          msg: '',
          phone: response.data.phone,
          isLogin: true,
          token: response.data.token,
          mmeberid: response.data.memberid,
        },
      });
      navigate('Activate', {location});
    }
  } catch (err) {
    console.log(err);
  }
};

const changepassword = dispatch => async request => {
  if (
    !request.data.password ||
    !request.data.repassword ||
    !request.data.memberid
  ) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.nullMsg},
    });
    return {err: 1, msg: alert.nullMsg};
  } else if (request.data.password != request.data.repassword) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.passwordMatch},
    });
    return {err: 1, msg: request.alert.passwordMatch};
  }
  try {
    const response = await mobileApi.post(
      `/${request.locale}/changePassword`,
      request.data,
    );
    //console.log(response.data);
    if (response.data.err == 1) {
      // server error msg
      dispatch({
        type: 'add_error',
        payload: {err: 1, msg: response.data.msg},
      });
      return {err: 1, msg: response.data.msg};
    } else {
      dispatch({
        type: 'change_password',
        payload: {
          err: '',
          msg: '',
        },
      });
      return {err: 0, msg: response.data.msg};
    }
  } catch (err) {
    //console.log(err);
  }
};

const forgot = dispatch => async request => {
  if (!request.phone) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: alert.phoneNotValid},
    });
    return {err: 1, msg: alert.phoneNotValid};
  }

  try {
    // const response = await mobileApi.get(`/${request.locale}/userForgot?email=${request.email}`);
    const response = await mobileApi.get(
      `/${request.locale}/userForgotPhone?phone=${request.phone}&location=${request.location}`,
    );

    //     console.log(response.data);
    // console.log( `/${request.locale}/userForgotPhone?phone=${request.phone}&location=${request.location}`);
    if (response.data.err == 1) {
      // server error msg
      dispatch({
        type: 'add_error',
        payload: {err: 1, msg: response.data.msg},
      });
      return {err: 1, msg: response.data.msg};
    } else {
      dispatch({
        type: 'forgot',
        payload: {
          err: '',
          msg: '',
          code: response.data.code,
          memberid: response.data.memberid,
          email: request.email,
        },
      });
      return {err: 0, msg: response.data.msg};
    }
  } catch (err) {
    console.log(err);
  }
};

const usersave = dispatch => async request => {
  const firstname = request.data.firstname;
  const lastname = request.data.lastname;
  const email = request.data.email;
  const phone = request.data.phone;
  const password = request.data.password;
  const token = request.data.token;
  const memberId = request.data.memberId;
  const locale = request.data.locale;
  if (!firstname || !lastname || !email || !phone || !password) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.nullMsg},
    });
    return {err: 1};
  } else if (!token || !memberId) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.systemError},
    });
    return {err: 1};
  }

  if (email) {
    let regEmail =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

    if (!regEmail.test(email)) {
      dispatch({
        type: 'add_error',
        payload: {err: 1, msg: request.alert.emailNotValid},
      });
      return {err: 1};
    }
  }

  try {
    const response = await mobileApi.post(`/${locale}/userSave`, request.data);
    if (response.data.err == 1) {
      // server error msg
      dispatch({
        type: 'add_error',
        payload: {err: 1, msg: response.data.msg},
      });
      return {err: 1};
    } else {
      await AsyncStorage.multiSet([
        ['@userdata', JSON.stringify(request.data)],
        ['@token', response.data.token],
        ['@memberid', response.data.memberid],
        ['@phone', response.data.phone],
        ['@email', email],
        ['@password', password],
        ['@isLogin', '1'],
      ]);
      dispatch({
        type: 'user_save',
        payload: {err: '', msg: ''},
      });
      return {err: ''};
    }
  } catch (err) {
    //console.log(err);
  }
};

const saveAddress = dispatch => async request => {
  const {
    name,
    surname,
    phone,
    label: addressname,
    region,
    city,
    district,
    token,
    detail,
    locale,
  } = request.data;

  if (
    !name ||
    !surname ||
    !phone ||
    !addressname ||
    !region ||
    !district ||
    !city ||
    !detail
  ) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.nullMsg},
    });

    return Promise.reject({err: 1});
  } else if (!token) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.tokenNull},
    });
    return Promise.reject({err: 1});
  }

  try {
    const response = await mobileApi.post(
      `/${locale}/userAddress`,
      request.data,
    );
    if (response.data.err == 1) {
      dispatch({
        type: 'add_error',
        payload: {err: 1, msg: response.data.msg},
      });
      return Promise.reject({err: 1});
    } else {
      dispatch({
        type: 'save_address',
        payload: {err: '', msg: ''},
      });
      return Promise.resolve({err: ''});
    }
  } catch (err) {
    return Promise.reject({err: 1});
  }
};

const sendActivation = dispatch => async request => {
  const phone = request.data.phone;
  const location = request.data.location;
  const locale = request.data.locale;
  if (!phone || !location || !locale) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.systemError},
    });
    return {err: 1, msg: request.alert.systemError};
  }

  try {
    const response = await mobileApi.post(
      `/${locale}/sendActivationV2`,
      request.data,
    );
    console.log(response.data);
    if (response.data.err == 1) {
      dispatch({
        type: 'add_error',
        payload: {err: 1, msg: response.data.msg},
      });
      return {err: 1, msg: response.data.msg};
    } else {
      dispatch({
        type: 'send_activation',
        payload: {err: 0, smsCode: response.data.smsCode},
      });
      return {err: 0, msg: '', smsCode: response.data.smsCode};
    }
  } catch (err) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.systemError},
    });
    return {err: 1, msg: request.alert.systemError};
  }
};

const updateActivation = dispatch => async request => {
  const memberId = request.data.memberId;
  const token = request.data.token;
  const locale = request.data.locale;
  if (!memberId || !token || !locale) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.systemError},
    });
    return;
  }

  try {
    const response = await mobileApi.post(
      `/${locale}/updateActivation`,
      request.data,
    );
    if (response.data.err == 1) {
      dispatch({
        type: 'add_error',
        payload: {err: 1, msg: response.data.msg},
      });
      return;
    } else {
      dispatch({
        type: 'update_activation',
        payload: {err: 0},
      });
      var adjustEvent = new AdjustEvent('39ujnc');
      Adjust.trackEvent(adjustEvent);

      analytics().setUserProperty('user_id_dimension', memberId);
      analytics().setUserProperty('customer_type', 'Registered');
      analytics().setUserProperty('user_member_status', 'Ninja');
      analytics().setUserProperty('user_point', '0');
      analytics().setUserId(memberId);

      await analytics().logEvent('user_register_success', {});
      return response.data;
    }
  } catch (err) {
    dispatch({
      type: 'add_error',
      payload: {err: 1, msg: request.alert.systemError},
    });
    return;
  }
};

const getBadge = dispatch => async () => {
  await AsyncStorage.getItem('@basket')
    .then(response => {
      const r = response ? JSON.parse(response) : [];
      let totalAmount = 0;
      if (r.length > 0) {
        r.map(i => {
          totalAmount +=
            i.count * (parseFloat(i.optionTotal) + parseFloat(i.price));
        });
      } else {
        totalAmount = 0;
      }
      dispatch({
        type: 'get_badge',
        payload: {totalCount: r.length, totalAmount},
      });
    })
    .catch(e => {
      //console.log(e);
      dispatch({type: 'get_badge', payload: {totalCount: 0, totalAmount: 0}});
    });
};

const logout = dispatch => async () => {
  await AsyncStorage.multiGet(['@userdata', '@token', '@memberid'])
    .then(r => {
      analytics().setUserProperty('user_id_dimension', r[2][1]);
      analytics().setUserId(r[2][1]);

      analytics().logEvent('logout', {});
    })
    .catch(err => {
      //console.log(err);
    });

  await AsyncStorage.clear();
  dispatch({type: 'logout'});
};

export const {Provider, Context} = createDataContext(
  userReducer,
  {
    login,
    register,
    logout,
    sendActivation,
    updateActivation,
    clearErrorMessage,
    loginCheck,
    saveBasket,
    saveAddress,
    setBranch,
    saveOrders,
    usersave,
    forgot,
    changepassword,
    getBadge,
  },
  {
    token: null,
    memberid: null,
    err: '',
    errormsg: '',
    isLogin: '',
    totalCount: '',
    branchId: '',
    branchName: '',
    service: '',
    orderDate: '',
    deliveryType: '',
    payment: '',
    badge: '5',
    badgeCount: '',
    fcmToken: '',
  },
);
