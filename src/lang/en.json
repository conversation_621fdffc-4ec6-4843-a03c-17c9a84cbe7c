{"hello": "Hello!", "settingsMenu": {"PreviousOrder": {"name": "SettingsPreviousOrder", "title": "Orders", "icon": "reload", "page": "PreviousOrderScreen", "login": "1"}, "Info": {"name": "SettingsProfileInfo", "title": "Membership Info", "icon": "user", "page": "ProfileInfoScreen", "login": "1"}, "Address": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Addresses", "icon": "home", "page": "AddressScreen", "login": "1"}, "Coupons": {"name": "SettingsCoupons", "title": "Opportunity Codes", "title1": "My Opportunity Codes", "icon": "options", "page": "CouponsScreen", "login": "1", "showWheel": "Show Wheel", "warning": "Warning", "wheelWarning": "Are you sure you want to close the fortune wheel?", "wheelEarned": "You've earned the right to spin the wheel"}, "Point": {"name": "SettingsPoint", "title": "SushiCo Points", "icon": "drop", "page": "PointScreen", "login": "1"}, "Notifications": {"name": "SettingsNotifications", "title": "Notifications", "icon": "drop", "page": "NotificationsScreen", "login": "1"}, "Level": {"name": "SettingsLevel", "title": "Membership Level", "icon": "badge", "page": "LevelScreen", "login": "1"}, "Favorite": {"name": "SettingsFavorite", "title": "Favorites", "icon": "heart", "page": "FavoriteScreen", "login": "1"}, "Referer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Share & Win", "icon": "user-follow", "page": "RefererScreen", "login": "1"}, "Contact": {"name": "SettingsContact", "title": "Contact Form", "icon": "note", "page": "ContactScreen", "function": "contact"}, "Support": {"name": "Support", "title": "Call Center", "icon": "phone", "page": "1"}, "Support2": {"name": "Support", "title": "Call Center (Kıbrıs)", "icon": "phone", "page": "2"}, "Agreement": {"name": "SettingsAgreement", "title": "Agreements", "icon": "doc", "page": "AgreementScreen"}, "Security": {"name": "SettingsSecurity", "title": "Information Security Policy", "icon": "doc", "page": "SecurityScreen"}, "Language": {"name": "SettingsLanguage", "title": "Select Language", "icon": "globe", "page": "LanguageScreen"}}, "ordersMenu": {"City": {"name": "OrdersSelectCity", "title": "Select City", "page": "CityScreen"}, "Town": {"name": "OrdersSelectTown", "title": "Select Town", "page": "TownScreen"}, "District": {"name": "OrdersSelectDistrict", "title": "Select District", "page": "DistrictScreen"}, "Branch": {"name": "OrdersSelectBranch", "title": "Select Branch", "page": "BranchScreen"}}, "menu": {"home": "Homepage", "settings": "Profile", "orders": "Order", "basket": "<PERSON><PERSON>", "branch": "Branches"}, "login": {"Turkiye": "TÜRKİYE", "KKTC": "KKTC", "version": "Version", "HelloTittle": "Hello ", "Welcome": "Welcome!", "membertxt": "Member Level", "pointtxt": "SushiCo Points", "WelcomeNoLogin": "Welcome to SushiCo ", "WelcomeSub": "Chinese - Japanese - Thai", "loginTitle": "<PERSON><PERSON>", "forgotTitle": "Password Reminder", "registerTitle": "Sign In", "logout": "Logout > ", "login": "Login > ", "activateTitle": "Membership Activation", "addressAddTitle": "Add Address", "addressEditTitle": "Edit Address", "changePasswordTitle": "Update Password", "fullname": "Name Surname", "firstname": "Name", "lastname": "Surname", "email": "E-mail", "refcode": "Referer Code", "password": "Password", "repassword": "Password (Repeat)", "phone": "Phone", "birthday": "Birthday", "location": "Location", "addressDetail": "Address", "addressName": "Address Description", "selectDistrict": "Select Zone", "addedAddress": "Your address information has been added.", "updatedAddress": "Your address information has been updated.", "congratulations": "CONGRATULATIONS", "updateProfiles": "Your membership information has been updated.", "selectTown": "Select Town", "selectCity": "Select City", "addressDesc": "Address Direction", "agreementText": "I have read and approved the \nmembership agreement and TPDPL text", "forgotText1": "Forgot my password", "registerText1": "Don't have an account?", "loginText1": "Do you have an account?", "registerText2": "Sign Up >", "forgotText2": "Sign In >", "saniye": "sec", "activateText1": "Activation code not received?", "activateText2": "Send Again >", "activateText4": "Edit Information >", "activateText3": "I didn't receive a code on my e-mail address.", "activateText5": "I didn't receive a code on my phone number.", "campaign": "For Promotions and Campaigns; ", "smsAccept": "I give permission to send SMS ", "emsgAccept": "I give permission to send E-mail ", "activationSms": "\n enter the code sent to your phone.", "forgotPasswordTitle": "Enter the code sent to your e-mail address.", "basketempty": "There are no items in your cart yet.", "nologinbasketempty": "To place an order, you must first login.", "orders": "Order", "nopointinfo": "You don't have any registered point information yet. Your point will be add when your order is confirmed.", "totalpoint": "Your Total Point", "error": "Your transaction could not be performed.", "phoneFormat": "Enter (5xxxxxxxxx) format", "remove": "Delete My Account", "removeConfirm": "Please enter your password for transaction confirmation", "removeText1": "You will not be able to become a member again with the phone number of this membership.", "removeText2": "ÜDo you approve the deletion of your membership information?", "removeBtn1": "I APPROVE", "removeBtn2": "Cancel"}, "payment": {"msText": "I approve the Distance Sales Agreement.", "deliveryText": "Delivery Service", "pickupText": "Pickup Service", "pickup": "Pickup", "thankyouTitle": "Thanks", "notes": "Your Order Note", "sauce": "I don't want <PERSON><PERSON>", "set": "I don't want a Service Set (chopstick, napkin, wet wipes, toothpick)", "deliveryaddress": "Delivery Address", "deliverytime": "Delivery Time", "delivery": "Delivery", "servicetype": "Service Type", "promocode": "Opportunity Code", "promocodes": "Opportunity Codes", "usepoint": "Points Usage", "usepoint_txt1": "you have points.", "usepoint_txt2": "Enter the score you want to use.", "usepoint_txt3": "You can use up to half of the cart amount.", "paymentmethod": "Payment Method", "ordernote": "Order Note", "maxchrc": "Up to 150 characters", "warning": "NOTICE", "alert_txt1": "You can use up to half of the cart amount.", "alert_txt2": "You canceled the payment transaction. You can continue with a new payment method.", "alert_txt3": "An error occurred during the connection. Please try again.", "alert_txt4": "The payment transaction could not be performed. Please check your information or choose a different payment method.", "modal_text1": "Order Branch", "modal_text2": "Delivery Type", "ok": "OK", "futuredate": "Future Order", "now": "Now", "deliverytime_txt1": "You can choose a date between 12:00 and 20:00, at the earliest 3 hours and 3 days later.", "deliverytime_txt2": "You can choose a time between 12:00 and 22:00. Tap this field to set a delivery time.", "promo_txt": "A defined opportunity code couldn't be found.", "thankyou_txt0": "Your order has been created.", "thankyou_txt1": "Enjoy your meal!", "thankyou_txt2": "You can track your order from the homepage.", "thankyou_txt3": "Evaluate your order in the  \n'Orders' \nsection and earn points.", "delivery_txt1": "You can create your order by choosing our branch that suits your address. ", "pickup_txt1": "Please select the appropriate branch for your PICKUP order.", "branchlist": "Our branches where you can order.", "nobranchlist": "We do not have a branch that serves the district you have chosen. ", "servicetime": "Delivery Time ", "workhour": "Working Hour", "dk": "min ", "minamount": "Minimum Order Amount ", "minamountshort": "<PERSON><PERSON>", "adres_txt": "Your registered address could not be found. \nPlease add an address to place an order.", "gecici_pakete_kapali_txt": "This branch is temporarily closed to package service. ", "outofservice1": "Only during branch working hours (", "outofservice2": ") you can order. "}, "branch": {"mainTitle": "Our Branches", "promoTitle": "Branch Promotions", "districtTitle": "Service Areas", "menubranch": "Please select branch whose menu you want to see.", "menu": [{"id": 1, "title": "Phone", "lefticon": "phone", "righticon": "angle-right", "desc": "1", "detail": "BranchPromo", "url": ""}, {"id": 2, "title": "Working Hours", "lefticon": "clock", "righticon": "angle-right", "desc": "2", "detail": "", "url": ""}, {"id": 4, "title": "Branch Promotions", "lefticon": "event", "righticon": "angle-right", "desc": "", "detail": "BranchPromo", "url": ""}, {"id": 5, "title": "Service Areas", "lefticon": "map", "righticon": "angle-right", "desc": "", "detail": "BranchDistricts", "url": ""}, {"id": 6, "title": "<PERSON><PERSON>", "lefticon": "book-open", "righticon": "angle-right", "desc": "", "detail": "BranchPromo", "url": ""}, {"id": 7, "title": "Pickup Service", "lefticon": "target", "righticon": "check", "righticon2": "times", "desc": "3", "detail": "", "url": ""}, {"id": 8, "title": "Payment Methods", "lefticon": "wallet", "righticon": "angle-right", "desc": "4", "detail": "", "url": ""}]}, "basket": {"campaign": {"campaignInfo": "Campaign Information", "campaignTitle": "<PERSON><PERSON><PERSON> Ticket for the Japan Tale!", "backToBasket": "Back to Basket"}, "recommendedProducts": {"alert": "Please complete your selections related to '%{productName}'"}}, "profile": {"delivery": "Delivery Order", "table": "Table Order", "cdelivery": "Special for You", "ctable": "General Codes", "csave": "Saved Codes", "table_btn": "TABLE SERVICE", "package_btn": "PACKAGE ORDER", "all_btn": "TABLE & PACKAGE ORDER", "use_btn": "USED", "sgt": "Expiry Date", "discount": "Discount", "gift": "gift", "info_text": "Scan the QR code on the ticket while making a payment at our branch to take advantage of the valid offer. Apply your defined code in the 'Use offer code' section, or enter the code you have in the relevant field.", "pvcode": "The special offer code defined for you could not be found", "codedesc": "Save Opportunity Code", "ccode": "Opportunity Code", "copy_btn": "Copy", "delete_btn": "Delete", "info_save_text": "You can add SushiCo Opportunity Codes from here", "kodtip0": "Valid on Package or Table", "kodtip1": "Only valid on Table order", "kodtip-1": "Only valid on Package order"}, "button": {"register": "REGISTER", "close": "CLOSE", "login": "LOGIN", "send": "SEND", "accept": "CONFIRM", "paymentPoint": "USE POINTS", "winPoint": "EARN POINTS", "usePromo": "USE PROMO CODE", "save": "SAVE", "ordernow": "ORDER", "update": "UPDATE", "check": "CHECK IT", "loginreg": "SIGN UP / LOGIN", "orderStatus": "Order Status", "review": "Evaluate", "reorder": "Repeat Order", "edit": "Edit", "delete": "Delete", "complete": "Complete Order", "confirm": "Confirm", "addbasket": "Add to Cart", "acceptBasket": "Confirm <PERSON>", "rateonehour": "The order evaluation form will be active one hour after your order time.", "detail": "Detail ", "use": "Apply", "cancel": "Cancel"}, "anasayfa": {"qr": "QR Code", "scan": "<PERSON><PERSON>", "sushico": "SushiCo", "puanim": "Points", "menu1": "ME", "menu2": "NU", "siparis": "PLACE", "ver": "ORDER", "sushico1": "LOYALTY", "club": "PROGRAM", "restoranlar": "RESTAURANTS", "kamp1": "CAM-", "kamp2": "PA", "kamp3": "INGS", "guven1": "TRUST", "guven2": "this", "guven3": "Flavour", "yeniler1": "SEASON OF THE NEW", "yeniler2": "SushiCo menu has been renewed. We are waiting for you to experience the Far East.", "bizimle": "WOULD YOU LIKE TO", "calismak": "WORK WITH US?", "ayin1": "Menu of the ", "ayin2": "MONTH", "paylas": "Share &", "kazan": "WIN!"}, "alert": {"errorTitle": "Sorry", "successTitle": "Successful", "cancelBtn": "CANCEL", "okBtn": "OK", "yesBtn": "YES", "noBtn": "NO", "retry": "TRY AGAIN", "systemError": "An error occurred during the operation. Please try again.", "nullMsg": "Please fill in all fields.", "passwordMatch": "Password and repeat password are not the same.", "agreementErr": "Confirm the membership agreement and TPDPL text.", "emailNotValid": "Please enter a valid e-mail address.", "passwordRequirement": {"char": "At least 1 letter", "specialChar": "At least 1 special character (!@#$...)", "number": "At least 1 number", "minLength": "Minimum length of 6 characters"}, "passwordRequirementsNotMet": "Your password does not meet the requirements.", "passwordChecklist": {"title": "Your password should have:"}, "smsCodeNotValid": "You entered the code sent by SMS incorrectly. Please check and try again.", "emailCodeNotValid": "You entered the wrong code sent to your e-mail address. Please check and try again.", "tokenNull": "Your membership information is incorrect or incomplete. Please login again.", "addressNull": "You have not selected a suitable address for delivery.", "delAddress": "The address you selected will be deleted. Do you want to continue?", "paymentNull": "Please select the payment method.", "addressNotFound": "Welcome.\nPlease add an address to order from your nearest branch.", "promoUse": "This code is used in your order.", "promoUse2": "You can use 1 opportunity code in your order.", "changeBranch": "Your cart will be deleted when you change the branch. Do you want to continue?", "cleanBasket": "If there is any product in your cart during the repeat order, it will be deleted.", "noBasket": "Products from your previous order are not available at the moment", "outTime": "Products stay in your cart for 30 minutes. After that, the products will be deleted automatically.", "responseTime": "Failed to load product list. Please try again.", "delPromo": "The opportunity code you selected will be deleted. Do you want to continue?"}, "langs": [{"lang": "tr", "langDesc": "Türkçe"}, {"lang": "en", "langDesc": "English"}], "menuanasayfa": "<PERSON><PERSON>", "SiparisVer": "Online Order", "QRKodTara": "QR Code Scan", "GirisUyelik": "Login / Sign In", "SiparisTakip": "Delivery Tracking", "SiparisDegerlendir": "Evaluate Order", "UyelikSeviyesi": "Your Membership Level", "OncekiSiparisler": "Previous Orders", "currency": "TL", "currency_a": "TRY", "today": "Today", "now": "Now", "branchPickup": "Delivery from Branch", "futureDate": "Future Delivery", "paymentScreen": "Payment Screen", "paymentType": "Payment Method", "promoCode": "Opportunity Codes", "deliveryService1": "DELIVERY SERVICE", "deliveryService2": "PICKUP SERVICE", "subProductSelect": "You have not selected product menu options.", "noCoupon": "No opportunity code you can use was found.", "noPromo": "No promotion found for the branch.", "orderRateTitle": "Order Evaluation", "btnChangeBranch": "Change Branch", "menuView": "<PERSON><PERSON>", "product-unavailable": "Products are currently unavailable at the {{branchName}} branch. Please try again later. Thank you for your understanding.", "minLimit": "Your cart is below the region limit.", "minLimitPrice": "If you order below the region limit, X TL delivery fee will be added.", "regionLimit": "Region order amount ", "addProduct": "Add", "historyNotFound": "No previous order information found.", "districtNotFount": " the region defined for the district could not be found.", "addbasket": "Added to <PERSON><PERSON>", "nofavorite": "No products found in your favorites list.", "nobranch": "To add to cart from your favorite list, you must first select a branch.", "nobranchrepeat": "For order repetition, you must first select a branch.", "notactiveproduct": "Some product(s) you selected during repeat order could not be added to your cart because they are not available.", "repeatsuccess": "Products have been added to your cart.", "nofavadd": "The product you selected is temporarily unavailable.", "offerterm": "Campaign Terms", "offeruse": "Use Opportunity Code", "lastorderrate": "Evaluate Your Last Order", "ordertrack": "Track your order on Map", "closedistrict": "Package service is temporarily not available to this address.", "faverror1": "You must first login to add to favorites.", "qrerror1": "Please login to earn points from your table orders or to pay with your points.", "second": "sec", "phonenumber": "4447874", "phonenumber2": "4445533", "cameraStatus": "You need to give camera permission to read QR Code. Please click to see the settings.", "deliveryPrice": "Delivery Fee", "discount": "Discount", "product": "Product", "deliveryText": "Delivery fee is charged for orders below the region limit. Delivery fee may differ by region.", "yeni": "New", "acili": "Spicy", "pismis": "Baked", "vegeteryan": "Vegetarian", "thai": "Thai", "freesauce": " is free in your order.", "addsauce": "Add Extra Sauce", "usePoint": "Use Point", "usePromoDiscount": "Opportunity Code", "usePointDiscount": "Point Discount", "pleaseWait": "Please Wait...", "amountTotal": "Amount", "zeroAmount": "Your payment amount is 0 TL, you cannot choose Online Credit Card Payment.", "homebirthdaymsg": "Your date of birth information has been received. Thank you.", "innotitle": "SushiCo", "sushicoclub": "Loyalty Program", "campaing": "Campaigns", "aboutus": "TRUST this flavour", "searchPlaceholder": "Minimum 3 characters", "notificationError": "You don't have a notification yet.", "viewGift": "Show My Gift", "sendGift": "Share with a Friend", "registerMsg": "Complete your registration to win the 500 TRY coupon code gift from your friend gave you as a New Year gift!", "wpShare": "500 TRY coupon code is my New Year gift to you! Sign up at SushiCO to use it!", "trackStatus": {"created": "Your order is preparing", "road": "Your order is on the delivery", "delivery": "Your order has been delivered", "title": "Order Tracking"}, "qr": {"yourpoint": "Your Point", "usepoint": "The point you want to use", "branch": "Branch", "success": "The points you earn from this order : ", "pointErr1": "You can use up to half of the payment amount.", "pointErr2": "You do not have enough points to pay.", "pointErr3": "Earning and Spending Points from Table is not active in this branch.", "pointErr4": "The ticket you entered is not valid.", "pointErr5": "This addition has been used before.", "pointErr6": "SushiCo Points and Promo Code cannot be used in the same order.", "pointErr7": "You cannot use more points than the payment amount", "pointErr8": "SushiCo Points and Opportunity Code cannot be used in the same order. Your opportunity code has been deleted.", "pointErr9": "SushiCo Points and Promo Code cannot be used in the same order. To use SushiCo Points, you need to delete the Promo Code", "pointErr10": "SushiCo Points and Promo Code cannot be used in the same order. To use a Promo Code, you need to delete your SushiCo Points"}, "repeatOrder": {"selectTargetAddress": "Please Select Address to Repeat the Order", "noMatchedAddress": "You do not have an address associated with {{branchName}} to repeat this order.", "cancelRepeatOrder": "CANCEL", "confirmRepeatOrder": "CONFIRM", "activeItemsAdded": "Some items from your previous order are unavailable, the remaining items have been added to your basket."}}