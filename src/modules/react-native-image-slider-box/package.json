{"name": "react-native-image-slider-box", "version": "2.0.7", "description": "A simple and fully customizable React Native component that implements an Image Slider UI.", "main": "index.js", "scripts": {}, "repository": {"type": "git", "url": "git+https://github.com/intellidev1991/react-native-image-slider-box.git"}, "keywords": ["react-native-image-slider-box", "react-native-image-slider", "react-native", "react-native-slider", "ReactNative", "react-component", "react-native-component", "react", "react native", "mobile", "ios", "android", "ui", "slider", "image slider", "image paging slider"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/intellidev1991/react-native-image-slider-box/issues"}, "homepage": "https://github.com/intellidev1991/react-native-image-slider-box#readme", "devDependencies": {"metro-react-native-babel-preset": "latest"}, "dependencies": {"react-native-snap-carousel": "latest"}}