import * as React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import LoginScreen from '../screens/login/LoginScreen';
import RegisterScreen from '../screens/login/RegisterScreen';
import ForgotScreen from '../screens/login/ForgotScreen';
import ActivateScreen from '../screens/login/ActivateScreen';
import AddressScreen from '../screens/login/AddressScreen';
import AdressCityScreen from '../screens/login/AdressCityScreen';
import AddressTownScreen from '../screens/login/AddressTownScreen';
import AddressDistrictScreen from '../screens/login/AddressDistrictScreen';
import ForgotCodeScreen from '../screens/login/ForgotCodeScreen';
import ChangePasswordScreen from '../screens/login/ChangePasswordScreen';
import AgreementScreen from '../screens/login/AgreementScreen';

const LoginStack = createStackNavigator();

const LoginNavigator = ({ navigation, localizationChange, handleBadge }) => {
  return (
    <LoginStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#ec1d23',
          shadowColor: 'black',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontFamily: 'IBMPlexSans-Medium',
        },
      }}
    >
      <LoginStack.Screen name="Login" component={LoginScreen} />
      <LoginStack.Screen name="Register" component={RegisterScreen} />
      <LoginStack.Screen name="Forgot" component={ForgotScreen} />
      <LoginStack.Screen name="Activate" component={ActivateScreen} />
      <LoginStack.Screen name="Address" component={AddressScreen} />
      <LoginStack.Screen name="AddressCity" component={AdressCityScreen} />
      <LoginStack.Screen name="AddressTown" component={AddressTownScreen} />
      <LoginStack.Screen name="ForgotCode" component={ForgotCodeScreen} />
      <LoginStack.Screen name="Agreement" component={AgreementScreen} />
      <LoginStack.Screen name="ChangePassword" component={ChangePasswordScreen} />
      <LoginStack.Screen name="AddressDistrict" component={AddressDistrictScreen} />
    </LoginStack.Navigator>
  );
};

export default LoginNavigator;
