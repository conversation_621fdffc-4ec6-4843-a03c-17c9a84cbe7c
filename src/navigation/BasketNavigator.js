import * as React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import MainScreen from '../screens/basket/MainScreen';
import ThankYouScreen from '../screens/basket/ThankYouScreen';
import PaymentScreen from '../screens/basket/PaymentScreen';
import PaymentDateScreen from '../screens/basket/PaymentDateScreen';
import PaymentAddressScreen from '../screens/basket/PaymentAddressScreen';
import PaymentPromoScreen from '../screens/basket/PaymentPromoScreen';
import PaymentMethodScreen from '../screens/basket/PaymentMethodScreen';
import SettingsCouponsDetail from '../screens/settings/CouponDetailScreen';

import LocalizationContext from '../context/LocalizationContext';

const BasketStack = createStackNavigator();

const BasketNavigator = ({navigation, handleBadge}) => {
  const {t, locale, setLocale} = React.useContext(LocalizationContext);
  const menu = t('basketMenu');

  return (
    <BasketStack.Navigator
      screenOptions={{
        initialRouteName: 'BasketMain',
        headerStyle: {
          backgroundColor: '#78BAE7',
        },
        headerTintColor: '#000',
        headerTitleStyle: {
          fontWeight: 'normal',
          fontFamily: 'AndaleMono',
          letterSpacing: -1,
        },
        unmountOnBlur: true,
      }}>
      <BasketStack.Screen
        name="BasketMain"
        component={MainScreen}
        options={{title: t('menu.basket')}}
        initialParams={{handleBadge}}
      />
      <BasketStack.Screen
        name="Payment"
        component={PaymentScreen}
        options={{title: 'Menü'}}
        initialParams={{handleBadge}}
      />
      <BasketStack.Screen
        name="PaymentDate"
        component={PaymentDateScreen}
        options={{title: 'Menü'}}
      />
      <BasketStack.Screen
        name="PaymentAddress"
        component={PaymentAddressScreen}
        options={{title: 'Menü'}}
      />
      <BasketStack.Screen
        name="PaymentPromo"
        component={PaymentPromoScreen}
        options={{title: 'Menü'}}
        initialParams={{handleBadge}}
      />
      <BasketStack.Screen
        name="PaymentMethod"
        component={PaymentMethodScreen}
        options={{title: 'Menü'}}
      />
      <BasketStack.Screen
        name="ThankYou"
        component={ThankYouScreen}
        options={{title: 'Menü'}}
      />
      <BasketStack.Screen
        name="SettingsCouponsDetail"
        component={SettingsCouponsDetail}
        options={{title: t('settingsMenu.Coupons.title')}}
      />
    </BasketStack.Navigator>
  );
};

export default BasketNavigator;
