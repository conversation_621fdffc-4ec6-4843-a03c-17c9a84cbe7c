import * as React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import BranchMain from '../screens/branch/MainScreen';
import BranchPromo from '../screens/branch/BranchPromo';
import BranchDetail from '../screens/branch/BranchDetail';
import BranchDistricts from '../screens/branch/BranchDistricts';
import {useFocusEffect} from '@react-navigation/native';

const BranchStack = createStackNavigator();

const BranchNavigator = ({navigation, route}) => {
  return (
    <BranchStack.Navigator
      screenOptions={{
        initialRouteName: 'BranchMain',
        headerStyle: {
          backgroundColor: '#78BAE7',
        },
        headerTintColor: '#000',
        headerTitleStyle: {
          fontWeight: 'normal',
          fontFamily: 'AndaleMono',
          letterSpacing: -1,
        },
        unmountOnBlur: true,
      }}>
      <BranchStack.Screen
        name="BranchMain"
        component={BranchMain}
        unmountOnBlur={true}
        initialParams={{menu: route?.params?.menu}}
      />
      <BranchStack.Screen name="BranchPromo" component={BranchPromo} />
      <BranchStack.Screen name="BranchDetail" component={BranchDetail} />
      <BranchStack.Screen name="BranchDistricts" component={BranchDistricts} />
    </BranchStack.Navigator>
  );
};

export default BranchNavigator;
