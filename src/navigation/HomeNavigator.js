import * as React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import HomeScreen from '../screens/home/<USER>';
import SliderInfo from '../screens/home/<USER>';
import SettingsNotifications from '../screens/home/<USER>';
import SushiCoClubScreen from '../screens/home/<USER>';
import CampaingScreen from '../screens/home/<USER>';
import AboutusScreen from '../screens/home/<USER>';
import InnovationsScreen from '../screens/home/<USER>';
import MonthlyMenuScreen from '../screens/home/<USER>';
import SettingsReferer from '../screens/settings/RefererScreen';

import LocalizationContext from '../context/LocalizationContext';

const HomeStack = createStackNavigator();

const HomeNavigator = ({handleBadge, route}) => {
  const {t, locale, setLocale} = React.useContext(LocalizationContext);
  const menu = t('settingsMenu');
  
  return (
    <HomeStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#ec1d23',
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontFamily: 'IBMPlexSans-Medium',
        },
        unmountOnBlur: true,
      }}>
      <HomeStack.Screen
        name="HomeScreen"
        component={HomeScreen}
        options={{
          title: '',
          headerTruncatedBackTitle: '',
          headerBackTitle: '',
          animationEnabled: false,
          headerShown: false,
        }}
        initialParams={{
          handleBadge,
        }}
      />
      <HomeStack.Screen
        name="SliderInfo"
        component={SliderInfo}
        options={{
          title: 'Sushico',
          headerTruncatedBackTitle: '',
          headerBackTitle: '',
          animationEnabled: false,
        }}
      />
      <HomeStack.Screen
        name="SettingsNotifications"
        component={SettingsNotifications}
        options={{title: t('settingsMenu.Notifications.title')}}
      />
      <HomeStack.Screen
        name="SettingsReferer"
        component={SettingsReferer}
        options={{title: t('settingsMenu.Referer.title')}}
      />
      <HomeStack.Screen
        name="SettingsInnovations"
        component={InnovationsScreen}
        options={{title: t('anasayfa.yeniler1')}}
      />

      {/* Yeni sayfalar */}
      <HomeStack.Screen
        name="SettingsSushiCoClub"
        component={SushiCoClubScreen}
        options={{title: t('sushicoclub')}}
      />
      <HomeStack.Screen
        name="MonthlyMenu"
        component={MonthlyMenuScreen}
        options={{title: `${t('anasayfa.ayin1')} ${t('anasayfa.ayin2')}`}}
      />
      <HomeStack.Screen
        name="SettingsCampaing"
        component={CampaingScreen}
        options={{title: t('campaing')}}
      />
      <HomeStack.Screen
        name="SettingsAboutus"
        component={AboutusScreen}
        options={{title: t('aboutus')}}
      />
    </HomeStack.Navigator>
  );
};

export default HomeNavigator;
