import * as React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import LoginScreen from '../screens/login/LoginScreen';
import RegisterScreen from '../screens/login/RegisterScreen';
import ForgotScreen from '../screens/login/ForgotScreen';
import ActivateScreen from '../screens/login/ActivateScreen';
import AddressScreen from '../screens/login/AddressScreen';
import AdressCityScreen from '../screens/login/AdressCityScreen';
import AddressTownScreen from '../screens/login/AddressTownScreen';
import AddressDistrictScreen from '../screens/login/AddressDistrictScreen';
import ForgotCodeScreen from '../screens/login/ForgotCodeScreen';
import ChangePasswordScreen from '../screens/login/ChangePasswordScreen';
import AgreementScreen from '../screens/login/AgreementScreen';

// setting için ayrıca yükleniyor
import SettingsPoint from '../screens/settings/PointScreen';
import SettingsLevel from '../screens/settings/LevelScreen';
import SettingsPointInfo from '../screens/settings/PointInfoScreen';

import OrderRateScreen from '../screens/settings/OrderRateScreen';
import TrackScreen from '../screens/settings/TrackScreen';
import QrCodeSuccessScreen from '../screens/settings/QrCodeSuccessScreen';
import QrCodeScreen from '../screens/settings/QrCodeScreen';
import QrCodePromoScreen from '../screens/settings/QrCodePromoScreen';
import SettingsPreviousOrder from '../screens/settings/PreviousOrderScreen';
import SettingsPreviousOrderDetail from '../screens/settings/PreviousOrderDetailScreen';

import SettingsCoupons from '../screens/settings/promo/PromoScreen';
import SettingsCouponsDetail from '../screens/settings/CouponDetailScreen';

import LocalizationContext from '../context/LocalizationContext';

const LoginStack = createStackNavigator();

const LoginNavigator = ({
  navigation,
  localizationChange,
  handleBadge,
  route,
}) => {
  const {t, locale, setLocale} = React.useContext(LocalizationContext);
  const menu = t('settingsMenu');
  return (
    <LoginStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#78BAE7',
        },
        headerTintColor: '#000',
        headerTitleStyle: {
          fontFamily: 'AndaleMono',
          letterSpacing: -1,
        },
        unmountOnBlur: true,
      }}>
      <LoginStack.Screen name="Login" component={LoginScreen} />
      <LoginStack.Screen
        name="Register"
        component={RegisterScreen}
        initialParams={{
          refCode: route?.params?.refCode ? route?.params?.refCode : '',
        }}
      />
      <LoginStack.Screen name="Forgot" component={ForgotScreen} />
      <LoginStack.Screen name="Activate" component={ActivateScreen} />
      <LoginStack.Screen name="Address" component={AddressScreen} />
      <LoginStack.Screen name="AddressCity" component={AdressCityScreen} />
      <LoginStack.Screen name="AddressTown" component={AddressTownScreen} />
      <LoginStack.Screen name="ForgotCode" component={ForgotCodeScreen} />
      <LoginStack.Screen name="Agreement" component={AgreementScreen} />
      <LoginStack.Screen
        name="ChangePassword"
        component={ChangePasswordScreen}
      />
      <LoginStack.Screen
        name="AddressDistrict"
        component={AddressDistrictScreen}
      />
      <LoginStack.Screen
        name="SettingsPreviousOrder"
        component={SettingsPreviousOrder}
        options={{title: t('settingsMenu.PreviousOrder.title')}}
        initialParams={{
          handleBadge,
        }}
      />
      <LoginStack.Screen
        name="SettingsPreviousOrderDetail"
        component={SettingsPreviousOrderDetail}
        options={{title: t('settingsMenu.PreviousOrder.title')}}
        initialParams={{
          handleBadge,
        }}
      />

      <LoginStack.Screen
        name="SettingsCoupons"
        component={SettingsCoupons}
        options={{title: t('settingsMenu.Coupons.title')}}
      />
      <LoginStack.Screen
        name="SettingsCouponsDetail"
        component={SettingsCouponsDetail}
        options={{title: t('settingsMenu.Coupons.title')}}
      />

      <LoginStack.Screen name="OrderRate" component={OrderRateScreen} />
      <LoginStack.Screen
        name="TrackScreen"
        component={TrackScreen}
        options={{title: t('trackStatus.title')}}
      />
      <LoginStack.Screen
        name="QrCodePromo"
        component={QrCodePromoScreen}
        options={{title: t('anasayfa.qr')}}
      />
      <LoginStack.Screen
        name="QrCodeSuccessScreen"
        component={QrCodeSuccessScreen}
        options={{title: t('anasayfa.qr')}}
      />
      <LoginStack.Screen
        name="QrCode"
        component={QrCodeScreen}
        options={{title: t('anasayfa.qr')}}
      />
      <LoginStack.Screen
        name="SettingsPoint"
        component={SettingsPoint}
        options={{title: t('settingsMenu.Point.title')}}
      />
      <LoginStack.Screen
        name="SettingsPointInfo"
        component={SettingsPointInfo}
        options={{title: t('settingsMenu.Point.title')}}
      />
      <LoginStack.Screen
        name="SettingsLevel"
        component={SettingsLevel}
        options={{title: t('settingsMenu.Level.title')}}
      />
    </LoginStack.Navigator>
  );
};

export default LoginNavigator;
