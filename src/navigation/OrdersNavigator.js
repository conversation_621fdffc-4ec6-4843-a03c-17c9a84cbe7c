import * as React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import MainScreen from '../screens/orders/MainScreen';
import MenuScreen from '../screens/orders/MenuScreen';
import MenuDetail from '../screens/orders/MenuDetail';
import DeliveryType from '../screens/orders/DeliveryTypeScreen';
import NewProductsScreen from '../screens/orders/NewProductsScreen';
import AdressCityScreen from '../screens/login/AdressCityScreen';
import AddressTownScreen from '../screens/login/AddressTownScreen';

import LocalizationContext from '../context/LocalizationContext';

const OrdersStack = createStackNavigator();

const OrdersNavigator = ({handleBadge, navigation}) => {
  const {t, locale, setLocale} = React.useContext(LocalizationContext);
  const menu = t('ordersMenu');

  return (
    <OrdersStack.Navigator
      screenOptions={{
        initialRouteName: 'MainScreen',
        headerStyle: {
          backgroundColor: '#78BAE7',
        },
        headerTintColor: '#000',
        headerTitleStyle: {
          fontFamily: 'AndaleMono',
          letterSpacing: -1,
        },
        unmountOnBlur: true,
      }}>
      <OrdersStack.Screen
        name="MainScreen"
        component={MainScreen}
        options={{
          title: '',
          headerTruncatedBackTitle: '',
          headerBackTitle: '',
          animationEnabled: false,
        }}
      />
      <OrdersStack.Screen
        name="MenuList"
        component={MenuScreen}
        options={{
          title: '',
          headerTruncatedBackTitle: '',
          headerBackTitle: '',
          animationEnabled: false,
        }}
        initialParams={{
          handleBadge,
        }}
      />
      <OrdersStack.Screen
        name="DeliveryType"
        component={DeliveryType}
        options={{
          title: '',
          headerTruncatedBackTitle: '',
          headerBackTitle: '',
          animationEnabled: false,
        }}
      />

      <OrdersStack.Screen
        name="MenuDetail"
        component={MenuDetail}
        options={{
          title: '',
          headerTruncatedBackTitle: '',
          headerBackTitle: '',
        }}
        initialParams={{
          handleBadge,
        }}
      />
      <OrdersStack.Screen name="AddressCity" component={AdressCityScreen} />
      <OrdersStack.Screen name="AddressTown" component={AddressTownScreen} />
      <OrdersStack.Screen
        name="NewProducts"
        component={NewProductsScreen}
        options={{
          title: '',
          headerTruncatedBackTitle: '',
          headerBackTitle: '',
          animationEnabled: false,
        }}
      />
    </OrdersStack.Navigator>
  );
};

export default OrdersNavigator;
