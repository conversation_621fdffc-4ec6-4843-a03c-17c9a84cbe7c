import * as React from 'react';
import {createStackNavigator} from '@react-navigation/stack';

import MainScreen from '../screens/settings/MainScreen';
import SettingsAddress from '../screens/settings/AddressScreen';

import SettingsPreviousOrder from '../screens/settings/PreviousOrderScreen';
import SettingsPreviousOrderDetail from '../screens/settings/PreviousOrderDetailScreen';
import SettingsFavorite from '../screens/settings/FavoriteScreen';
import SettingsCoupons from '../screens/settings/promo/PromoScreen';
import SettingsCouponsDetail from '../screens/settings/CouponDetailScreen';
import SettingsContact from '../screens/settings/ContactScreen';
import SettingsAgreement from '../screens/settings/AgreementScreen';
import SettingsSecurity from '../screens/settings/SecurityScreen';
import SettingsPoint from '../screens/settings/PointScreen';
import SettingsNotifications from '../screens/home/<USER>';
import SettingsPointInfo from '../screens/settings/PointInfoScreen';
import SettingsLevel from '../screens/settings/LevelScreen';
import SettingsReferer from '../screens/settings/RefererScreen';

import OrderRateScreen from '../screens/settings/OrderRateScreen';
import TrackScreen from '../screens/settings/TrackScreen';
import QrCodeSuccessScreen from '../screens/settings/QrCodeSuccessScreen';
import QrCodeScreen from '../screens/settings/QrCodeScreen';
import QrCodePromoScreen from '../screens/settings/QrCodePromoScreen';
import QrCodeThankYouScreen from '../screens/settings/QrCodeThankYouScreen';

import SettingsLanguage from '../screens/settings/LanguageScreen';
import SettingsProfileInfo from '../screens/settings/ProfileInfoScreen';
import LocalizationContext from '../context/LocalizationContext';

import MenuDetail from '../screens/orders/MenuDetail';

const Stack = createStackNavigator();

const SettingsNavigator = ({localizationChange, handleBadge}) => {
  const {t, locale, setLocale} = React.useContext(LocalizationContext);
  const menu = t('settingsMenu');

  return (
    <Stack.Navigator
      screenOptions={{
        initialRouteName: 'SettingsMain',
        headerStyle: {
          backgroundColor: '#78BAE7',
        },
        headerTintColor: '#000',
        headerTitleStyle: {
          fontFamily: 'AndaleMono',
          letterSpacing: -1,
        },
      }}>
      <Stack.Screen
        name="SettingsMain"
        component={MainScreen}
        options={{title: t('menu.settings')}}
        initialParams={{
          handleBadge,
        }}
      />
      <Stack.Screen
        name={menu.Address.name}
        component={SettingsAddress}
        options={{title: t('settingsMenu.Address.title')}}
      />
      <Stack.Screen
        name={menu.Point.name}
        component={SettingsPoint}
        options={{title: t('settingsMenu.Point.title')}}
      />
      <Stack.Screen
        name={menu.Notifications.name}
        component={SettingsNotifications}
        options={{title: t('settingsMenu.Notifications.title')}}
      />
      <Stack.Screen
        name="SettingsPointInfo"
        component={SettingsPointInfo}
        options={{title: t('settingsMenu.Point.title')}}
      />
      <Stack.Screen
        name={menu.Level.name}
        component={SettingsLevel}
        options={{title: t('settingsMenu.Level.title')}}
      />
      <Stack.Screen
        name={menu.Referer.name}
        component={SettingsReferer}
        options={{title: t('settingsMenu.Referer.title')}}
      />

      <Stack.Screen
        name={menu.PreviousOrder.name}
        component={SettingsPreviousOrder}
        options={{title: t('settingsMenu.PreviousOrder.title')}}
        initialParams={{
          handleBadge,
        }}
      />
      <Stack.Screen
        name="SettingsPreviousOrderDetail"
        component={SettingsPreviousOrderDetail}
        options={{title: t('settingsMenu.PreviousOrder.title')}}
        initialParams={{
          handleBadge,
        }}
      />
      <Stack.Screen
        name={menu.Favorite.name}
        component={SettingsFavorite}
        options={{title: t('settingsMenu.Favorite.title')}}
      />
      <Stack.Screen name="OrderRate" component={OrderRateScreen} />
      <Stack.Screen
        name={menu.Coupons.name}
        component={SettingsCoupons}
        options={{title: t('settingsMenu.Coupons.title')}}
      />
      <Stack.Screen
        name="SettingsCouponsDetail"
        component={SettingsCouponsDetail}
        options={{title: t('settingsMenu.Coupons.title')}}
      />
      <Stack.Screen
        name={menu.Agreement.name}
        component={SettingsAgreement}
        options={{title: t('menu.settings')}}
      />
      <Stack.Screen
        name={menu.Security.name}
        component={SettingsSecurity}
        options={{title: t('settingsMenu.Security.title')}}
      />
      <Stack.Screen
        name={menu.Contact.name}
        component={SettingsContact}
        options={{title: t('settingsMenu.Contact.title')}}
      />
      <Stack.Screen
        name={menu.Language.name}
        children={() => (
          <SettingsLanguage localizationChange={localizationChange} />
        )}
        options={{title: menu.Language.title}}
      />
      <Stack.Screen
        name={menu.Info.name}
        component={SettingsProfileInfo}
        options={{title: menu.Info.title}}
      />
      <Stack.Screen
        name="TrackScreen"
        component={TrackScreen}
        options={{title: t('trackStatus.title')}}
      />
      <Stack.Screen
        name="QrCodePromo"
        component={QrCodePromoScreen}
        options={{title: t('button.qr')}}
      />
      <Stack.Screen
        name="QrCodeSuccessScreen"
        component={QrCodeSuccessScreen}
        options={{title: t('button.qr')}}
      />
      <Stack.Screen
        name="QrCodeThankYouScreen"
        component={QrCodeThankYouScreen}
        options={{title: t('button.qr')}}
        initialParams={{
          winPoint: 0,
        }}
      />
      <Stack.Screen
        name="QrCode"
        component={QrCodeScreen}
        options={{title: t('button.qr')}}
      />
      <Stack.Screen
        name="MenuDetail"
        component={MenuDetail}
        options={{
          title: '',
          headerTruncatedBackTitle: '',
          headerBackTitle: '',
        }}
        initialParams={{
          handleBadge,
        }}
      />
    </Stack.Navigator>
  );
};

export default SettingsNavigator;
