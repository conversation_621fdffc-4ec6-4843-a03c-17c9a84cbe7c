import React from 'react';

export const navigationRef = React.createRef();

export function navigate(name, navigationParams = {}) {
  const currentState = navigationRef.current?.getRootState();
  const currentStackIndex = currentState?.index;
  const currentStack = currentState?.routes[currentStackIndex];
  const currentScreen =
    currentStack?.state?.routes?.[currentStack.state.index]?.name;
  const params = {
    ...navigationParams,
    params: {
      ...navigationParams.params,
      fromStack: currentStack?.name,
      fromScreen: currentScreen,
    },
  };
  navigationRef.current?.navigate(name, params);
}

