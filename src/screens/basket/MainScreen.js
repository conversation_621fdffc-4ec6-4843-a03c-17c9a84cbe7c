import React, {useContext, useEffect, useState} from 'react';
import {
  View,
  Text,
  Image,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  LogBox,
  Alert,
  ImageBackground,
  Modal,
  StyleSheet,
  Pressable,
  Dimensions,
} from 'react-native';
import {Icon} from 'react-native-elements';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';
import {useIsFocused} from '@react-navigation/native';
import mobileApi from '../../api/mobileApi';
import analytics from '@react-native-firebase/analytics';

import {navigate} from '../../navigationRef';
import Loading from '../../components/Loading';
import {ModalRecommendedProducts} from './components';

LogBox.ignoreAllLogs();
const {width: viewportWidth, height: viewportHeight} = Dimensions.get('window');

const MainScreen = ({navigation, route}) => {
  const isFocused = useIsFocused();
  const {handleBadge} = route.params;
  const [isLoading, setLoading] = useState(true);

  const {state, loginCheck, saveBasket, getBadge} = useContext(UserContext);
  const {t, locale} = useContext(LocalizationContext);
  const [basket, setBasket] = useState([]);
  const [limit, setLimit] = useState();
  const [deliveryPrice, setDeliveryPrice] = useState();
  const [service, setService] = useState();
  const [sauceText, setSauceText] = useState('');
  const [sauceModal, setSauceModal] = useState(false);
  const [products, setProducts] = useState([]);
  const [fdatas, setFdatas] = useState([]);
  const [pagestate, setPageState] = useState([]);
  const [upSellModal, setUpSellModal] = useState(false);
  const [upSellText, setUpSellText] = useState('');

  const [showRecommendedProducts, setShowRecommendedProducts] = useState(false);

  let sauceArray = [];
  let diffTime = 0;
  let basketTime = 0;
  const l = t('login');
  let outTime = 0;
  const outId = [];

  const _decBasket = async req => {
    let count = pagestate[`${req.p.productId}`]?.count
      ? pagestate[`${req.p.productId}`]?.count
      : 0;
    count--;
    console.log('_decbasket count:', count);
    await AsyncStorage.multiGet(['@basket'])
      .then(r => {
        const b = r[0][1] ? JSON.parse(r[0][1]) : [];
        const inx = getAlIndex(b, req.p.productId);
        const data = {
          title: req.p.name,
          price: req.p.price,
          id: req.p.productId,
          count,
          action: req.action,
          inx,
        };
        console.log('_decbasket data', data);
        setPageState({
          ...pagestate,
          [`${req.p.productId}`]: {
            count,
          },
        });
        saveBasket({data: data, alert: t('alert')}).then(() => {
          _basketCheck();
          _getBadge();
        });
      })
      .catch(err => {
        // _goBack();
      });
  };

  const _getBadge = async () => {
    await AsyncStorage.getItem('@basket')
      .then(response => {
        const r = response ? JSON.parse(response) : [];
        handleBadge(r.length);
      })
      .catch(e => {
        handleBadge(0);
      });
  };

  const _addBasket = async req => {
    let count = pagestate[`${req.p.productId}`]?.count
      ? pagestate[`${req.p.productId}`]?.count
      : 0;
    count++;
    console.log('_addbasket count:', count);
    setPageState({
      ...pagestate,
      [`${req.p.productId}`]: {
        count,
      },
    });

    const data = {
      title: req.p.name,
      price: req.p.price,
      menu: [],
      sos: req.p.sos ? req.p.sos : '',
      id: req.p.productId,
      desc: req.p.desc,
      category: req.p.category,
      count,
      optionTotal: 0,
      optionsCount: 0,
      promoId: '',
      promoCode: '',
      action: req.action,
    };

    await saveBasket({data: data, alert: t('alert')}).then(response => {
      _basketCheck();
      _getBadge();
    });
  };

  const getAlIndex = (arr, id) => {
    return arr.findIndex(obj => obj.id === id.toString());
  };

  const _saveBasket = async ({
    action,
    id,
    title,
    price,
    count,
    inx,
    desc,
    category,
  }) => {
    const data = {
      title,
      price,
      id,
      count,
      action,
      inx,
      desc,
      category,
    };
    await saveBasket({data: data, alert: t('alert')}).then(() => {
      _basketCheck();
    });
  };

  const _getSauce = async (restaurant, menu) => {
    mobileApi
      .get(`/${locale}/getProducts?restaurant=${restaurant}&menu=${menu}`)
      .then(response => {
        //console.log('rest', JSON.stringify(response.data));
        setProducts(response.data);
      })
      .catch(error => {
        //console.log(error);
      });
  };

  const _basketTimeCheck = async () => {
    outTime = 0;
    const items = [];
    await AsyncStorage.getItem('@basket')
      .then(response => {
        const r = response ? JSON.parse(response) : [];
        const now = new Date();
        if (r.length > 0) {
          r.map((i, j) => {
            const temp = {
              item_id: i.id,
              item_name: i.title,
              item_category: i.category,
              quantity: i.count,
              price: parseFloat(i.price),
              item_brand: 'Sushico',
            };
            items.push(temp);
            basketTime = new Date(i.basketTime);
            diffTime = Math.floor(
              (now.getTime() - basketTime.getTime()) / (1000 * 60),
            );
            //console.log(diffTime);
            if (diffTime > 30) {
              outTime++;
              outId.push(j);
            }
          });
        }

        if (outTime > 0) {
          // AsyncStorage.setItem('@basket', JSON.stringify(r));
          AsyncStorage.multiRemove([
            '@basket',
            '@promoCode',
            '@promoId',
            '@promoPrice',
            '@promoProductName',
            '@isRecommendedProductsShown',
          ]);
          Alert.alert(t('payment').warning, t('alert').outTime, [
            {
              text: t('alert').okBtn,
              onPress: () => {
                _basketCheck();
                getBadge();
              },
            },
          ]);
        } else {
          if (items) {
            const tempdatas = {
              items,
              currency: 'TRY',
              value: state.totalAmount,
              placement: 'Cart',
            };
            setFdatas(tempdatas);
            analytics().logEvent('view_cart', tempdatas);
          }
        }
        setBasket(r);
        setLimit(response[1][1]);
        setDeliveryPrice('');
        setPageState([]);
      })
      .catch(e => {
        setBasket([]);
      });
  };

  const _calculateSauce = async (sauce, pcount) => {
    for (let item of sauce) {
      sauceArray.push({
        sosid: item.sosid,
        count: pcount,
        name: item.sosName,
        oran: item.oran,
      });
    }
  };

  const _createSauceText = (text, val) => {
    let temp = '';
    let i = 0;
    Object.keys(val).map(item => {
      if (i > 0) {
        temp += ', ';
      }
      temp += `${Math.ceil(val[item])} ${text[item]}`;
      i++;
    });
    if (temp) {
      temp += t('freesauce');
      setSauceText(temp);
    } else {
      setSauceText('');
    }
  };

  const _basketCheck = async () => {
    await AsyncStorage.multiGet([
      '@basket',
      '@regionLimit',
      '@service',
      '@branchId',
      '@deliveryPrice',
    ])
      .then(response => {
        _getSauce(response[3][1], 32);
        sauceArray = [];
        const r = response[0][1] ? JSON.parse(response[0][1]) : [];
        let totalAmount = 0;
        //console.log('rrr', r);
        if (r.length > 0) {
          r.map((i, j) => {
            // console.log('sıs', i.sos, i.count);
            if (i?.sos && i?.count) {
              _calculateSauce(i.sos, i.count);
            }
            totalAmount +=
              i.count * (parseFloat(i.optionTotal) + parseFloat(i.price));
          });
        } else {
          totalAmount = 0;
        }

        setDeliveryPrice(0);

        state.totalAmount = totalAmount;
        setBasket(r);
        setLimit(response[1][1]);

        let sauceText = {};
        let sauceVal = {};
        for (i = 0; i <= sauceArray.length - 1; i++) {
          let sid = sauceArray[i].sosid;
          sauceText[sid] = sauceArray[i].name;
          sauceVal[sid] = sauceVal[sid]
            ? sauceVal[sid] + sauceArray[i].count * sauceArray[i].oran
            : sauceArray[i].count * sauceArray[i].oran;
        }

        _createSauceText(sauceText, sauceVal);

        setService(response[2][1]);
      })
      .catch(e => {
        setBasket([]);
      });
  };

  const _goPayment = async () => {
    await analytics().logEvent('begin_checkout', fdatas);
    navigate('Payment');
  };
  const _readVal = async () => {
    await AsyncStorage.multiGet(['@token', '@memberid'])
      .then(r => {
        const req = {
          memberId: r[1][1],
          token: r[0][1],
          amount: state.totalAmount,
          locale,
        };
        _getResults(req);
      })
      .catch(err => {
        console.log(err);
      });
  };

  const _getResults = async request => {
    await mobileApi.post(`/${locale}/upSell`, request).then(response => {
      //console.log(response.data);
      if (!response.data.err) {
        setUpSellModal(true);
        setUpSellText(response.data.text);
      }
      //console.log('res', response.data);
    });
  };

  useEffect(() => {
    getBadge();
    handleBadge(state.totalCount);
  }, [state.totalCount]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loginCheck().then(() => {
        _basketTimeCheck().then(() => {
          _basketCheck().then(() => {
            setLoading(false);
            AsyncStorage.getItem('@isRecommendedProductsShown').then(value => {
              const isRecommendedProductsShown = JSON.parse(value);
              setShowRecommendedProducts(!isRecommendedProductsShown); //set this value to true for debug
            });
          });
        });
      });
    });

    _readVal();

    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    if (basket.length > 0) {
      navigation.setOptions({
        headerRight: () => (
          <TouchableOpacity
            onPress={() => {
              navigate('Orders', {screen: 'MenuList', params: {menu: 0}});
            }}
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            {/*
            <Icon
              name="plus"
              type="font-awesome"
              iconStyle={{color: 'white', fontSize: 14}}
            />

            <Text
              style={{
                color: 'white',
                fontSize: 16,
                paddingHorizontal: 5,
              }}>
              {t('addProduct')}
            </Text>
            */}
          </TouchableOpacity>
        ),
      });
    } else {
      navigation.setOptions({
        headerRight: () => null,
      });
    }
  }, [basket.length]);

  useEffect(() => {
    if (state.isLogin) {
      _basketCheck();
    }
  }, [state.totalAmount]);

  useEffect(() => {
    if (!isLoading && !basket.length) {
      AsyncStorage.removeItem('@isRecommendedProductsShown'); // enable re-show the recommended products modal
    }
  }, [isLoading, basket.length]);

  if (isLoading) {
    return <Loading />;
  }

  if (!state.isLogin || basket.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <ImageBackground
          source={require('../../../assets/img/back_alt.jpg')}
          resizeMode="cover"
          style={{flex: 1, marginTop: 0}}>
          <View
            style={[
              styles.sepetiniz_full_kutu,
              {
                marginTop: 10,
                padding: 40,
                display: 'flex',
                flexDirection: 'column',
                alignSelf: 'center',
                backgroundColor: 'transparent',
              },
            ]}>
            {!state.isLogin ? (
              <View>
                <Image
                  source={require('../../../assets/img/uzgun.png')}
                  style={{
                    height: 90,
                    width: 90,
                    marginVertical: 20,
                    alignSelf: 'center',
                    resizeMode: 'contain',
                  }}
                />
                <Text style={[styles.text18, styles.black, styles.tcenter20]}>
                  {l.nologinbasketempty}
                </Text>
              </View>
            ) : (
              <View>
                <Image
                  source={require('../../../assets/img/uzgun.png')}
                  style={{
                    height: 90,
                    width: 90,
                    marginVertical: 20,
                    alignSelf: 'center',
                    resizeMode: 'contain',
                  }}
                />
                <Text
                  style={[
                    styles.text20,
                    styles.tcenter,
                    styles.black,
                    styles.mb10,
                  ]}>
                  {l.basketempty}{' '}
                </Text>
                <View style={styles.detay_kutu}>
                  <View style={styles.detay_kutu_center}>
                    <TouchableOpacity
                      onPress={() =>
                        navigate('Orders', {screen: 'MainScreen'})
                      }>
                      <View style={[sc.PrimaryButton, {width: '90%'}]}>
                        <Text style={sc.PrimaryButton_text}>
                          {t('button.addbasket')}{' '}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            )}
          </View>
        </ImageBackground>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ImageBackground
        source={require('../../../assets/img/back_cart.jpg')}
        resizeMode="cover"
        style={{flex: 1, marginTop: 0}}>
        {service === '1' && state.totalAmount < parseFloat(limit) && (
          <View
            style={[
              {
                alignSelf: 'center',
                paddingVertical: 10,
                color: 'red',
              },
            ]}>
            <Text style={[styles.text14, {color: 'red', textAlign: 'center'}]}>
              {t('minLimit')} {'\n'} {t('regionLimit')} : {limit}{' '}
              {t('currency')}
            </Text>
          </View>
        )}
        <ScrollView style={{marginTop: 10}}>
          {basket.map((item, i) => {
            //console.log(item);
            return (
              <View
                style={[
                  styles.sepetiniz_full_kutu,
                  styles.shadow,
                  {marginBottom: 10},
                ]}
                key={i}>
                <View style={styles.sepetiniz_child_sol}>
                  <View
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}>
                    <Text style={sc.UrunAdi}>
                      {item.title} {'\n'}
                      {item.promoCode && (
                        <Text style={(sc.Fiyat, styles.text12)}>
                          ({item.promoCode}) {'\n'}
                        </Text>
                      )}
                      <Text style={sc.Fiyat_sepet}>
                        {item.price} {t('currency')}
                      </Text>
                    </Text>
                  </View>

                  {item.menu &&
                    item.menu.map((option, j) => {
                      return (
                        <View
                          key={j}
                          style={{
                            display: 'flex',
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}>
                          <Text
                            style={{
                              fontStyle: 'italic',
                              fontSize: 10,
                              color: 'black',
                            }}>
                            {option.itemname}
                          </Text>

                          <Text
                            style={{
                              fontStyle: 'italic',
                              fontSize: 10,
                              color: 'black',
                            }}>
                            {option.itemprice > 0
                              ? `+${option.itemprice} ${t('currency')}`
                              : null}
                          </Text>
                        </View>
                      );
                    })}
                </View>

                <View style={styles.basket_count_container}>
                  <TouchableOpacity
                    onPress={() => {
                      let newCount = item.count - 1;
                      _saveBasket({
                        action: 'dec',
                        id: item.id,
                        title: item.title,
                        price: item.price,
                        count: newCount,
                        inx: i,
                        desc: item.desc,
                        category: item.category,
                      });
                    }}>
                    <View style={styles.basket_count_left}>
                      {item.count === 1 ? (
                        <Icon
                          name="trash-o"
                          type="font-awesome"
                          iconStyle={{color: 'silver', fontSize: 18}}
                        />
                      ) : (
                        <Icon
                          name="minus"
                          type="font-awesome"
                          iconStyle={{color: 'white', fontSize: 14, top: 2}}
                        />
                      )}
                    </View>
                  </TouchableOpacity>

                  <View style={styles.basket_count_middle}>
                    <Text style={sc.Adet2}>{item.count}</Text>
                  </View>
                  {item.promoCode === '' ? (
                    <TouchableOpacity
                      onPress={() => {
                        let newCount = item.count + 1;

                        _saveBasket({
                          action: 'inc',
                          id: item.id,
                          title: item.title,
                          price: item.price,
                          count: newCount,
                          inx: i,
                          desc: item.desc,
                          category: item.category,
                        });
                      }}>
                      <View style={[styles.basket_count_right, {height: 30}]}>
                        <Icon
                          name="plus"
                          type="font-awesome"
                          iconStyle={{
                            color: 'white',
                            fontSize: 14,
                            paddingTop: 3,
                          }}
                        />
                      </View>
                    </TouchableOpacity>
                  ) : null}
                </View>
              </View>
            );
          })}

          {
            /* Sos bilgi ekranı */ sauceText ? (
              <View style={styles.anasayfa_element_genis}>
                <View
                  style={[
                    styles.anasayfa_element_genis_ikon,
                    {backgroundColor: '#f8ecde'},
                  ]}>
                  <View>
                    <Image
                      source={require('../../../assets/img/icon_info.png')}
                      style={{
                        height: 32,
                        width: 32,
                        marginRight: 15,
                        resizeMode: 'contain',
                      }}
                    />
                  </View>

                  <View style={{width: '85%'}}>
                    <Text
                      style={[
                        styles.anasayfa_element_yazi,
                        {flexWrap: 'wrap'},
                      ]}>
                      {sauceText}
                    </Text>
                  </View>
                </View>
              </View>
            ) : null
          }

          <View style={{flex: 1, flexDirection: 'row', marginTop: 20}}>
            <View style={{flex: 1, marginLeft: 15}}>
              <TouchableOpacity
                onPress={() => {
                  navigate('Orders', {screen: 'MenuList', params: {menu: 0}});
                }}
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <View style={[styles.basket_count_right, {maxWidth: 30}]}>
                  <Icon
                    name="plus"
                    type="font-awesome"
                    iconStyle={{color: 'white', fontSize: 14}}
                  />
                </View>
                <Text
                  style={{
                    color: 'black',
                    fontFamily: 'AndaleMono',
                    letterSpacing: -1,
                    fontSize: 16,
                    paddingHorizontal: 5,
                  }}>
                  {t('addProduct')}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={{flex: 1}}>
              <TouchableOpacity
                onPress={() => setSauceModal(true)}
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  
                }}>
                <Text
                  style={{
                    color: 'black',
                    fontSize: 16,
                    paddingHorizontal: 5,
                    fontFamily: 'AndaleMono',
                    letterSpacing: -1,
                  }}>
                  {t('addsauce')}
                </Text>
                <View style={[styles.basket_count_right, {maxWidth: 30}]}>
                  <Icon
                    name="plus"
                    type="font-awesome"
                    iconStyle={{color: 'white', fontSize: 14}}
                  />
                </View>
              </TouchableOpacity>
            </View>
          </View>

          {/* Sos bilgi ekranı */}

          {/* modal */}
          <Modal
            animationType="slide"
            transparent={true}
            visible={sauceModal}
            statusBarTranslucent={true}
            onRequestClose={() => {
              setSauceModal(!sauceModal);
            }}>
            <View style={pagestyles.bottomView}>
              <View style={pagestyles.bottomModalView}>
                <ScrollView
                  style={{
                    width: viewportWidth,
                  }}>
                  {products.map((p, j) => {
                    return (
                      <View key={j}>
                        <View style={[styles.sepetiniz_full_kutu]}>
                          <View style={styles.sepetiniz_child_sol}>
                            <View
                              style={{
                                display: 'flex',
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                              }}>
                              <Text style={sc.UrunAdi}>{p.name}</Text>
                              <Text style={sc.Fiyat_sepet}>
                                {p.price} {t('currency')}
                              </Text>
                            </View>
                          </View>

                          <View style={styles.basket_count_container}>
                            {pagestate[`${p.productId}`]?.count > 0 ? (
                              <TouchableOpacity
                                onPress={() => {
                                  _decBasket({action: 'dec', p});
                                }}>
                                <View style={styles.basket_count_left}>
                                  {pagestate[`${p.productId}`]?.count === 1 ? (
                                    <Icon
                                      name="trash-o"
                                      type="font-awesome"
                                      iconStyle={{color: 'white', fontSize: 14}}
                                    />
                                  ) : (
                                    <Text>
                                      <Icon
                                        name="minus"
                                        type="font-awesome"
                                        iconStyle={{
                                          color: 'white',
                                          fontSize: 14,
                                          top: 2,
                                        }}
                                      />
                                    </Text>
                                  )}
                                </View>
                              </TouchableOpacity>
                            ) : null}

                            <View style={styles.basket_count_middle}>
                              <Text style={sc.Adet2}>
                                {pagestate[`${p.productId}`]?.count
                                  ? pagestate[`${p.productId}`]?.count
                                  : ''}
                              </Text>
                            </View>

                            <TouchableOpacity
                              onPress={() => {
                                _addBasket({action: 'add', p});
                              }}>
                              <View style={styles.basket_count_right}>
                                <Icon
                                  name="plus"
                                  type="font-awesome"
                                  iconStyle={{
                                    color: 'white',
                                    fontSize: 14,
                                    paddingTop: 4,
                                  }}
                                />
                              </View>
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    );
                  })}
                </ScrollView>

                <Pressable onPress={() => setSauceModal(!sauceModal)}>
                  <View style={sc.PrimaryButton}>
                    <Text style={sc.PrimaryButton_text}>
                      {t('button.close')}
                    </Text>
                  </View>
                </Pressable>
              </View>
            </View>
          </Modal>

          {/* modal */}

          <Modal
            animationType="slide"
            transparent={true}
            visible={upSellModal}
            statusBarTranslucent={true}
            onRequestClose={() => {
              setSauceModal(!upSellModal);
            }}>
            <View style={pagestyles.centeredView}>
              <View style={pagestyles.modalView}>
                <View
                  style={[
                    styles.anasayfa_element_genis_ikon,
                    {backgroundColor: '#ffffff', marginBottom: 15},
                  ]}>
                  <View style={{width: '12%'}}>
                    <Text>
                      <Image
                        source={require('../../../assets/img/icon_duyuru.png')}
                        style={{height: 32, width: 32, resizeMode: 'contain'}}
                      />
                    </Text>
                  </View>
                  <View style={[styles.anasayfa_element_txt, {width: '85%'}]}>
                    <Text style={styles.anasayfa_element_yazi}>
                      {upSellText}
                    </Text>
                  </View>
                </View>
                <Pressable onPress={() => setUpSellModal(!upSellModal)}>
                  <View style={sc.PrimaryButton}>
                    <Text style={sc.PrimaryButton_text}>
                      {t('button.close')}
                    </Text>
                  </View>
                </Pressable>
              </View>
            </View>
          </Modal>
        </ScrollView>

        {basket.length > 0 ? (
          <View style={styles.fiyat_block}>
            <View style={styles.fiyat_text}>
              <Text style={[styles.text22, styles.ozelfont_bold, styles.black]}>
                {Math.round(parseFloat(state.totalAmount) + 0)}

                <Text style={{color: 'black'}}> {t('currency')}</Text>
              </Text>
            </View>
            {state.totalAmount >= parseFloat(limit) ? (
              <TouchableOpacity onPress={() => _goPayment()}>
                <View style={styles.fiyat_btn}>
                  <Text style={[sc.PrimaryButton_text, styles.ozelfont_bold]}>
                    {t('button.acceptBasket')}{' '}
                  </Text>
                </View>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                onPress={() =>
                  navigate('Orders', {
                    screen: 'MenuList',
                    params: {
                      menu: 0,
                      g: 0,
                    },
                  })
                }>
                <View style={styles.fiyat_btn}>
                  <Text style={[sc.PrimaryButton_text, styles.ozelfont_bold]}>
                    {t('addProduct')}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
        ) : null}

        <ModalRecommendedProducts
          basket={basket}
          _addBasket={_addBasket}
          _decBasket={_decBasket}
          _saveBasket={_saveBasket}
          setShowRecommendedProducts={setShowRecommendedProducts}
          showRecommendedProducts={showRecommendedProducts}
          _basketCheck={_basketCheck}
          _getBadge={_getBadge}
        />
      </ImageBackground>
    </SafeAreaView>
  );
};

const pagestyles = StyleSheet.create({
  bottomView: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    //backgroundColor: '#00000099',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  bottomModalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    height: viewportHeight * 0.5,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    borderRadius: 10,
    padding: 10,
    elevation: 2,
  },
  buttonOpen: {
    backgroundColor: '#F194FF',
  },
  buttonClose: {
    backgroundColor: '#2196F3',
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontFamily: 'IBMPlexSans-Light',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
});

export default MainScreen;
