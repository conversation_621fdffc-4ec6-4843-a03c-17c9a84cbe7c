import React, {useState, useEffect} from 'react';
import {View, TouchableOpacity} from 'react-native';
import {Icon, ListItem} from 'react-native-elements';
import AsyncStorage from '@react-native-async-storage/async-storage';
import styles from '../../stylesheets/styles';

import LocalizationContext from '../../context/LocalizationContext';

const PaymentAddressScreen = ({navigation, route}) => {
  const {t} = React.useContext(LocalizationContext);
  const p = t('payment');

  const [pagestate, setPageState] = useState({
    userData: [],
    addressId: '',
    branchId: '',
  });

  const _goBack = () => {
    navigation.goBack();
    route.params._onOrderAddress();
  };

  const _readVal = async () => {
    await AsyncStorage.multiGet(['@userdata', '@addressid', '@branchId'])
      .then(r => {
        const userdata = r[0][1] ? JSON.parse(r[0][1]) : [];
        const addressid = r[1][1];
        const branchid = r[2][1];

        setPageState({
          ...pagestate,
          userData: userdata.address,
          addressId: addressid,
          branchId: branchid,
        });
      })
      .catch(err => {});
  };

  const getAddressIndex = (arr, id) => {
    return arr.findIndex(obj => obj.id === id);
  };
  const getBranchIndex = (arr, id) => {
    return arr.findIndex(obj => obj.branchId === parseInt(id));
  };

  const _setAddress = async id => {
    await AsyncStorage.setItem('@addressid', id).then(() => {
      _goBack();
    });
  };

  useEffect(() => {
    navigation.setOptions({
      title: p.deliveryaddress,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    _readVal();
  }, []);

  if (!pagestate.userData) {
    return null;
  }

  return (
    <View style={{flex: 1, justifyContent: 'flex-start'}}>
      {pagestate.userData.map((item, i) => {
        let index = '';
        if (item.branchs) {
          index = getBranchIndex(item.branchs, pagestate.branchId);
        } else {
          index = -1;
        }
        //const index = 0;
        if (index > -1) {
          return (
            <ListItem key={i} bottomDivider>
              <Icon
                name="location-arrow"
                type="font-awesome"
                iconStyle={{color: 'gray', fontSize: 20}}
              />
              <ListItem.Content>
                <TouchableOpacity onPress={() => _setAddress(item.id)}>
                  <ListItem.Title style={styles.SubeAd}>
                    {item.label}
                  </ListItem.Title>
                  <ListItem.Subtitle style={{lineHeight: 28}}>
                    {item.detail} {` `}
                    {item.regionName} {` `}
                    {item.districtName}
                  </ListItem.Subtitle>
                </TouchableOpacity>
              </ListItem.Content>
              {item.id === pagestate.addressId && (
                <ListItem.Chevron
                  name="check"
                  type="font-awesome"
                  iconStyle={{color: 'green', fontSize: 24}}
                />
              )}
            </ListItem>
          );
        }
      })}
    </View>
  );
};

export default PaymentAddressScreen;
