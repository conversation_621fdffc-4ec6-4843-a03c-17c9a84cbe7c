import React, {useState} from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import {Icon, ListItem} from 'react-native-elements';
import DatePicker from 'react-native-date-picker';
import {Context as UserContext} from '../../context/UserContext';
import LocalizationContext from '../../context/LocalizationContext';

const PaymentDateScreen = ({navigation, route}) => {
  const {state} = React.useContext(UserContext);
  const [refresh, setRefresh] = useState(0);
  const {t, locale} = React.useContext(LocalizationContext);
  const p = t('payment');
  const b = t('button');

  const _goBack = () => {
    route.params._onOrderDate({
      date: state.orderDate,
      type: state.deliveryType,
    });
    navigation.goBack();
  };

  const _selectDateTime = type => {
    const now = new Date();
    if (type == 1) {
      _onChangeDate(now);
      state.deliveryType = 2;
    } else {
      _onChangeDate(now);
      //state.orderDate = now;
      state.deliveryType = 1;
    }
    _setRefresh();
  };

  const _setRefresh = () => {
    refresh === 1 ? setRefresh(0) : setRefresh(1);
  };

  const _onChangeDate = date => {
    state.orderDate = date;
    const now = new Date();
    const currentHour = now.getHours();
    const currentTime = now.getTime();
    const currentDay = now.getDate();
    const selectHour = date.getHours();
    const selectMinute = date.getMinutes();
    const selectDay = date.getDate();
    const selectTime = date.getTime();

    if (route.params.service === '1') {
      const remainHours = 20 - currentHour;
      const minTime = 1000 * 60 * 60 * 3;
      const minTime2 = 1000 * 60 * 15;
      const maxTime = 1000 * 60 * 60 * (24 + remainHours) * 3;
      let err = '';
      if (
        currentTime + minTime > selectTime ||
        currentTime + maxTime < selectTime
      ) {
        if (currentHour + 3 > 20) {
          now.setDate(currentDay + 1);
          now.setHours('12');
          now.setMinutes('0');
        } else if (currentHour + 3 < 12) {
          now.setHours('12');
          now.setMinutes('0');
        } else {
          now.setTime(currentTime + minTime);
          if (now.getMinutes < 15) {
            now.setMinutes('0');
          } else if (now.getMinutes > 15 && now.getMinutes < 30) {
            now.setMinutes('15');
          } else if (now.getMinutes > 30 && now.getMinutes < 45) {
            now.setMinutes('30');
          } else if (now.getMinutes > 45) {
            now.setMinutes('45');
          }
        }
        state.orderDate = now;
      } else {
        if (selectHour > 20 && currentDay === selectDay) {
          now.setDate(currentDay + 1);
          now.setHours('12');
          now.setMinutes('0');
        } else if (selectHour < 12 || selectHour > 20) {
          now.setDate(selectDay);
          now.setHours('12');
          now.setMinutes('0');
        } else {
          now.setDate(selectDay);
          now.setHours(selectHour);
          selectHour === 20 && selectMinute > 0
            ? now.setMinutes(0)
            : now.setMinutes(selectMinute);
        }
        state.orderDate = now;
      }
    } else {
      const minTime2 = 1000 * 60 * 29;
      if (selectHour < 12 || selectHour > 22) {
        if (currentHour < 12) {
          now.setHours('12');
          now.setMinutes('0');
        } else {
          now.setTime(currentTime + minTime2);
        }
      } else {
        if (currentTime + minTime2 > selectTime) {
          now.setTime(currentTime + minTime2);
        } else {
          now.setTime(selectTime);
        }
        if (selectHour === 22) now.setMinutes(0);
      }
      state.orderDate = now;
    }

    _setRefresh();
  };

  React.useEffect(() => {
    navigation.setOptions({
      title: p.deliverytime,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
      headerRight: () => (
        <TouchableOpacity
          onPress={() => {
            _goBack();
          }}
          style={{display: 'flex', flexDirection: 'row',  backgroundColor: '#fff', alignItems: 'center', padding: 10, borderRadius: 2, marginRight: 5}}>
          <Text
            style={{
              color: 'black',
              fontSize: 16,
              paddingHorizontal: 5,
              fontWeight: 'bold',
              fontFamily: 'AndaleMono',
              letterSpacing: -1,
            }}>
            {b.confirm}
          </Text>
          <Icon name="check" type="feather" iconStyle={{color: 'black'}} />
        </TouchableOpacity>
      ),
    });
  }, [refresh]);

  React.useEffect(() => {
    const now = new Date();
    _onChangeDate(now);
    state.service = route.params.service;
    if (route.params.service === '1') {
      state.deliveryType = 1;
    } else {
      state.deliveryType = 2;
    }
  }, []);

  return (
    <View style={{flex: 1, justifyContent: 'space-between'}}>
      {route.params.service === '1' ? (
        <View>
          <ListItem key="1" bottomDivider>
            <Icon name="clock" type="feather" iconStyle={{color: '#78bae7'}} />
            <ListItem.Content>
              <TouchableOpacity onPress={() => _selectDateTime(0)}>
                <ListItem.Title style={{fontFamily: 'Andale Mono Bold', fontSize: 20, letterSpacing: -1}}>{p.now}</ListItem.Title>
              </TouchableOpacity>
            </ListItem.Content>
            {state.deliveryType === 1 && (
              <ListItem.Chevron
                name="check"
                type="feather"
                iconStyle={{color: 'green', fontWeight: 'bold', fontSize: 20}}
              />
            )}
          </ListItem>
          <ListItem key="2" bottomDivider>
            <Icon name="clock" type="feather" iconStyle={{color: '#78bae7'}} />
            <ListItem.Content>
              <TouchableOpacity onPress={() => _selectDateTime(1)}>
                <ListItem.Title style={{fontFamily: 'Andale Mono Bold', fontSize: 18,letterSpacing: -1}}>{p.futuredate}</ListItem.Title>
                <ListItem.Subtitle
                  style={{fontSize: 14, color: 'gray', fontWeight: 'bold', fontFamily: 'AndaleMono'}}
                >{p.deliverytime_txt1}</ListItem.Subtitle>
              </TouchableOpacity>
            </ListItem.Content>
            {state.deliveryType === 2 && (
              <ListItem.Chevron
                name="check"
                type="feather"
                iconStyle={{color: 'green', fontWeight: 'bold', fontSize: 20}}
              />
            )}
          </ListItem>
        </View>
      ) : (
        <View>
          <ListItem key="2" bottomDivider>
            <Icon name="clock" type="feather" />
            <ListItem.Content>
              <TouchableOpacity onPress={() => _selectDateTime(1)}>
                <ListItem.Subtitle>{p.deliverytime_txt2}</ListItem.Subtitle>
              </TouchableOpacity>
            </ListItem.Content>
            {state.deliveryType === 2 && (
              <ListItem.Chevron
                name="check"
                type="feather"
                iconStyle={{color: 'green', fontWeight: 'bold', fontSize: 24}}
              />
            )}
          </ListItem>
        </View>
      )}
      {state.deliveryType === 2 ? (
        <View
          style={{
            width: '100%',
            alignItems: 'center',
            
          }}>
          <View>
            <DatePicker
              date={state.orderDate}
              minuteInterval={15}
              androidVariant="nativeAndroid"
              locale="tr"
              theme="light"
              mode={route.params.service === '1' ? 'datetime' : 'time'}
              onDateChange={date => _onChangeDate(date)}
            />
          </View>
        </View>
      ) : null}
    </View>
  );
};

export default PaymentDateScreen;
