import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ImageBackground,
} from 'react-native';
import {Icon, ListItem} from 'react-native-elements';
import {SafeAreaView} from 'react-native-safe-area-context';
import mobileApi from '../../api/mobileApi';
import LocalizationContext from '../../context/LocalizationContext';

const PaymentMethodScreen = ({navigation, route}) => {
  const [results, setResults] = useState(null);
  const {t, locale} = React.useContext(LocalizationContext);

  const {branchId} = route.params;
  const getResults = async () => {
    const response = await mobileApi.get(
      `/${locale}/getPayments?restaurant=${branchId}`,
    );
    setResults(response.data);
  };

  const _goBack = payment => {
    navigation.goBack();
    route.params._onOrderPayment(payment);
  };

  useEffect(() => {
    navigation.setOptions({
      title: t('paymentType'),
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    getResults();
  }, [locale]);

  if (!results) {
    return null;
  }

  return (
    <ImageBackground
      source={require('../../../assets/img/back_language.jpg')}
      resizeMode="cover"
      style={{flex: 1, marginTop: 0}}>
      <SafeAreaView>
        <ScrollView>
          {results.map((item, i) => {
            return (
              <TouchableOpacity key={i} onPress={() => _goBack(item)}>
                <ListItem bottomDivider>
                  <Icon name="angle-right" type="font-awesome" />
                  <ListItem.Content>
                    <ListItem.Title style={{fontFamily: 'AndaleMono', fontSize: 18, letterSpacing: -1}}>{item}</ListItem.Title>
                  </ListItem.Content>
                </ListItem>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 20,
  },
});

export default PaymentMethodScreen;
