import React, {useState, useEffect, useCallback, useContext} from 'react';
import {
  View,
  Image,
  Text,
  TextInput,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Pressable,
  ImageBackground,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import LocalizationContext from '../../context/LocalizationContext';
import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import mobileApi from '../../api/mobileApi';
import {Context as UserContext} from '../../context/UserContext';
import {Icon} from 'react-native-elements';
import VersionNumber from 'react-native-version-number';
import {parseDate} from '../../utils';
import {FlatList} from 'react-native-gesture-handler';
import {PromoCard} from '../settings/promo/components';
import Loading from '../../components/Loading';

const PaymentPromoScreen = ({navigation, route}) => {
  const {handleBadge, _onCheckPromo} = route.params;

  const {t, locale} = useContext(LocalizationContext);
  const {state, saveBasket} = useContext(UserContext);
  const {token, memberid: memberId} = state;
  const [code, setCode] = useState('');
  const [totalCount, setTotalCount] = useState();
  const [branch, setBranch] = useState();
  const [promoId, setPromoId] = useState();
  const [promoCode, setPromoCode] = useState();
  const [modalVisible, setModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [allPromoCodes, setAllPromoCodes] = useState([]);

  const fetchAllPromoCodes = useCallback(async branchId => {
    const [promoCodes, userPromoCodes] = await Promise.all([
      fetchPromoCodes(branchId),
      fetchUserPromoCodes(branchId),
    ]);
    const mergedPromoCodes = [...promoCodes, ...userPromoCodes];
    const sortedPromoCodes = mergedPromoCodes.sort((a, b) => {
      const dateA = parseDate(a.expireDate);
      const dateB = parseDate(b.expireDate);

      return dateA - dateB;
    });
    setAllPromoCodes(sortedPromoCodes);
  }, []);

  const fetchPromoCodes = async branchId => {
    try {
      const getPromosPayload = {
        memberId: memberId,
        token: token,
        orderStatus: '1',
        restaurant: branchId,
      };
      const getPromosResponse = await mobileApi.post(
        `/${locale}/getPromosV2`,
        getPromosPayload,
      );

      const promoCodes = getPromosResponse?.data?.promos;
      if (!Array.isArray(promoCodes) || !promoCodes.length) {
        return [];
      }
      const convertedPromoCodes = promoCodes?.map(promoCode => ({
        code: promoCode?.code,
        expireDate: promoCode?.sgt,
        minAmount: promoCode?.minprice,
        price: promoCode?.price,
        promoProduct: promoCode?.product,
        promoType: promoCode?.promotype,
        useCode: promoCode?.usePromo,
        useType: promoCode?.useType,
        shortdesc: promoCode?.shortdesc,
      }));
      return convertedPromoCodes;
    } catch (error) {
      console.log('catch getpromosv2:', error);
      return [];
    }
  };

  const fetchUserPromoCodes = async () => {
    try {
      const userPromoPayload = {
        token: token,
        memberId: memberId,
      };
      const userPromoResponse = await mobileApi.post(
        `/${locale}/getUserPromoV2`,
        userPromoPayload,
      );
      const userPromos = userPromoResponse?.data?.map(userPromo => ({
        ...userPromo,
        isSaved: true,
      }));
      if (!Array.isArray(userPromos) || !userPromos.length) {
        return [];
      }
      return userPromos;
    } catch (error) {
      console.log('catch user promos :', error);
      return [];
    }
  };

  const _readVal = async () => {
    setIsLoading(true);
    try {
      const storedValues = await AsyncStorage.multiGet([
        '@branchId',
        '@promoId',
        '@promoCode',
      ]);
      setBranch(storedValues[0][1]);
      setPromoId(storedValues[1][1]);
      setPromoCode(storedValues[2][1]);
      await fetchAllPromoCodes(storedValues[0][1]);
    } catch (error) {
      console.log('error', error);
    } finally {
      setIsLoading(false);
    }
  };

  const _saveBasket = async item => {
    const data = {
      title: item.name,
      price: parseInt(item.price),
      menu: [],
      id: item.id,
      count: 1,
      optionTotal: 0,
      optionsCount: 0,
      promoId: item.promoId,
      promoCode: item.code,
      promoProductName: item.promoProductName,
      action: 'add',
    };

    await saveBasket({data: data, alert: t('alert')}).then(response => {
      handleBadge(totalCount + 1);
      navigation.goBack();
      _onCheckPromo(item);
    });
  };

  const _promoDiscount = async item => {
    navigation.goBack();
    _onCheckPromo(item);
  };

  const _checkPromo = async code => {
    if (promoCode && promoCode === code) {
      Alert.alert(t('payment').warning, t('alert').promoUse, [
        {
          text: t('payment').ok,
        },
      ]);
      return;
    }

    if (promoId) {
      Alert.alert(t('payment').warning, t('alert').promoUse2, [
        {
          text: t('payment').ok,
        },
      ]);
      return;
    }

    const req = {
      code,
      token: token,
      memberId: memberId,
      locale,
      amount: state.totalAmount,
      restaurant: branch,
      version: VersionNumber.appVersion,
    };

    try {
      const checkPromoResponse = await mobileApi.post(
        `/${locale}/checkPromo`,
        req,
      );
      console.log('checkpromoresponse', checkPromoResponse.data);
      if (checkPromoResponse?.data?.err === 1) {
        Alert.alert(t('payment').warning, checkPromoResponse.data?.msg, [
          {
            text: t('payment').ok,
          },
        ]);
      } else {
        if (checkPromoResponse?.data?.promoProduct > 0) {
          await AsyncStorage.multiSet([
            ['@promoId', checkPromoResponse?.data?.promoid],
            ['@promoCode', code],
            ['@promoPrice', checkPromoResponse?.data?.price],
            ['@promoProductName', checkPromoResponse?.data?.promoProductName],
          ]);
          const item = {
            name: checkPromoResponse?.data?.promoProductName,
            id: checkPromoResponse?.data?.promoProduct,
            code: code,
            price: checkPromoResponse?.data?.price,
            promoId: checkPromoResponse?.data?.promoid,
          };
          _saveBasket(item);
        } else {
          await AsyncStorage.multiSet([
            ['@promoId', checkPromoResponse?.data?.promoid],
            ['@promoCode', code],
            ['@promoPrice', checkPromoResponse?.data?.price],
          ]);
          await AsyncStorage.removeItem('@promoProductName');
          const item = {
            name: '',
            id: '',
            code: code,
            price: checkPromoResponse?.data?.price,
            promoId: checkPromoResponse?.data?.promoid,
          };
          _promoDiscount(item);
        }
      }
    } catch (error) {
      console.log('checkpromo error:', error);
    }
  };

  const renderPromoItem = useCallback(
    ({item: promo}) => {
      return (
        <PromoCard
          promo={promo}
          fetchUserPromos={fetchAllPromoCodes}
          allowedAction="use"
          onPromoUse={promo => _checkPromo(promo.code)}
        />
      );
    },
    [allPromoCodes],
  );

  useEffect(() => {
    navigation.setOptions({
      title: t('promoCode'),
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    _readVal();
    setTotalCount(state.totalCount);
  }, []);

  if (isLoading) return <Loading />;

  return (
    <ImageBackground
      source={require('../../../assets/img/back_language.jpg')}
      resizeMode="cover"
      style={{flex: 1, marginTop: 0}}>
      <SafeAreaView style={{flex: 1}}>
        <View style={styles.topmargin}></View>
        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => {
            setModalVisible(!modalVisible);
          }}>
          <View style={pagestyles.centeredView}>
            <View style={pagestyles.modalView}>
              <Pressable
                onPress={() => setModalVisible(!modalVisible)}
                style={{right: 10, top: 0, position: 'absolute'}}>
                <View style={sc.PrimaryButton_small}>
                  <Text
                    style={[
                      sc.PrimaryButton_text_small,
                      {
                        alignItems: 'flex-start',
                        alignSelf: 'flex-start',
                        lineHeight: 25,
                        width: 90,
                      },
                    ]}>
                    {' '}
                    {t('button.close')}{' '}
                  </Text>
                </View>
              </Pressable>
              <View style={[styles.labelline, styles.mt15]}>
                <Text style={(styles.labelStyle, styles.text18)}>
                  {t('payment').promocode}
                </Text>
              </View>
              <View style={styles.kutu_full}>
                <TextInput
                  value={code}
                  onChangeText={value => setCode(value)}
                  style={[styles.InputStyle, {marginVertical: 20}]}
                  autoCapitalize="characters"
                  autoCorrect={false}
                />
              </View>
              <View
                style={{marginHorizontal: 20, width: '90%', marginBottom: 30}}>
                <TouchableOpacity onPress={() => _checkPromo(code)}>
                  <View style={[styles.button100, styles.bgrenk1]}>
                    <Text style={sc.ThirdButtonButton_text}>
                      {t('button.check')}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        <TouchableOpacity
          onPress={() => {
            setCode('');
            setModalVisible(true);
          }}>
          <View>
            <View style={[styles.button100, styles.bgrenk1]}>
              <Text style={sc.PrimaryButton_text}>
                <Icon
                  name="plus"
                  type="font-awesome"
                  iconStyle={{
                    color: 'white',
                    fontSize: 16,
                    paddingHorizontal: 5,
                    flexDirection: 'row',
                  }}
                />
                {t('offeruse')}
              </Text>
            </View>
          </View>
        </TouchableOpacity>

        <FlatList data={allPromoCodes} renderItem={renderPromoItem} />
      </SafeAreaView>
    </ImageBackground>
  );
};

const pagestyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#00000090',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 30,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    borderRadius: 20,
    padding: 10,
    elevation: 2,
  },
  buttonOpen: {
    backgroundColor: '#F194FF',
  },
  buttonClose: {
    backgroundColor: '#2196F3',
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
  shadow: {
    shadowOffset: {width: 3, height: 3},
    shadowColor: 'black',
    shadowOpacity: 0.4,
    shadowRadius: 8,
    backgroundColor: 'white',
    display: 'flex',
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: 'silver',
    paddingVertical: 10,
    paddingHorizontal: 30,
    width: 250,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default PaymentPromoScreen;
