import React, {useContext, useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  LogBox,
  TextInput,
  Platform,
  Alert,
  Image,
  Dimensions,
  Modal,
  Pressable,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {WebView} from 'react-native-webview';

import {Icon, CheckBox} from 'react-native-elements';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';

import AsyncStorage from '@react-native-async-storage/async-storage';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';

import mobileApi from '../../api/mobileApi';
import {navigate} from '../../navigationRef';
import analytics from '@react-native-firebase/analytics';
import {Adjust, AdjustEvent} from 'react-native-adjust';
import Loading from '../../components/Loading';
import {EntryReminder} from './components';
const {width: viewportWidth, height: viewportHeight} = Dimensions.get('window');

LogBox.ignoreAllLogs();

const PaymentScreen = ({navigation, route}) => {
  const {state, saveOrders, getBadge} = React.useContext(UserContext);
  const {t, locale} = React.useContext(LocalizationContext);
  const now = new Date();
  const {handleBadge} = route.params;
  const [isLoading, setLoading] = useState(false);
  const [alertModal, setAlertModal] = useState(true);
  const [bname, setBname] = useState('');

  const [pagestate, setPageState] = useState({
    orderDate: new Date(),
    point: '',
    userPoint: '',
    address: '',
    addressIndex: '',
    addressLabel: '',
    branchName: '',
    city: '',
    district: '',
    service: '',
    basket: '',
    day: '',
    daySub: '',
    deliveryType: '',
    deliveryTime: '',
    paymentType: '',
    refresh: 0,
    branchId: '',
    addressId: '',
    notes: '',
    serviceset: '',
    sauce: '',
    showWebView: false,
    orderId: '',
    promoCode: null,
    promoId: null,
    promoProductName: null,
    promoPrice: 0,
    totalAmount: 0,
    deliveryPrice: 0,
    cruise: 0,
  });
  const p = t('payment');
  const b = t('button');

  function getAddressIndex(arr, id) {
    return arr.findIndex(obj => obj.id === id);
  }
  function getBranchIndex(arr, id) {
    return arr.findIndex(obj => obj.branchId === id);
  }

  const _onOrderDate = params => {
    setPageState({
      ...pagestate,
      orderDate: params.date,
      deliveryType: params.type,
    });
    _setDeliveryTime(params.date, params.type);
  };

  const _onOrderPayment = payment => {
    setPageState({
      ...pagestate,
      paymentType: payment,
    });
  };

  const _basketConvert = basket => {
    if (!basket) return null;
    const appBasket = [];
    JSON.parse(basket).map(item => {
      appBasket.push({
        itemcode: item.id,
        itemprice: item.price,
        quantity: item.count,
        promoid: item.promoId,
        promocode: item.promoCode,
        menu: item.menu,
        is_recommended: item.is_recommended,
        is_repeat: item.is_repeat,
      });
    });

    return appBasket;
  };

  const _saveOrder = async () => {
    setLoading(true);
    let addressId = '';
    let deliveryTime = '';
    if (pagestate.service === '1') {
      addressId = pagestate.addressId;
      if (pagestate.service == '1') {
        deliveryTime =
          _addByZero(pagestate.orderDate.getFullYear()) +
          '-' +
          _addByZero(parseInt(pagestate.orderDate.getMonth() + 1)) +
          '-' +
          _addByZero(pagestate.orderDate.getDate()) +
          ' ' +
          _addByZero(pagestate.orderDate.getHours()) +
          ':' +
          _addByZero(pagestate.orderDate.getMinutes());
      }
    } else {
      deliveryTime =
        pagestate.orderDate.getHours() < 12
          ? _addByZero(12) + '00'
          : _addByZero(parseInt(pagestate.orderDate.getHours())) +
            '' +
            _addByZero(pagestate.orderDate.getMinutes());
    }

    const data = {
      restaurant: pagestate.branchId,
      memberId: state.memberid,
      token: state.token,
      payment: pagestate.paymentType,
      not: pagestate.notes,
      addressId: addressId,
      servis: pagestate.serviceset ? 0 : 1,
      soyasos: pagestate.sauce ? 0 : 1,
      puan: pagestate.point,
      servistipi: pagestate.service,
      deliveryType: pagestate.deliveryType,
      deliveryPrice: 0,
      teslimsaati: pagestate.deliveryType === 2 ? deliveryTime : '',
      amount: state.totalAmount,
      items: _basketConvert(pagestate.basket),
      codes: pagestate.promoCode ? [pagestate.promoCode] : [],
      platform: Platform.OS,
      locale,
    };
    await saveOrders({data: data, alert: t('alert')}).then(resp => {
      const total = _grandAmount2();

      if (resp.err === 1) {
        setLoading(false);
        Alert.alert(p.warning, resp.msg, [
          {
            text: p.ok,
          },
        ]);
      } else {
        const items = [];
        JSON.parse(pagestate.basket).map(item => {
          items.push({
            item_id: item.id,
            item_name: item.title,
            item_category: item.category,
            quantity: item.count,
            price: parseFloat(item.price),
            item_variant: item.desc,
            item_brand: 'Sushico',
          });
        });

        const firedatas = {
          items,
          payment_type: pagestate.paymentType,
          currency: 'TRY',
          value: parseFloat(state.totalAmount),
        };

        analytics().logEvent('add_payment_info', firedatas);

        if (
          resp.orderId &&
          (pagestate.paymentType === 'Online Kredi Kartı' ||
            pagestate.paymentType === 'Online Credit Card')
        ) {
          setLoading(false);
          if (total > 0) {
            setPageState({
              ...pagestate,
              showWebView: true,
              orderId: resp.orderId,
            });
          } else {
            Alert.alert(p.warning, t('zeroAmount'), [
              {
                text: p.ok,
              },
            ]);
          }
        } else {
          sendOrderMail(
            resp.orderId,
            resp?.data?.fortuneWheel == 1 ? true : false,
          );
        }
      }
    });
  };

  const updateCardOrder = async () => {
    const request = {
      memberId: state.memberid,
      token: state.token,
      orderId: pagestate.orderId,
    };
    await mobileApi.post(`/${locale}/updateCardOrder`, request).then(() => {
      sendOrderMail();
    });
  };

  const sendOrderMail = async (orderId = '', showFortuneWheel) => {
    const request = {
      memberId: state.memberid,
      token: state.token,
      orderId: orderId ? orderId : pagestate.orderId,
    };

    const items = [];

    JSON.parse(pagestate.basket).map(item => {
      items.push({
        item_id: item.id,
        item_name: item.title,
        item_category: item.category,
        quantity: item.count,
        price: parseFloat(item.price),
        item_variant: item.desc,
        item_brand: 'Sushico',
      });
    });

    const firedatas = {
      items,
      restaurant_id: pagestate.branchId,
      city: pagestate.city,
      district: pagestate.district,
      payment_type: pagestate.paymentType,
      currency: 'TRY',
      transaction_id: orderId ? orderId : pagestate.orderId,
      tax: 0,
      shipping: 0,
      value: parseFloat(state.totalAmount),
      coupon: pagestate.promoCode ? pagestate.promoCode : '',
    };

    analytics().logEvent('purchase', firedatas);

    var adjustEvent = new AdjustEvent('6qkmb0');
    adjustEvent.setRevenue(state.totalAmount, 'TRY');
    adjustEvent.setTransactionId(orderId ? orderId : pagestate.orderId);
    Adjust.trackEvent(adjustEvent);

    state.totalAmount = 0;
    await mobileApi.post(`/${locale}/sendOrderMail`, request).then(r => {
      console.log('response of sendordermail', r.data);
      AsyncStorage.multiRemove([
        '@basket',
        '@promoCode',
        '@promoId',
        '@promoPrice',
        '@promoProductName',
      ]).then(() => {
        setLoading(false);
        state.totalAmount = 0;
        setPageState({
          ...pagestate,
          showWebView: false,
        });
        state.totalAmount = 0;
        AsyncStorage.removeItem('@isRecommendedProductsShown').then(() => {
          navigate('ThankYou', {
            orderDetails: {
              announcement: r.data?.announcement,
              showFortuneWheel,
            },
          });
        });
      });
    });
  };
  const onlineOrderView = () => {
    const postdata = {
      orderId: pagestate.orderId,
      amount: _grandAmount2(),
      locale,
      mp: 1,
    };
    return (
      <View
        style={{
          position: 'absolute',
          flex: 1,
          width: '100%',
          height: '100%',
          backgroundColor: 'white',
          zIndex: 1,
        }}>
        <TouchableOpacity
          onPress={() => {
            setPageState({
              ...pagestate,
              showWebView: false,
            });
            Alert.alert(p.warning, p.alert_txt2, [
              {
                text: p.ok,
              },
            ]);
          }}>
          <Text
            style={{
              fontSize: 18,
              paddingVertical: 5,
              marginRight: 10,
              textAlign: 'right',
            }}>
            {t('button.close')}(X)
          </Text>
        </TouchableOpacity>
        <WebView
          source={{
            uri: mobileApi.defaults.baseURL + '/ccPayment.php',
            method: 'POST',
            body: JSON.stringify(postdata),
            headers: {'Content-Type': 'application/json'},
          }}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          scalesPageToFit
          renderError={errorName => {
            Alert.alert(p.warning, p.alert_txt3, [
              {
                text: p.ok,
              },
            ]);
            setPageState({
              ...pagestate,
              showWebView: false,
            });
          }}
          onMessage={event => {
            if (event) {
              const res = JSON.parse(event.nativeEvent.data);
              if (res.success === 1) {
                updateCardOrder();
              } else {
                setPageState({
                  ...pagestate,
                  showWebView: false,
                });
                Alert.alert(p.warning, p.alert_txt4, [
                  {
                    text: p.ok,
                  },
                ]);
              }
            }
          }}
          style={{width: '100%', height: '100%'}}
        />
      </View>
    );
  };

  const _onCheckPromo = item => {
    setPageState({
      ...pagestate,
      promoCode: item.code,
      promoId: item.promoId,
      promoPrice: item.price,
      promoProductName: item.name,
    });
    _getBasket();
    getBadge();
    handleBadge(state.totalCount);
  };

  const _onRemovePormo = async () => {
    await AsyncStorage.getItem('@basket')
      .then(response => {
        const r = response ? JSON.parse(response) : [];
        if (r) {
          r.map((item, i) => {
            if (item.promoCode || item.promoId) {
              r.splice(i, 1);
              AsyncStorage.setItem('@basket', JSON.stringify(r));
              getBadge();
              handleBadge(state.totalCount);
            }
          });
        }
        AsyncStorage.multiRemove([
          '@promoCode',
          '@promoId',
          '@promoPrice',
          '@promoProductName',
        ]);
        setPageState({
          ...pagestate,
          basket: JSON.stringify(r),
          promoCode: null,
          promoId: null,
          promoPrice: 0,
          promoProductName: null,
        });
      })
      .catch(e => {
        //console('asd');
      });
  };

  const _readVal = async () => {
    await AsyncStorage.multiGet([
      '@userdata',
      '@basket',
      '@addressid',
      '@service',
      '@branchId',
      '@branchName',
      '@promoId',
      '@promoCode',
      '@promoPrice',
      '@promoProductName',
      '@deliveryPrice',
      '@regionLimit',
      '@vip',
    ])
      .then(r => {
        // console.log(r);
        const newdate =
          _addByZero(now.getDate()) +
          '.' +
          _addByZero(parseInt(now.getMonth() + 1)) +
          '.' +
          now.getFullYear();

        const newHour =
          now.getHours() < 12
            ? _addByZero(12) + ':00'
            : _addByZero(parseInt(now.getHours() + 1)) + ':00';

        const addressid = r[2][1];
        const branchId = r[4][1];
        const userdata = r[0][1] ? JSON.parse(r[0][1]) : [];
        let addressString = '';
        let addressLabel = '';
        let deliveryTime = '';
        let city = '';
        let district = '';
        let deliveryPrice = 0;
        let totalAmount = state.totalAmount;
        let day = '';
        let daySub = '';
        let cruise = 0;
        if (r[3][1] == '1') {
          const addressIndex = getAddressIndex(userdata.address, addressid);
          const branchdata = userdata.address[addressIndex].branchs;
          const branchIndex = getBranchIndex(branchdata, parseInt(branchId));
          addressLabel = userdata.address[addressIndex].label;
          deliveryTime = branchdata[branchIndex].service;
          city = userdata.address[addressIndex].cityName;
          district = userdata.address[addressIndex].districtName;
          cruise = userdata.cruise;
          console.log(cruise);
          addressString =
            userdata.address[addressIndex].detail +
            ' ' +
            userdata.address[addressIndex].districtName;
          ' ' + userdata.address[addressIndex].cityName;
          day = `${t('today')} ${newdate}`;
          daySub = t('now');
        } else {
          day = `${t('today')}  (${newdate})`;
          daySub = newHour;
          addressString = t('branchPickup');
          addressLabel = r[5][1];
          deliveryTime = '';
        }

        deliveryPrice = 0;
        setPageState({
          ...pagestate,
          service: r[3][1],
          basket: r[1][1],
          day: day,
          daySub: daySub,
          addressId: addressid,
          address: addressString,
          district: district,
          city: city,
          branchId: branchId,
          deliveryTime: deliveryTime,
          userPoint: userdata.point,
          addressLabel: addressLabel,
          branchName: r[5][1],
          orderDate: now,
          promoId: r[6][1],
          promoCode: r[7][1],
          promoPrice: r[8][1] ? r[8][1] : 0,
          promoProductName: r[9][1],
          vip: r[12][1],
          totalAmount: state.totalAmount,
          deliveryPrice: 0,
          cruise: cruise,
        });
      })
      .catch(err => {});
  };
  const _getBasket = async () => {
    await AsyncStorage.multiGet([
      '@basket',
      '@promoId',
      '@promoCode',
      '@promoPrice',
      '@promoProductName',
    ])
      .then(r => {
        setPageState({
          ...pagestate,
          basket: r[0][1],
          promoId: r[1][1],
          promoCode: r[2][1],
          promoPrice: r[3][1] ? r[3][1] : 0,
          promoProductName: r[4][1],
        });
      })
      .catch(err => {});
  };
  const _grandAmount2 = () => {
    if (pagestate.promoProductName) {
      return state.totalAmount - pagestate.point >= 0
        ? state.totalAmount - pagestate.point
        : state.totalAmount;
    } else {
      return state.totalAmount -
        pagestate.point -
        parseInt(pagestate.promoPrice) >=
        0
        ? state.totalAmount - pagestate.point - parseInt(pagestate.promoPrice)
        : state.totalAmount;
    }
  };

  const _grandAmount = () => {
    let err = 0;
    if (parseInt(pagestate.promoPrice) > 0 && pagestate.point > 0) {
      Alert.alert(p.warning, t('qr.pointErr8'), [
        {
          text: p.ok,
          onPress: () => {
            pagestate.promoId ? _onRemovePormo() : null;
            setPageState({
              ...pagestate,
              point: '',
              promoCode: null,
              promoId: null,
              promoPrice: 0,
              promoProductName: null,
            });
            err = 1;
          },
        },
      ]);
    } else if (pagestate.promoProductName && err == 0 && pagestate.point > 0) {
      Alert.alert(p.warning, t('qr.pointErr8'), [
        {
          text: p.ok,
          onPress: () => {
            pagestate.promoId ? _onRemovePormo() : null;
            setPageState({
              ...pagestate,
              point: '',
              promoCode: null,
              promoId: null,
              promoPrice: 0,
              promoProductName: null,
            });
            err = 1;
          },
        },
      ]);
    }
    if (pagestate.vip > 0 && err == 0) {
      if (
        parseInt(pagestate.point) > parseInt(state.totalAmount) &&
        parseInt(state.totalAmount) > 0
      ) {
        Alert.alert(p.warning, t('qr.pointErr7'), [
          {
            text: p.ok,
            onPress: () => {
              pagestate.promoId ? _onRemovePormo() : null;
              setPageState({
                ...pagestate,
                point: '',
                promoCode: null,
                promoId: null,
                promoPrice: 0,
                promoProductName: null,
              });
              err = 1;
            },
          },
        ]);
      } else if (parseInt(pagestate.point) > parseInt(pagestate.userPoint)) {
        Alert.alert(t('payment').warning, t('qr.pointErr2'), [
          {
            text: t('payment').ok,
            onPress: () => {
              pagestate.promoId ? _onRemovePormo() : null;
              setPageState({
                ...pagestate,
                point: '',
                promoCode: null,
                promoId: null,
                promoPrice: 0,
                promoProductName: null,
              });
              err = 1;
            },
          },
        ]);
      }
    }
    if (pagestate.vip == 0 && err == 0) {
      if (
        parseInt(pagestate.point) > parseInt(state.totalAmount) / 2 &&
        parseInt(state.totalAmount) > 0
      ) {
        Alert.alert(p.warning, p.alert_txt1, [
          {
            text: p.ok,
            onPress: () => {
              pagestate.promoId ? _onRemovePormo() : null;
              setPageState({
                ...pagestate,
                point: '',
                promoCode: null,
                promoId: null,
                promoPrice: 0,
                promoProductName: null,
              });
              err = 1;
            },
          },
        ]);
      }
    }
    if (err == 0) {
      if (pagestate.promoProductName) {
        setPageState({
          ...pagestate,
          totalAmount:
            parseInt(state.totalAmount) - parseInt(pagestate.point) > 0
              ? parseInt(state.totalAmount) - parseInt(pagestate.point)
              : parseInt(state.totalAmount),
        });
      } else {
        setPageState({
          ...pagestate,
          totalAmount:
            parseInt(state.totalAmount) -
              parseInt(pagestate.point) -
              parseInt(pagestate.promoPrice) >
            0
              ? parseInt(state.totalAmount) -
                parseInt(pagestate.point) -
                parseInt(pagestate.promoPrice)
              : parseInt(state.totalAmount),
        });
      }
    }
  };

  const _addByZero = n => {
    return n < 10 ? '0' + n : n;
  };

  const _setDeliveryTime = (date, type) => {
    if (pagestate.service === '1') {
      if (now.getDate() === date.getDate() && type === 1) {
        const newdate =
          _addByZero(now.getDate()) +
          '.' +
          _addByZero(parseInt(now.getMonth() + 1)) +
          '.' +
          now.getFullYear();
        setPageState({
          ...pagestate,
          refresh: 1,
          day: `${t('today')}  (${newdate})`,
          daySub: `${t('now')}  ~ ${pagestate.deliveryTime}`,
          deliveryType: type,
        });
      } else {
        const newdate =
          _addByZero(date.getDate()) +
          '.' +
          _addByZero(parseInt(date.getMonth() + 1)) +
          '.' +
          date.getFullYear() +
          ' ' +
          _addByZero(date.getHours()) +
          ':' +
          _addByZero(date.getMinutes());
        setPageState({
          ...pagestate,
          day: newdate,
          daySub: t('futureDate'),
          orderDate: date,
          deliveryType: type,
        });
      }
    } else {
      const newdate =
        _addByZero(date.getDate()) +
        '.' +
        _addByZero(parseInt(date.getMonth() + 1)) +
        '.' +
        date.getFullYear() +
        ' ' +
        _addByZero(date.getHours()) +
        ':' +
        _addByZero(date.getMinutes());
      setPageState({
        ...pagestate,
        day: newdate,
        daySub: t('payment').pickup,
        orderDate: date,
        deliveryType: type,
      });
    }
  };

  useEffect(() => {
    //console.log('total', pagestate.totalAmount, state.totalAmount);
    _grandAmount();
  }, [state.totalAmount, pagestate.point, pagestate.promoCode]);

  useEffect(() => {
    navigation.setOptions({
      title: t('paymentScreen'),
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    _readVal();
  }, []);

  if (!pagestate.orderDate) {
    return null;
  }

  if (isLoading) {
    return <Loading />;
  }

  return (
    <View style={styles.container}>
      {pagestate.showWebView && onlineOrderView()}
      <KeyboardAwareScrollView style={pagestyles.pagecontainer}>
        <View style={styles.topmargin}></View>

        <View style={styles.labelline_payment}>
          <Text style={styles.labelStyle}>{p.delivery}</Text>
        </View>

        <View style={[styles.listeleme, {marginHorizontal: 0}]}>
          <View style={styles.kutu_full}>
            <View style={styles.listeleme_sol}>
              <Icon name="bag" type="simple-line-icon" iconStyle="" />
            </View>
            <View style={styles.listeleme_orta}>
              <Text style={[styles.SubeAd, styles.Txt_renk2, styles.black]}>
                {' '}
                {pagestate.service === '1'
                  ? t('payment').deliveryText
                  : t('payment').pickupText}
              </Text>
              <Text style={[styles.SubeAd, styles.Txt_renk1]}>
                {' '}
                {pagestate.addressLabel}
              </Text>
              <Text numberOfLines={1} style={[styles.black]}>
                {' '}
                {pagestate.address}
              </Text>
            </View>
          </View>
        </View>

        <Modal
          animationType="slide"
          transparent={true}
          visible={alertModal}
          statusBarTranslucent={true}
          onRequestClose={() => {
            setSauceModal(!alertModal);
          }}>
          <View style={pagestyles.centeredView}>
            <View style={[pagestyles.modalView, {width: '80%'}]}>
              <View
                style={[
                  styles.anasayfa_element_txt,
                  {width: '100%', flexDirection: 'column'},
                ]}>
                <Text
                  style={[styles.anasayfa_element_yazi, {textAlign: 'center'}]}>
                  {t('payment').modal_text1}
                </Text>
                <Text
                  style={[
                    styles.anasayfa_element_yazi,
                    {textAlign: 'center', fontSize: 22, color: 'red'},
                  ]}>
                  {pagestate.branchName} {'\n'}
                </Text>
                <Text
                  style={[styles.anasayfa_element_yazi, {textAlign: 'center'}]}>
                  {t('payment').modal_text2}
                </Text>
                <Text
                  style={[
                    styles.anasayfa_element_yazi,
                    {textAlign: 'center', fontSize: 22, color: 'red'},
                  ]}>
                  {' '}
                  {pagestate.service === '1'
                    ? t('payment').deliveryText
                    : t('payment').pickupText}
                </Text>
              </View>
              <Pressable onPress={() => setAlertModal(!alertModal)}>
                <View style={sc.PrimaryButton}>
                  <Text style={sc.PrimaryButton_text}>{t('payment.ok')}</Text>
                </View>
              </Pressable>
            </View>
          </View>
        </Modal>

        <View style={[styles.topmargin]}>
          <Text style={styles.labelStyle}> {p.deliverytime}</Text>
        </View>

        <TouchableOpacity
          style={[styles.listeleme, {marginHorizontal: 0}]}
          onPress={() =>
            navigate('PaymentDate', {
              _onOrderDate: _onOrderDate,
              service: pagestate.service,
            })
          }>
          <View style={styles.kutu_full}>
            <View style={styles.listeleme_sol}>
              <Icon name="clock" type="simple-line-icon" />
            </View>
            <View style={styles.listeleme_orta}>
              <Text style={[styles.OdemeBaslik, styles.black]}>
                {pagestate.day}
              </Text>
              <Text style={[styles.mt10, styles.black]}>
                {pagestate.daySub}
              </Text>
            </View>
            <View style={styles.listeleme_sag}>
              <Icon
                name="angle-right"
                type="font-awesome"
                iconStyle={sc.RightArrow}
              />
            </View>
          </View>
        </TouchableOpacity>

        <View style={[styles.topmargin]}>
          <Text style={styles.labelStyle}> {p.promocode}</Text>
        </View>

        <TouchableOpacity
          style={[styles.listeleme, {marginHorizontal: 0}]}
          onPress={() =>
            pagestate.point
              ? Alert.alert(p.warning, t('qr.pointErr10'))
              : navigate('PaymentPromo', {_onCheckPromo: _onCheckPromo})
          }>
          <View style={styles.kutu_full}>
            <View style={styles.listeleme_sol}>
              <Icon name="options" type="simple-line-icon" />
            </View>
            <View style={styles.listeleme_orta}>
              <Text style={[styles.OdemeBaslik, styles.black]}>
                {' '}
                {p.promocodes}
              </Text>
              {pagestate.promoCode && pagestate.point < 1 && (
                <Text style={[styles.mt10, {fontSize: 18, color: '#29850d'}]}>
                  {pagestate.promoCode}{' '}
                  <TouchableOpacity onPress={() => _onRemovePormo()}>
                    <Icon
                      name="trash-o"
                      type="font-awesome"
                      iconStyle={{fontSize: 20}}
                    />
                  </TouchableOpacity>
                  {'\n '}
                  {pagestate.promoProductName ? (
                    <Text style={[styles.text16, styles.black]}>
                      {pagestate.promoProductName} - {pagestate.promoPrice}{' '}
                      {t('currency')}
                    </Text>
                  ) : (
                    <Text style={[styles.text12, styles.black]}>
                      - {pagestate.promoPrice} {t('currency')}
                    </Text>
                  )}
                </Text>
              )}
            </View>
            <View style={styles.listeleme_sag}>
              <Icon
                name="angle-right"
                type="font-awesome"
                iconStyle={sc.RightArrow}
              />
            </View>
          </View>
        </TouchableOpacity>

        <View style={[styles.topmargin]}>
          <Text style={styles.labelStyle}> {p.usepoint} </Text>
        </View>
        <View style={[styles.listeleme, {marginHorizontal: 0}]}>
          <View style={styles.kutu_full}>
            <View style={styles.listeleme_orta}>
              <Text style={[styles.SubeAdres, styles.black]}>
                {
                  <Text
                    style={[
                      styles.text20,
                      styles.Txt_renk1,
                      styles.ozelfont_medium,
                      styles.bold,
                      {letterSpacing: -0}
                    ]}>
                    {pagestate.userPoint}{' '}
                  </Text>
                }
                {p.usepoint_txt1} {'\n'} {p.usepoint_txt2}
              </Text>
            </View>
          </View>
          <View style={styles.kutu_full}>
            <View style={[styles.listeleme_orta]}>
              <Text
                style={[
                  styles.text14,
                  styles.darkgray,
                  styles.ozelfont_medium,
                  styles.mt10,
                  {letterSpacing: -1}
                ]}>
                {p.usepoint_txt3}
              </Text>
            </View>
            <View
              style={[
                styles.listeleme_sag2,
                {
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 0.8,
                  gap: 8,
                  paddingHorizontal: 8,
                },
              ]}>
              <Pressable
                onPress={() => {
                  pagestate.promoCode &&
                    Alert.alert(p.warning, t('qr.pointErr9'));
                }}
                style={{flex: 1}}>
                <TextInput
                  style={[
                    styles.InputStyle,
                    {
                      flex: 1,
                      paddingLeft: 0,
                      textAlign: 'center',
                      fontSize: 20,
                      backgroundColor: pagestate.promoCode
                        ? '#e3e3e3'
                        : 'white',
                      borderWidth: 1,
                      borderColor: '#cccccc',
                    },
                  ]}
                  editable={pagestate.promoCode ? false : true}
                  autoCapitalize="none"
                  autoCorrect={false}
                  keyboardType="numeric"
                  pointerEvents={pagestate.promoCode ? 'none' : 'auto'} // Android için kritik
                  value={pagestate.point}
                  onChangeText={value =>
                    setPageState({
                      ...pagestate,
                      point: value.replace(/\D/g, ''),
                    })
                  }
                />
              </Pressable>

              <Pressable
                style={{padding: 4, display: pagestate.point ? 'flex' : 'none'}}
                onPress={() => setPageState({...pagestate, point: ''})}>
                <Icon name="trash-o" type="font-awesome" />
              </Pressable>
            </View>
          </View>
        </View>

        <View style={[styles.topmargin]}>
          <Text style={styles.labelStyle}>{p.paymentmethod}</Text>
        </View>
        <TouchableOpacity
          style={[styles.listeleme, {marginHorizontal: 0}]}
          onPress={() =>
            navigate('PaymentMethod', {
              branchId: pagestate.branchId,
              _onOrderPayment: _onOrderPayment,
            })
          }>
          <View style={styles.kutu_full}>
            <View style={styles.listeleme_sol}>
              <Icon name="wallet" type="simple-line-icon" />
            </View>
            <View style={styles.listeleme_orta}>
              <Text style={[styles.OdemeBaslik, styles.black]}>
                {pagestate.paymentType
                  ? pagestate.paymentType
                  : t('paymentType')}
              </Text>
            </View>
            <View style={styles.listeleme_sag}>
              <Icon
                name="angle-right"
                type="font-awesome"
                iconStyle={sc.RightArrow}
              />
            </View>
          </View>
        </TouchableOpacity>

        <View style={[styles.topmargin]}>
          <Text style={styles.labelStyle}>{p.ordernote}</Text>
        </View>
        <View style={{width: '100%', marginTop: 10}}>
          <TextInput
            style={styles.InputStylePlaceholder}
            placeholder={`${p.notes}  ${p.maxchrc}`}
            autoCorrect={false}
            placeholderTextColor="#000"
            value={pagestate.notes}
            multiline={true}
            maxLength={150}
            onChangeText={value => setPageState({...pagestate, notes: value})}
          />
        </View>
        <View
          style={{
            width: '100%',
          }}>
          <CheckBox
            left
            title={p.sauce}
            checkedIcon="check"
            uncheckedIcon="circle-o"
            checkedColor="#70c92f"
            textStyle={{fontWeight: 'normal', fontFamily: 'AndaleMono',  letterSpacing: -1}}
            checked={pagestate.sauce}
            onPress={() =>
              setPageState({...pagestate, sauce: !pagestate.sauce})
            }
          />
        </View>
        <View
          style={{
            width: '100%',
          }}>
          <CheckBox
            left
            title={p.set}
            checkedIcon="check"
            uncheckedIcon="circle-o"
            checkedColor="#70c92f"
            textStyle={{fontWeight: 'normal', fontFamily: 'AndaleMono',  letterSpacing: -1}}
            checked={pagestate.serviceset}
            onPress={() =>
              setPageState({...pagestate, serviceset: !pagestate.serviceset})
            }
          />
        </View>
      </KeyboardAwareScrollView>
      {pagestate?.cruise == 1 && (
        <EntryReminder
          cartTotal={
            state.totalAmount -
            pagestate.point -
            parseInt(pagestate?.promoPrice)
          }
        />
      )}

      <View style={styles.fiyat_block}>
        <View style={styles.fiyat_text}>
          <Text style={[styles.text22, styles.ozelfont_bold, styles.black]}>
            {pagestate.promoProductName
              ? state.totalAmount - pagestate.point
              : state.totalAmount -
                pagestate.point -
                parseInt(pagestate.promoPrice)}
            <Text style={{color: 'black'}}> {t('currency')}</Text>
          </Text>
        </View>

        <TouchableOpacity onPress={() => _saveOrder()}>
          <View style={[styles.fiyat_btn, {backgroundColor: '#000', width: 200}]}>
            <Text style={[sc.PrimaryButton_text, styles.ozelfont_medium]}>
              {b.complete}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const pagestyles = StyleSheet.create({
  pagecontainer: {backgroundColor: '#ffffff', flex: 1, paddingHorizontal: 15},
  bottomView: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    //backgroundColor: '#00000099',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  bottomModalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    height: viewportHeight * 0.5,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    borderRadius: 10,
    padding: 10,
    elevation: 2,
  },
  buttonOpen: {
    backgroundColor: '#F194FF',
  },
  buttonClose: {
    backgroundColor: '#2196F3',
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontFamily: 'IBMPlexSans-Light',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
});

export default PaymentScreen;
