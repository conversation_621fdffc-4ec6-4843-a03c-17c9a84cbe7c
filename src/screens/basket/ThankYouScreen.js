import React, {useContext, useEffect, useState} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Linking,
  ScrollView,
  Pressable,
} from 'react-native';
import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';

import {Context as UserContext} from '../../context/UserContext';
import LocalizationContext from '../../context/LocalizationContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {CommonActions, useNavigation, useRoute} from '@react-navigation/native';
import mobileApi from '../../api/mobileApi';
import FortuneWheelModal from '../../components/FortuneWheelModal';
import {navigationRef} from '../../navigationRef';
const ThankYouScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const orderDetails = route?.params?.orderDetails;
  const {t, locale} = useContext(LocalizationContext);
  const l = t('payment');
  const {state, getBadge, setBranch} = useContext(UserContext);

  const [isWheelVisible, setIsWheelVisible] = useState(false);

  useEffect(() => {
    navigation.setOptions({
      title: l.thankyouTitle,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
      headerLeft: () => null,
    });
    AsyncStorage.multiRemove([
      '@basket',
      '@promoCode',
      '@promoId',
      '@promoPrice',
      '@promoProductName',
    ]);
    setBranch('', '');
    getBadge();
    setTimeout(() => {
      setIsWheelVisible(orderDetails?.showFortuneWheel);
    }, 1000);
  }, []);

  return (
    <ScrollView style={styles.container} bounces={false}>
      <View
        style={[
          styles.container,
          {
            paddingTop: 10,
            alignItems: 'center',
            textAlign: 'center',
            padding: 40,
          },
        ]}>
        <View style={{marginVertical: 20, alignItems: 'center'}}>
          <Image
            source={require('../../../assets/img/sojubar-logo.png')}
            style={{height: 100, resizeMode: 'contain'}}
          />
        </View>
        <Image
          source={require('../../../assets/img/icon_check.png')}
          style={{height: 32, width: 32, marginLeft: 10, resizeMode: 'contain'}}
        />
        <Text style={[styles.text20, {color: '#29850d', marginTop: 20, fontFamily: 'FrontageCondensed-Regular'}]}>
          {l.thankyou_txt0}
        </Text>
        <Text style={[styles.text32, styles.Txt_renk5, {fontFamily: 'FrontageCondensed-Regular'}]}>{l.thankyou_txt1}</Text>

        <Image
          source={require('../../../assets/img/icon_home_degerlendirme.png')}
          style={{
            height: 32,
            width: 32,
            marginLeft: 10,
            marginTop: 20,
            resizeMode: 'contain',
          }}
        />
        <Text style={[styles.text20, {textAlign: 'center', color: 'black',fontFamily: 'AndaleMono'}]}>
          {l.thankyou_txt3}{' '}
        </Text>

        <Image
          source={require('../../../assets/img/icon_home_kurye.png')}
          style={{
            height: 32,
            width: 32,
            marginLeft: 10,
            marginTop: 20,
            resizeMode: 'contain',
          }}
        />
        <Text style={[styles.text20, {textAlign: 'center', color: 'black', fontFamily: 'AndaleMono'}]}>
          {l.thankyou_txt2}
        </Text>

        {/* {orderDetails?.showFortuneWheel && (
          <Pressable
            style={[{marginTop: 20}, sc.PrimaryButton]}
            onPress={() => setIsWheelVisible(true)}>
            <Text style={[sc.PrimaryButton_text, styles.ozelfont_medium]}>
              {t('settingsMenu.Coupons.showWheel')}
            </Text>
          </Pressable>
        )} */}

        <View
          style={{
            marginTop: 20,
            gap: 20,
            width: '100%',
          }}>
          {orderDetails?.announcement?.map((announcementDetail, index) => (
            <View
              key={index}
              style={{
                gap: 20,
                borderWidth: 1,
                borderRadius: 5,
                borderColor: '#ee3124',
                padding: 15,
              }}>
              {announcementDetail?.msgTitle && (
                <Text
                  style={[
                    styles.text22,
                    {
                      textAlign: 'center',
                      color: '#d40b11',
                      fontWeight: 'bold',
                    },
                  ]}>
                  {announcementDetail?.msgTitle}
                </Text>
              )}

              <Text
                style={[
                  styles.text20,
                  {
                    textAlign: 'center',
                    color: 'black',
                    fontWeight: '500',
                  },
                ]}>
                {announcementDetail?.msg}
              </Text>
              <TouchableOpacity
                onPress={() => Linking.openURL(announcementDetail?.url)}>
                <Text
                  style={[
                    styles.text16,
                    {
                      textAlign: 'center',
                      color: '#ee3124',
                      textDecorationLine: 'underline',
                    },
                  ]}>
                  {announcementDetail?.urlText}
                </Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </View>
      <FortuneWheelModal
        source={`${mobileApi.defaults.webURL}/fortuneWheel.php?token=${state?.token}`}
        isVisible={isWheelVisible}
        setIsVisible={setIsWheelVisible}
        onPress={() => {
          navigationRef?.current?.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [
                {
                  name: 'Settings',
                  state: {
                    index: 1,
                    routes: [{name: 'SettingsMain', name: 'SettingsCoupons'}],
                  },
                },
              ],
            }),
          );
          setIsWheelVisible(false);
        }}
      />
    </ScrollView>
  );
};

export default ThankYouScreen;
