import React, {memo, useContext, useRef} from 'react';
import {
  Dimensions,
  Linking,
  PixelRatio,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {Icon} from 'react-native-elements';
import Animated, {
  interpolate,
  measure,
  runOnUI,
  useAnimatedRef,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import {useNavigation} from '@react-navigation/native';
import LocalizationContext from '../../../context/LocalizationContext';
const {width} = Dimensions.get('screen');

const entryTreshold = 2000;
const baseSpringConfig = {
  mass: 1,
  overshootClamping: false,
  restDisplacementThreshold: 0.01,
  restSpeedThreshold: 2,
  damping: 15,
  stiffness: 100,
};
const rotationSpringConfig = {
  duration: 500,
};
const guidelineBaseWidth = 430;
const EntryReminder = ({cartTotal}) => {
  const {t, locale} = useContext(LocalizationContext);

  const navigation = useNavigation();
  const reminderRef = useAnimatedRef();
  const reminderHeight = useSharedValue(0);
  const rotation = useSharedValue(0);
  const hasMeasured = useRef(false);

  const handleLayout = React.useCallback(event => {
    if (!hasMeasured.current) {
      const {height} = event.nativeEvent.layout;
      reminderHeight.value = height;
      hasMeasured.current = true;
      // runOnUI(() => {
      //   'worklet';
      //   reminderHeight.value = withSpring(measure(reminderRef).height, {
      //     duration: 1000,
      //   });
      //   rotation.value = withSpring(0, rotationSpringConfig);
      // })();
    }
  }, []);

  const amountFormater = amount => {
    const regex = /(\d)(?=(\d{3})+(?!\d))/g;
    const fixedAmount = amount.toFixed(2);
    const replacedAmount = fixedAmount.replace(regex, '$1,');
    return replacedAmount;
  };

  const calculateReward = React.useMemo(() => {
    const totalSpend = Math.round(cartTotal * 100) / 100;
    const entryCount = Math.floor(totalSpend / entryTreshold);
    console.log('en', entryCount);
    const currentThresholdSpend = totalSpend % entryTreshold;
    const entryRemainder = amountFormater(
      entryTreshold - currentThresholdSpend,
    );
    return {entryCount, entryRemainder};
  }, [cartTotal]);

  const handleAccordionPress = () => {
    if (reminderHeight.value == 0) {
      runOnUI(() => {
        'worklet';
        reminderHeight.value = withSpring(
          measure(reminderRef).height,
          baseSpringConfig,
        );
        rotation.value = withSpring(0, rotationSpringConfig);
      })();
    } else {
      reminderHeight.value = withTiming(0, {duration: 500});
      rotation.value = withSpring(1, rotationSpringConfig);
    }
  };

  const animatedContainerStyle = useAnimatedStyle(() => {
    return {height: reminderHeight.value};
  });

  const animatedIconStyle = useAnimatedStyle(() => {
    const degree = interpolate(rotation.value, [0, 1], [0, 180]);
    return {
      transform: [{rotate: `${degree}deg`}],
    };
  });

  return (
    <View style={remainderStyles.container}>
      <Pressable
        onPress={handleAccordionPress}
        style={remainderStyles.toggleBtn}>
        <Text style={remainderStyles.titleText}>
          {t('basket.campaign.campaignTitle')}
        </Text>
        <Animated.View style={animatedIconStyle}>
          <Icon
            name="chevron-down"
            type="feather"
            iconStyle={{color: 'white', fontSize: 24}}
          />
        </Animated.View>
      </Pressable>

      <Animated.View style={animatedContainerStyle}>
        <Animated.View
          onLayout={handleLayout}
          ref={reminderRef}
          style={remainderStyles.contentContainer}>
          <Text style={remainderStyles.contentText}>
            {locale === 'tr'
              ? `Sepetinize ${
                  calculateReward.entryRemainder
                } TL'lik ürün daha ekleyerek siparişinizi tamamladığınızda${
                  calculateReward.entryCount ? ' ek ' : ''
                } 1 çekiliş hakkı ${
                  calculateReward.entryCount ? 'daha ' : ''
                } kazanacaksınız. İndirim ve puan kullanımı sonrasında, ödediğiniz tutarın 2.000 TL veya üzerinde olması gerekmektedir.`
              : `Add products worth ${
                  calculateReward.entryRemainder
                } TL to your cart, and when you complete your order, you will earn${
                  calculateReward.entryCount ? ' an additional ' : ' '
                }1 raffle ticket${
                  calculateReward.entryCount ? ' more' : ''
                }. After applying discounts and points, the total amount paid must be 2,000 TL or more.`}
          </Text>
          <View style={remainderStyles.linkContainer}>
            <TouchableOpacity
              onPress={() =>
                Linking.openURL('https://www.sushico.com.tr/tr/cekilis.php')
              }>
              <Text style={remainderStyles.linkText}>
                {t('basket.campaign.campaignInfo')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                // navigate('Orders', {screen: 'MenuList', params: {menu: 0}});
                navigation.goBack();
              }}
              style={remainderStyles.backToMenuBtn}>
              <Text style={remainderStyles.backToMenuText}>
                {t('basket.campaign.backToBasket')}
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </Animated.View>
    </View>
  );
};
export default memo(EntryReminder);
const normalize = size => {
  const scale = width / guidelineBaseWidth;
  const newSize = scale < 1 ? size * scale * 0.9 : size * scale;
  const fontSize = Math.round(PixelRatio.roundToNearestPixel(newSize));
  return fontSize;
};

const remainderStyles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    marginBottom: 10,
  },
  toggleBtn: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#ee3124',
    paddingVertical: 10,
    paddingLeft: 15,
    paddingRight: 10,
  },
  titleText: {
    color: '#fff',
    fontSize: normalize(20),
    fontWeight: 'bold',
    fontFamily: 'IBMPlexSans-Medium',
  },
  contentContainer: {
    position: 'absolute',
    width: '100%',
    top: 0,
    gap: 8,
    backgroundColor: '#ee3124',
    paddingRight: 10,
    paddingLeft: 15,
  },
  contentText: {
    fontSize: normalize(16),
    color: '#fff',
    fontWeight: '400',
    fontFamily: 'IBMPlexSans-Medium',
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: 15,
  },
  linkText: {
    fontSize: normalize(14),
    color: '#fff',
    textDecorationLine: 'underline',
  },
  backToMenuBtn: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 17,
    paddingVertical: 10,
    borderRadius: 10,
    backgroundColor: '#eee',
  },
  backToMenuText: {
    color: '#111',
    fontSize: normalize(16),
    fontFamily: 'IBMPlexSans-Regular',
  },
});
