import React, {useEffect, useContext, useState, useCallback} from 'react';
import LocalizationContext from '../../../../context/LocalizationContext';
import {Context as UserContext} from '../../../../context/UserContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  FlatList,
  Modal,
  StyleSheet,
  Text,
  View,
  Dimensions,
  Alert,
} from 'react-native';
import {TouchableOpacity} from 'react-native';
import {Icon} from 'react-native-elements';
import mobileApi from '../../../../api/mobileApi';
import ProductItem from './ProductItem';
import styles from '../../../../stylesheets/styles';

const {width, height} = Dimensions.get('screen');
const ModalRecommendedProducts = ({
  basket = [],
  showRecommendedProducts,
  setShowRecommendedProducts,
  _basketCheck = callback => {},
  _getBadge = callback => {},
}) => {
  const {saveBasket} = useContext(UserContext);
  const {t, locale} = useContext(LocalizationContext);
  const [recommendedProducts, setRecommendedProducts] = useState({
    data: [],
    title: '',
  });
  const [selectedProductOptions, setSelectedProductOptions] = useState({});
  const [previouslySelectedProduct, setPreviouslySelectedProduct] = useState(
    {},
  );
  const [showOptions, setShowOptions] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  useEffect(() => {
    const fetchRecommendedProducts = async () => {
      const restaurant = await AsyncStorage.getItem('@branchId');
      const response = await mobileApi.get(
        `/${locale}/getRecommendedProducts?restaurant=${restaurant}`,
      );
      setRecommendedProducts({
        data: response?.data?.data || [],
        title: response?.data?.title || '',
      });
    };
    fetchRecommendedProducts();
  }, [locale]);

  useEffect(() => {
    console.log('showRecommendedProducts', showRecommendedProducts);
  }, [showRecommendedProducts]);

  const _saveBasket = data => {
    saveBasket({data, alert: t('alert')}).then(() => {
      _basketCheck();
      _getBadge();
      setShowOptions(false);
      setTimeout(() => {
        setShowRecommendedProducts(false);
        setShowSuccessModal(true);
        setTimeout(() => {
          setShowRecommendedProducts(true);
          setShowSuccessModal(false);
          setSelectedProductOptions({});
          setPreviouslySelectedProduct({});
        }, 1500);
      }, 500);
    });
  };

  const _calculateBasketTotal = newMenu => {
    let optionPriceTotal = 0;
    Object.keys(newMenu).map(item => {
      optionPriceTotal =
        parseFloat(optionPriceTotal) + parseFloat(newMenu[item]?.itemprice);
    });
    return parseFloat(optionPriceTotal);
  };

  const _addRecommendedProduct = useCallback(
    (selectedProduct, menuOptions) => {
      let newMenu = [];
      let updatedOptions = [];
      const optionsCount = selectedProduct?.option?.length || 0;
      let data = {
        id: selectedProduct.productId,
        title: selectedProduct.name,
        price: selectedProduct.price,
        desc: selectedProduct.desc,
        menu: newMenu,
        count: 1,
        category: recommendedProducts.title,
        optionTotal: _calculateBasketTotal(newMenu),
        optionsCount: optionsCount,
        sos: '',
        action: 'add',
        promoId: '',
        promoCode: '',
        is_recommended: 1,
      };

      if (previouslySelectedProduct?.productId === selectedProduct?.productId) {
        if (selectedProduct?.option?.length) {
          updatedOptions = {
            ...selectedProductOptions,
            [menuOptions.optionid]: {
              itemcode: menuOptions.itemcode,
              itemname: menuOptions.itemname,
              itemprice: menuOptions.itemprice,
            },
          };
          const updatedOptionsArray = Object.values(updatedOptions);
          updatedOptionsArray.forEach(option => {
            newMenu.push(option);
          });
          if (selectedProduct?.option?.length === updatedOptionsArray?.length) {
            data = {
              ...data,
              optionTotal: _calculateBasketTotal(newMenu),
            };
            return _saveBasket(data);
          }
          setSelectedProductOptions(updatedOptions);
          return setPreviouslySelectedProduct(selectedProduct);
        }
        return _saveBasket(data);
      }
      if (previouslySelectedProduct?.option?.length) {
        Alert.alert(
          t('basket.recommendedProducts.alert', {
            productName: previouslySelectedProduct.name,
          }),
        );
        return;
      }
      if (selectedProduct?.option?.length) {
        updatedOptions = {
          ...selectedProductOptions,
          [menuOptions.optionid]: {
            itemcode: menuOptions.itemcode,
            itemname: menuOptions.itemname,
            itemprice: menuOptions.itemprice,
          },
        };
        setSelectedProductOptions(updatedOptions);
        const updatedOptionsArray = Object.values(updatedOptions);
        updatedOptionsArray.forEach(option => {
          newMenu.push(option);
        });
        if (selectedProduct?.option?.length == 1) {
          data = {
            ...data,
            optionTotal: _calculateBasketTotal(newMenu),
          };
          return _saveBasket(data);
        }
        return setPreviouslySelectedProduct(selectedProduct);
      }
      return _saveBasket(data);
    },
    [
      selectedProductOptions,
      saveBasket,
      t,
      previouslySelectedProduct,
      _basketCheck,
      _getBadge,
    ],
  );

  if (showSuccessModal)
    return (
      <Modal animationType="fade" transparent={true} visible={showSuccessModal}>
        <View style={pagestyles.centeredView}>
          <View
            style={[
              pagestyles.modalView,
              styles.ozelfont_medium,
              styles.bgrenk3,
              {alignContent: 'center'},
            ]}>
            <Text
              style={[
                styles.text20,
                styles.ozelfont_medium,
                styles.white,
                {alignItems: 'center'},
              ]}>
              <Icon
                name="check"
                color="#ffffff"
                type="simple-line-icon"
                size={24}
                style={{marginRight: 10, marginTop: 2}}
              />{' '}
              {t('addbasket')}
            </Text>
          </View>
        </View>
      </Modal>
    );

  return (
    <Modal
      transparent
      visible={showRecommendedProducts && recommendedProducts?.data?.length > 0}
      animationType="fade">
      <View style={stylesRecommended.backDrop}>
        <View style={stylesRecommended.centeredView}>
          {/* <TouchableOpacity
            onPress={() => {
              setShowRecommendedProducts(!showRecommendedProducts),
                AsyncStorage.setItem(
                  '@isRecommendedProductsShown',
                  JSON.stringify(true),
                );
            }}
            style={stylesRecommended.backButton}>
            <Icon
              name="x"
              type="feather"
              iconStyle={{color: 'white', fontSize: 24}}
            />
          </TouchableOpacity> */}

          <View style={{paddingVertical: 10}}>
            <FlatList
              bounces={false}
              nestedScrollEnabled={true}
              contentContainerStyle={{
                ...stylesRecommended.scrollView,
                gap: 10,
              }}
              indicatorStyle="black"
              style={stylesRecommended.scrollView}
              data={recommendedProducts?.data}
              keyExtractor={item => item.productId.toString()}
              renderItem={({item}) => (
                <ProductItem
                  product={item}
                  basket={basket}
                  showOptions={showOptions}
                  setShowOptions={setShowOptions}
                  selectedProductOptions={selectedProductOptions}
                  _addRecommendedProduct={_addRecommendedProduct}
                />
              )}
              ListHeaderComponent={
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingVertical: 12,
                  }}>
                  <View style={stylesRecommended.headerContainer}>
                    <Text style={stylesRecommended.headerText}>
                      {recommendedProducts?.title}
                    </Text>
                  </View>

                  <TouchableOpacity
                    onPress={() => {
                      setShowRecommendedProducts(!showRecommendedProducts),
                        AsyncStorage.setItem(
                          '@isRecommendedProductsShown',
                          JSON.stringify(true),
                        );
                    }}
                    style={{
                      position: 'absolute',
                      right: 0,
                      height: BUTTON_SIZE,
                      width: BUTTON_SIZE,
                      backgroundColor: 'rgba(0,0,0,.7)',
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: BUTTON_SIZE / 2,
                    }}>
                    <Icon
                      name="x"
                      type="feather"
                      iconStyle={{color: 'white', fontSize: 24}}
                    />
                  </TouchableOpacity>
                </View>
              }
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};
export default ModalRecommendedProducts;

const BUTTON_SIZE = 36;
const stylesRecommended = StyleSheet.create({
  backDrop: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  centeredView: {
    width: width * 0.95,
    maxHeight: width * 1.25,
    backgroundColor: 'white',
    borderRadius: 10,
  },
  backButton: {
    position: 'absolute',
    height: BUTTON_SIZE,
    width: BUTTON_SIZE,
    right: 0,
    top: -(1.2 * BUTTON_SIZE),
    backgroundColor: 'rgba(0,0,0,.7)',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BUTTON_SIZE / 2,
    borderWidth: 2,
    borderColor: 'white',
  },
  scrollView: {
    flexGrow: 1,
    paddingHorizontal: 6,
    paddingBottom: 0,
  },
  headerContainer: {alignItems: 'center', flex: 1},
  headerText: {fontWeight: 'bold', color: '#000', fontSize: 16},
});

const pagestyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#00000099',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    borderRadius: 10,
    padding: 10,
    elevation: 2,
  },
  buttonOpen: {
    backgroundColor: '#F194FF',
  },
  buttonClose: {
    backgroundColor: '#2196F3',
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontFamily: 'AndaleMono',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
});
