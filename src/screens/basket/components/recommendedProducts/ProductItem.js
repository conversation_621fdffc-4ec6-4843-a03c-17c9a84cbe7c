import React, {memo, useContext, useEffect, useMemo, useState} from 'react';
import {
  FlatList,
  Text,
  View,
  TouchableOpacity,
  Image,
  StyleSheet,
} from 'react-native';
import LocalizationContext from '../../../../context/LocalizationContext';
import ProductOption from './ProductOption';
import {Icon} from 'react-native-elements';
import globalStyles from '../../../../stylesheets/styles';
import * as sc from '../../../../stylesheets/stylesShort';

const ProductItem = ({
  product,
  basket,
  selectedProductOptions,
  _addRecommendedProduct,
}) => {
  const {t} = useContext(LocalizationContext);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [showOptionsLocal, setShowOptionsLocal] = useState(false);

  const handlePress = optionId => {
    setActiveDropdown(prev => (prev === optionId ? null : optionId));
  };

  const isProductExists = useMemo(() => {
    return basket.some(item => item.id === product.productId);
  }, [basket, product]);

  return (
    <View
      style={[
        styles.container,
        globalStyles.shadow,
        isProductExists && styles.disabled,
      ]}>
      <View style={styles.row}>
        <Image source={{uri: product.thumb}} style={styles.productImage} />

        <View style={globalStyles.sepetiniz_child_sol}>
          <View style={styles.details}>
            <Text style={sc.UrunAdi}>
              {product?.name} {'\n'}
              <Text style={sc.Fiyat_sepet}>
                {product?.price} {t('currency')}
              </Text>
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={[
            styles.addButton,
            {
              backgroundColor: isProductExists
                ? 'rgba(0, 0, 0, 0.3)'
                : 'rgba(0, 0, 00, 1)',
            },
          ]}
          disabled={isProductExists}
          onPress={() => {
            if (product?.option?.length) {
              setShowOptionsLocal(!showOptionsLocal);
            } else {
              _addRecommendedProduct(product);
            }
            setActiveDropdown(null);
          }}>
          <Icon
            name="plus"
            type="font-awesome"
            iconStyle={{color: 'white', fontSize: 14}}
          />
        </TouchableOpacity>
      </View>

      {product?.option && showOptionsLocal && (
        <FlatList
          data={product.option}
          renderItem={({item, index}) => (
            <ProductOption
              index={index}
              isExpanded={activeDropdown === item.optionid}
              onPress={() => handlePress(item.optionid)}
              option={item}
              product={product}
              selectedProductOptions={selectedProductOptions}
              _addRecommendedProduct={_addRecommendedProduct}
              setActiveDropdown={setActiveDropdown}
            />
          )}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 5,
    borderRadius: 2,
    borderColor: '#808080',
    backgroundColor: '#fff',
    gap: 12,
  },
  disabled: {
    backgroundColor: 'rgba(0,0,0,0.2)',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productImage: {width: 50, height: 50, marginRight: 4, borderRadius: 4},
  details: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  addButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 30,
    height: 30,

    borderRadius: 2,
  },
});

export default memo(ProductItem);
