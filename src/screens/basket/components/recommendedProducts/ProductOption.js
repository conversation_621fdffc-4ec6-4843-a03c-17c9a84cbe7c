import React, {memo, useContext, useState} from 'react';
import {FlatList, Text, TouchableOpacity, View, StyleSheet} from 'react-native';
import {Icon} from 'react-native-elements';
import CustomDropDown from '../../../../components/CustomDropDown';
import LocalizationContext from '../../../../context/LocalizationContext';

const ProductOption = ({
  option,
  product,
  selectedProductOptions,
  _addRecommendedProduct,
  isExpanded,
  onPress,
  setActiveDropdown,
}) => {
  const {t} = useContext(LocalizationContext);
  const _checkSubOptionStatus = (productOptionId, productSubOptionId) => {
    return (
      selectedProductOptions[productOptionId]?.itemcode === productSubOptionId
    );
  };

  const renderProductSubOption = ({item: subOption}) => {
    const isSelected = _checkSubOptionStatus(
      option?.optionid,
      subOption?.optionitemid,
    );

    return (
      <View
        style={[
          styles.subOptionContainer,
          isSelected && styles.selectedSubOption,
        ]}>
        <Text
          style={[
            styles.subOptionText,
            isSelected && styles.selectedSubOptionText,
          ]}>
          {subOption?.optionitemname}
        </Text>
        {subOption?.optionitemprice > 0 && (
          <Text style={styles.subOptionPrice}>
            +{subOption?.optionitemprice} {t('currency')}
          </Text>
        )}
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => {
            _addRecommendedProduct(product, {
              optionid: option?.optionid,
              itemcode: subOption?.optionitemid,
              itemname: subOption?.optionitemname,
              itemprice: subOption?.optionitemprice,
              required: subOption?.required,
            });
            const nextOptionIndex = product?.option?.findIndex(
              (opt, index) =>
                opt.optionid !== option?.optionid &&
                !selectedProductOptions[opt.optionid],
            );

            if (nextOptionIndex !== -1) {
              setActiveDropdown(product.option[nextOptionIndex].optionid);
            } else {
              setActiveDropdown(null);
            }
          }}>
          <Icon name="plus" type="font-awesome" iconStyle={styles.addIcon} />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <CustomDropDown
      isExpanded={isExpanded}
      onPress={onPress}
      HeaderComponent={() => (
        <Text style={styles.optionName}>{option?.optionname}</Text>
      )}
      style={styles.dropDownContainer}>
      <FlatList
        data={option?.optionitem}
        renderItem={renderProductSubOption}
        keyExtractor={item => item.optionitemid.toString()}
      />
    </CustomDropDown>
  );
};

const styles = StyleSheet.create({
  dropDownContainer: {
    backgroundColor: '#f1f2f1',
    borderRadius: 8,
    marginVertical: 5,
  },
  optionName: {
    fontSize: 18,
    flex: 1,
    color: '#000',
  },
  subOptionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#e3e3e3',
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  selectedSubOption: {
    backgroundColor: '#a1a1a1',
  },
  subOptionText: {
    flex: 1,
    fontSize: 14,
    color: '#000',
  },
  selectedSubOptionText: {
    color: '#fff',
  },
  subOptionPrice: {
    marginRight: 10,
    color: '#666',
  },
  addButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 30,
    height: 30,
    backgroundColor: '#d40b11',
    borderRadius: 5,
  },
  addIcon: {
    color: 'white',
    fontSize: 14,
  },
});

export default memo(ProductOption);
