import {View, Text, StyleSheet, Pressable} from 'react-native';
import React, {memo} from 'react';
import Animated, {
  interpolate,
  measure,
  runOnUI,
  useAnimatedRef,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import {Icon} from 'react-native-elements';
const baseSpringConfig = {
  mass: 1,
  overshootClamping: false,
  restDisplacementThreshold: 0.01,
  restSpeedThreshold: 2,
  damping: 15,
  stiffness: 100,
};

const rotationSpringConfig = {
  duration: 800,
};

const FAQItem = faq => {
  const descriptionRef = useAnimatedRef();
  const isExpanded = useSharedValue(false);
  const height = useSharedValue(0);
  const rotation = useSharedValue(0);

  const handleAccordionPress = () => {
    if (height.value === 0) {
      runOnUI(() => {
        'worklet';
        height.value = withSpring(
          measure(descriptionRef).height,
          baseSpringConfig,
        );
        rotation.value = withSpring(1, rotationSpringConfig);
      })();
    } else {
      height.value = withTiming(0, {duration: 500});
      rotation.value = withSpring(0, rotationSpringConfig);
    }
    isExpanded.value = !isExpanded.value;
  };

  const animatedContainerStyle = useAnimatedStyle(() => {
    return {height: height.value};
  });

  const animatedIconStyle = useAnimatedStyle(() => {
    const degree = interpolate(rotation.value, [0, 1], [0, 180]);
    return {
      transform: [{rotate: `${degree}deg`}],
    };
  });
  if (faq?.disabled)
    return (
      <View style={styles.disabledContainer}>
        <Text style={styles.disabledTitleText}>{faq.title}</Text>
      </View>
    );

  return (
    <View style={[styles.container]}>
      <Pressable style={styles.pressable} onPress={handleAccordionPress}>
        <Text style={styles.titleText}>{faq.title}</Text>
        <Animated.View style={[animatedIconStyle]}>
          <IconDown stroke={Colors.primary} />
        </Animated.View>
      </Pressable>

      <Animated.View style={animatedContainerStyle}>
        <Animated.View style={styles.descriptionContainer} ref={descriptionRef}>
          <View style={styles.description}>
            <Text style={styles.descriptionText}>{faq.description}</Text>
          </View>
        </Animated.View>
      </Animated.View>
    </View>
  );
};
export default memo(FAQItem);
const styles = StyleSheet.create({
  disabledContainer: {
    borderRadius: 6,
    // borderColor: Colors.lightGray,
    // borderWidth: 1,
    backgroundColor: '#f1f2f1',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  // color: '#d40b11',

  // sub item backgroundColor: '#e3e3e3',
  disabledTitleText: {
    color: Colors.black,
    fontSize: 14,
    flexGrow: 1,
    flexShrink: 1,
  },
  container: {
    borderRadius: 6,
    borderWidth: 1,
    backgroundColor: Colors.lighterGray,
    borderColor: Colors.lightGray,
    overflow: 'hidden',
  },
  pressable: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: Colors.white,

    justifyContent: 'space-between',
  },
  titleText: {
    color: Colors.black,
    fontSize: 16,
    fontWeight: '500',
    flexGrow: 1,
    flexShrink: 1,
  },
  descriptionContainer: {
    position: 'absolute',
    width: '100%',
    top: 0,
  },
  description: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: Colors.lighterGray,
  },
  descriptionText: {
    color: '#4e5252',
  },
});
