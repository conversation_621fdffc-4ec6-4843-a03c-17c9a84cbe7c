import React, {memo, useContext} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {Icon} from 'react-native-elements';
import LocalizationContext from '../../../../context/LocalizationContext';

const ProductSubOption = ({
  productOption,
  productSubOption,
  selectedProductOptions,
  recommendedProduct,
  _addRecommendedProduct,
}) => {
  const {t} = useContext(LocalizationContext);

  const isSelected =
    selectedProductOptions[productOption?.optionid]?.itemcode ===
    productSubOption?.optionitemid;

  return (
    <View
      style={[
        styles.subOptionContainer,
        isSelected && styles.selectedContainer,
      ]}>
      <Text style={[styles.subOptionName, isSelected && styles.selectedText]}>
        {productSubOption?.optionitemname}
      </Text>
      {productSubOption?.optionitemprice > 0 && (
        <Text style={styles.subOptionPrice}>
          +{productSubOption?.optionitemprice} {t('currency')}
        </Text>
      )}
      <TouchableOpacity
        style={styles.addButton}
        onPress={() =>
          _addRecommendedProduct(recommendedProduct, {
            optionid: productOption?.optionid,
            itemcode: productSubOption?.optionitemid,
            itemname: productSubOption?.optionitemname,
            itemprice: productSubOption?.optionitemprice,
          })
        }>
        <Icon name="plus" type="font-awesome" iconStyle={styles.icon} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  subOptionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: '#e3e3e3',
    borderRadius: 8,
    marginVertical: 4,
  },
  selectedContainer: {
    backgroundColor: '#a1a1a1',
  },
  subOptionName: {
    flex: 1,
    fontSize: 14,
    color: '#000',
  },
  selectedText: {
    color: '#fff',
  },
  subOptionPrice: {
    marginRight: 10,
    color: '#666',
  },
  addButton: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 30,
    height: 30,
    backgroundColor: '#d40b11',
    borderRadius: 5,
  },
  icon: {
    color: 'white',
    fontSize: 14,
  },
});

export default memo(ProductSubOption);
