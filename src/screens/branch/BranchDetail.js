import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Platform,
  Dimensions,
  LogBox,
  Linking,
} from 'react-native';
import {SliderBox} from '../../modules/react-native-image-slider-box';

import {Icon, ListItem} from 'react-native-elements';

import styles from '../../stylesheets/styles';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';

import mobileApi from '../../api/mobileApi';
import {navigate} from '../../navigationRef';

LogBox.ignoreAllLogs();

const BranchDetail = ({navigation, route}) => {
  const {t, locale} = React.useContext(LocalizationContext);
  const b = t('branch');
  const {state, setBranch} = React.useContext(UserContext);

  const {id, results, menu} = route.params;
  const [paymentData, setPaymentData] = React.useState(null);
  const getResults = async () => {
    const response = await mobileApi.get(
      `/${locale}/getPayments?restaurant=${id}`,
    );
    if (response.data) {
      setPaymentData(response.data);
    }
  };

  /*const _setBranch = async (id, name) => {
    const service = '';
    const addressid = '';
    const regionLimit = '';
    const serviceTime = '';
    await setBranch(id, name, service, addressid, regionLimit, serviceTime)
      .then(() =>
        navigate('Orders', {
          screen: 'MenuList',
          params: {
            id,
            name,
            menu: 1,
            detail: true,
            results: results,
          },
        }),
      )
      .catch(err => {
        console.log(err);
      });
  };*/

  const _setBranch = async (id, name) => {
    navigate('Orders', {
      screen: 'MenuList',
      params: {
        id,
        name,
        menu: 1,
        detail: true,
        results: results,
      },
    });
  };

  React.useEffect(() => {
    navigation.setOptions({
      title: b.mainTitle,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    getResults();
  }, []);

  if (!results || !paymentData) {
    return null;
  }
  return (
    <View style={styles.container}>
      <SafeAreaView>
        <ScrollView>
          {results.img ? (
            <SliderBox
              images={results.img}
              resizeMethod={'resize'}
              resizeMode={'cover'}
              sliderBoxHeight={300}
              dotStyle={{
                width: 5,
                height: 5,
                borderRadius: 5,
                marginHorizontal: 5,
                padding: 5,
              }}
              paginationBoxStyle={{
                position: 'absolute',
                bottom: 0,
                padding: 0,
                alignItems: 'flex-end',
                alignSelf: 'flex-end',
                justifyContent: 'flex-end',
                paddingVertical: 15,
              }}
              inactiveDotColor="#a1a5aa"
              dotColor="#9f00aa"
              ImageComponentStyle={{
                borderRadius: 0,
                width: '100%',
              }}
              autoplay
              circleLoop
              activeOpacity={1}
              autoplayInterval={5000}
            />
          ) : null}

          <View style={{marginBottom: 15}}>
            <Text
              style={[
                styles.text26,
                styles.white,
                styles.ozelfont_medium,
                {marginVertical: 0, paddingVertical: 10, paddingLeft:25, letterSpacing: -2, backgroundColor: '#9f00aa'},
              ]}>
              {results.name}
            </Text>
           
          </View>



            <View style={{marginHorizontal: 25, marginBottom: 20}}>
             
            <Text
              style={[styles.text16, styles.ozelfont_regular, styles.black]}>
              {results.address}
            </Text>
          </View>
          


          <View>
            {Object.keys(b.menu).map(item => {
              return (
                <TouchableOpacity
                  style={[styles.listeleme, styles.bgrenk5]}
                  key={item}
                  onPress={() => {
                    if (b.menu[item].detail) {
                      if (b.menu[item].id === 1) {
                        Linking.openURL(
                          `tel:${results.phone.replace(/ /g, '')}`,
                        );
                        //console.log(results.phone);
                      } else if (b.menu[item].id === 6) {
                        _setBranch(results.id, results.name);
                      } else {
                        navigate(b.menu[item].detail, {
                          id: results.id,
                          results: results,
                        });
                      }
                    }
                  }}>
                  <View style={styles.kutu_full}>
                    {/* <View style={styles.listeleme_sol}>
                      <Icon
                        name={b.menu[item].lefticon}
                        type="simple-line-icon"
                        color="#949494"
                        alignSelf="center"
                      />
                    </View> */}

                    <View style={styles.listeleme_orta}>
                      <View>
                        <Text style={[styles.SubeDetayBaslik, styles.black]}>
                          {b.menu[item].title}
                        </Text>
                      </View>
                      {b.menu[item].desc === '4' ? (
                        <View
                          style={[
                            styles.text12,
                            {
                              display: 'flex',
                              flexDirection: 'row',
                              flexWrap: 'wrap',
                              width: '100%',
                              padding: 15,
                            },
                          ]}>
                          {paymentData
                            ? paymentData.map(item => (
                                <Text
                                  key={item}
                                  style={{
                                    paddingVertical: 2,
                                    width: '50%',
                                    color: 'black',
                                    letterSpacing: -1,
                                    fontFamily: 'AndaleMono',
                                  }}>
                                  {item}
                                </Text>
                              ))
                            : null}
                        </View>
                      ) : null}
                    </View>

                    {b.menu[item].id === 1 ? (
                      <View style={styles.listeleme_sag2}>
                        <Text style={[styles.ozelfont_medium, styles.black]}>
                          {results.phone}
                        </Text>
                      </View>
                    ) : b.menu[item].id === 2 ? (
                      <View style={styles.listeleme_sag2}>
                        <Text style={[styles.ozelfont_medium, styles.black]}>
                          {results.hour}
                        </Text>
                      </View>
                    ) : b.menu[item].id === 7 && results.gelal === 'Y' ? (
                      <View style={styles.listeleme_sag}>
                        <Icon
                          name={b.menu[item].righticon}
                          type="font-awesome"
                          color="green"
                          size={20}
                        />
                      </View>
                    ) : b.menu[item].id === 7 && results.gelal === 'N' ? (
                      <View style={styles.listeleme_sag}>
                        <Icon
                          name={b.menu[item].righticon2}
                          type="font-awesome"
                          size={20}
                        />
                      </View>
                    ) : b.menu[item].desc === '' ? (
                      <View style={styles.listeleme_sag}>
                        <Icon
                          name={b.menu[item].righticon}
                          type="font-awesome"
                        />
                      </View>
                    ) : null}
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default BranchDetail;
