import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  SafeAreaView,
  ScrollView,
  ImageBackground
} from 'react-native';
import styles from '../../stylesheets/styles';
import LocalizationContext from '../../context/LocalizationContext';

import mobileApi from '../../api/mobileApi';

const BranchDistricts = ({navigation, route}) => {
  const {t, locale} = React.useContext(LocalizationContext);
  const b = t('branch');
  const {id} = route.params;
  const [districtData, setDistrictData] = React.useState(null);
  const getResults = async () => {
    const response = await mobileApi.get(
      `/${locale}/getRestaurantDistincts?restaurant=${id}`,
    );
    console.log(response.data);
    if (response.data) {
      let newdata = Object.values(
        response.data.reduce((acc, item) => {
          if (!acc[item.price])
            acc[item.price] = {
              distincId: item.distincId,
              price: item.price,
              deliveryPrice: item.deliveryPrice,
              district: [],
            };
          acc[item.price].district.push(item.distinctName);
          return acc;
        }, {}),
      );

      setDistrictData(newdata);
    }
  };

  React.useEffect(() => {
    navigation.setOptions({
      title: b.districtTitle,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    getResults();
  }, []);
  if (!districtData) {
    return null;
  }
  return (

    <ImageBackground source={require('../../../assets/img/back_language.jpg')} resizeMode="cover" style={{ flex: 1, marginTop: 0 }}>

    <View>
      <SafeAreaView>
        <ScrollView>
          { /*<View style={styles.anasayfa_element_genis}>
            <View
              style={[
                styles.anasayfa_element_genis_ikon,
                {backgroundColor: '#ffffff'},
              ]}>
              <View>
                <Image
                  source={require('../../../assets/img/icon_info.png')}
                  style={{
                    height: 32,
                    width: 32,
                    marginRight: 15,
                    resizeMode: 'contain',
                  }}
                />
              </View>

              <View style={{width: '85%'}}>
                <Text
                  style={[styles.anasayfa_element_yazi,  {flexWrap: 'wrap'}]}>
                  {t('deliveryText')}
                </Text>
              </View>
            </View>
          </View>*/}
          <View
             
              style={{
                display: 'flex',
                flexDirection: 'row',
                marginHorizontal: 20,
                marginVertical: 10,
              }}>
              <Text
                style={[
                  styles.text14,
                  styles.black,
                  styles.ozelfont_regular,
                  {flex: 1, lineHeight: 20},
                ]}>{t("regionLimit")}
              </Text>
              <Text
                style={[
                  styles.text14,
                  styles.black,
                  styles.ozelfont_regular,
                  {flex: 2, lineHeight: 20},
                ]}>{t("login.selectDistrict")}
              </Text>
            </View>

          {districtData.map((item, i) => (
            <View
              key={i}
              style={{
                display: 'flex',
                flexDirection: 'row',
                marginHorizontal: 20,
                marginVertical: 10,
              }}>
              <Text
                style={[
                  styles.text16,
                  styles.black,
                  styles.ozelfont_medium,
                  {flex: 1, lineHeight: 25},
                ]}>
                {item.price} {t('currency')}{' '}
              </Text>
              <Text
                style={[
                  styles.text14,
                  styles.black,
                  styles.ozelfont_regular,
                  {flex: 2, lineHeight: 20},
                ]}>
                {item.district.map((district, d) => {
                  //console.log(district);
                  return (
                    <Text key={d} style={{flex: 1}}>
                      {district} {'\n'}
                    </Text>
                  );
                })}
              </Text>
            </View>
          ))}
        </ScrollView>
      </SafeAreaView>
    </View>
    </ImageBackground>
  );
};

export default BranchDistricts;
