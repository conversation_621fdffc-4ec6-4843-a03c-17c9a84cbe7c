import React from 'react';
import {
  Platform,
  View,
  Text,
  StyleSheet,
  TouchableHighlight,
  Image,
  Dimensions,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';
import LocalizationContext from '../../context/LocalizationContext';
import Carousel from 'react-native-snap-carousel';

import styles from '../../stylesheets/styles';

const IS_IOS = Platform.OS === 'ios';
const {width: viewportWidth, height: viewportHeight} = Dimensions.get('window');
function wp(percentage) {
  const value = (percentage * viewportWidth) / 100;
  return Math.round(value);
}

const slideHeight = wp(140);
const slideWidth = wp(70);
const itemHorizontalMargin = wp(0.2);

export const sliderWidth = viewportWidth;
export const itemWidth = slideWidth + itemHorizontalMargin * 2;

const entryBorderRadius = 5;

const BranchPromo = ({navigation, route}) => {
  const {t, locale, setLocale} = React.useContext(LocalizationContext);
  const b = t('branch');
  const {id, results} = route.params;
  const [promo, setPromo] = React.useState();
  const promoList = [];

  _convertData = () => {
    results?.promo?.map((item, index) => {
      if (item[1]) {
        // console.log(item[1]);
        promoList[index] = {
          title: item[0],
          image: item[1],
        };
      }
    });
    setPromo(promoList);
  };

  _renderItem = ({item, index}) => {
    return (
      <TouchableOpacity
        activeOpacity={1}
        style={{
          width: itemWidth,
          height: slideHeight,
          paddingHorizontal: itemHorizontalMargin,
          paddingBottom: 18, // needed for shadow
        }}>
        <View
          style={{
            flex: 1,
            marginBottom: IS_IOS ? 0 : -1,
            backgroundColor: 'white',
            borderTopLeftRadius: entryBorderRadius,
            borderTopRightRadius: entryBorderRadius,
          }}>
          <Text style={([styles.text20], {alignSelf: 'center'})}>
            {item.title}
          </Text>
          <Image
            source={{uri: item.image}}
            style={{
              ...StyleSheet.absoluteFillObject,
              resizeMode: 'contain',
              borderRadius: IS_IOS ? entryBorderRadius : 0,
              borderTopLeftRadius: entryBorderRadius,
              borderTopRightRadius: entryBorderRadius,
            }}
          />
        </View>
      </TouchableOpacity>
    );
  };

  React.useEffect(() => {
    navigation.setOptions({
      title: b.promoTitle,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    _convertData();
  }, []);

  if (!promo) {
    return null;
  }

  return (
    <ImageBackground
      source={require('../../../assets/img/back_cart.jpg')}
      resizeMode="cover"
      style={{flex: 1, marginTop: 0}}>
      <View style={{paddingTop: 10}}>
        {promo.length > 0 ? (
          <Carousel
            data={promo}
            renderItem={_renderItem}
            sliderWidth={sliderWidth}
            itemWidth={itemWidth}
            loop={true}
          />
        ) : (
          <View style={{alignItems: 'center'}}>
            <Image
              source={require('../../../assets/img/uzgun2.png')}
              style={{
                height: 80,
                width: 80,
                marginVertical: 20,
                resizeMode: 'contain',
              }}
            />
            <Text style={[styles.anasayfa_element_yazi, {flexWrap: 'wrap'}]}>
              {t('noPromo')}
            </Text>
          </View>
        )}
      </View>
    </ImageBackground>
  );
};

export default BranchPromo;
