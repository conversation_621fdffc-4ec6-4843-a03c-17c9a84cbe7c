import React, {useContext, useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ImageBackground,
  Image,
} from 'react-native';
import {Icon} from 'react-native-elements';

import {navigate} from '../../navigationRef';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';
import Geolocation from '@react-native-community/geolocation';
import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import mobileApi from '../../api/mobileApi';
import Loading from '../../components/Loading';

const MainScreen = ({navigation, route}) => {
  const {menu} = route.params;
  const {t, locale} = useContext(LocalizationContext);
  const {setBranch} = useContext(UserContext);
  const [isLoading, setLoading] = useState(true);
  const [isGranted, setGranted] = useState(false);

  const [results, setResults] = useState(null);
  const [menuView, setMenuView] = useState(0);
  const b = t('branch');

  const getResults = async coords => {
    const response = await mobileApi.get(
      `/${locale}/getRestaurants?lat=${coords.latitude}&lon=${coords.longitude}`,
    );
    if (coords) {
      var dataArray = [];
      for (var i in response.data) {
        dataArray.push(response.data[i]);
      }

      dataArray.sort(function (x, y) {
        var xDistance = x.distance;
        var yDistance = y.distance;

        if (xDistance < yDistance) return -1;
        else if (xDistance > yDistance) return 1;
        else return 0;
      });

      setResults(dataArray);
    } else {
      setResults(response.data);
    }

    setLoading(false);
  };

  const _setBranch = async (id, name) => {
    console.log('params=>', {
      id,
      name,
    });
    if (route?.params?.menu === 2) {
      navigate('Orders', {
        screen: 'NewProducts',
        params: {
          id,
          name,
        },
      });
    } else {
      navigate('Orders', {
        screen: 'MenuList',
        params: {
          id,
          name,
          menu: 1,
          detail: false,
        },
      });
    }
  };

  const getLocation = () => {
    Geolocation.getCurrentPosition(
      info => {
        setGranted(true);

        if (info) {
          getResults(info.coords);
        } else {
          setGranted(false);
          getResults('');
        }
      },
      () => {
        setGranted(false);
        getResults('');
      },
    );
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      navigation.setOptions({
        title: b.mainTitle,
        headerTruncatedBackTitle: '',
        headerBackTitle: '',
      });
      getLocation();
    });
    return unsubscribe;
  }, [navigation]);

  if (isLoading) {
    return <Loading />;
  }

  return (

       
        <ImageBackground
          source={require('../../../assets/img/back_profil.jpg')}
          resizeMode="cover"
          style={{flex: 1, height: '100%'}}>
          

    <SafeAreaView>
        <Image
                    style={{height: 300, width: '100%', zIndex: 0, position: 'relative',  }}
                    source={require('../../../assets/img/sojubar-subeler.png')}
                    resizeMode="stretch"
                  />
                  
      {menu ? (
        <View
          style={[
            styles.alertbox,
            styles.tcenter,
            {alignSelf: 'center', width: '90%'},
          ]}>
          <Text style={[styles.text16, styles.tcenter, styles.black]}>
            {b.menubranch}
          </Text>
        </View>
      ) : null}
      <ScrollView>


<View style={{marginBottom: 10}}>
      
</View>
            
        {Object.keys(results).map(item => {
          if (results[item].status === 'Y') {
            return (
              <TouchableOpacity
                style={[styles.listeleme, styles.bgrenk5]}
                key={results[item].id}
                onPress={() => {
                  if (menu === 1 || menu === 2) {
                    _setBranch(results[item].id, results[item].name);
                  } else {
                    navigate('BranchDetail', {
                      id: results[item].id,
                      results: results[item],
                      menu,
                    });
                  }
                }}>
                <View style={styles.kutu_full}>
                  <View style={styles.listeleme_orta}>
                    <Text
                      style={[
                        styles.SubeAd,
                        styles.ozelfont_bold,
                        styles.black,
                      ]}>
                      {results[item].name}{' '}
                      {isGranted && (
                        <Text
                          style={[
                            styles.text12,
                            styles.ozelfont_bold,
                            {color: 'gray'},
                          ]}>
                          ({results[item].distance} km)
                        </Text>
                      )}
                    </Text>
                    {/* <Text style={styles.SubeAdres}>
                      {results[item].address}
                    </Text> */}
                  </View>
                  <View style={styles.listeleme_sag}>
                    <Icon
                      name="angle-right"
                      type="font-awesome"
                      iconStyle={sc.RightArrow}
                    />
                  </View>
                </View>
              </TouchableOpacity>
            );
          }
        })}
      </ScrollView>
    </SafeAreaView>

    </ImageBackground>
  );
};

export default MainScreen;
