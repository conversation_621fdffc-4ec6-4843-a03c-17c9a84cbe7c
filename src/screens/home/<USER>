import React, {useEffect, useContext, useState, useMemo} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Geolocation from '@react-native-community/geolocation';
import {Icon} from 'react-native-elements';
import {SliderBox} from '../../modules/react-native-image-slider-box';
import {navigate} from '../../navigationRef';

import messaging from '@react-native-firebase/messaging';
import {firebase} from '@react-native-firebase/analytics';
import Loading from '../../components/Loading';
import {
  View,
  Text,
  Image,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  LogBox,
  ImageBackground,
  Platform,
  Dimensions,
  StyleSheet,
  PermissionsAndroid,
  Alert,
  Linking,
  Modal,
  Pressable,
  Share,
  TouchableWithoutFeedback,
} from 'react-native';
import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';
import {request, PERMISSIONS} from 'react-native-permissions';
import {requestTrackingPermission} from 'react-native-tracking-transparency';
import VersionNumber from 'react-native-version-number';
const {width: deviceWidth, height: deviceHeight} = Dimensions.get('window');

import mobileApi from '../../api/mobileApi';

LogBox.ignoreLogs([
  'Non-serializable values were found in the navigation state',
]);

import DatePicker from 'react-native-date-picker';
import {Adjust} from 'react-native-adjust';
import WebView from 'react-native-webview';

LogBox.ignoreAllLogs();
const HomeScreen = ({navigation, route}) => {
  const {state, loginCheck, getBadge, login} = useContext(UserContext);
  const {t, locale} = useContext(LocalizationContext);
  const l = t('login');
  const [track, setTrack] = useState(0);
  const [trackStatus, setTrackStatus] = useState('');
  const [trackStatusText, setTrackStatusText] = useState('');
  const [trackTime, setTrackTime] = useState('');
  const [trackId, setTrackId] = useState('');
  const [rateView, setRateView] = useState(false);
  const [userLoyalty, setUserLoyalty] = useState('');

  const [sliders, setSliders] = React.useState([]);

  const {handleBadge} = route.params;
  const [date, setDate] = useState(new Date());
  const [open, setOpen] = useState(false);
  const [birthday, setBirthday] = useState(true);

  const [results, setResults] = useState([]);
  const [update, setUpdate] = useState(1);

  const [appModal, setAppModal] = useState(false);
  const [newYearImage, setNewYearImage] = useState('');
  const [newYearLink, setNewYearlink] = useState('');
  const [newYearId, setNewYearId] = useState();

  const [modal, setModal] = useState(false);
  const [modalImage, setModalImage] = useState('');
  const [mainLink, setMainLink] = useState('');
  const [subLink, setSubLink] = useState();
  const [buttonText, setButtonText] = useState();

  const [appConfig, setAppConfig] = useState({});

  const restaurantMenuImage = useMemo(() => {
    return `https://www.sushico.com.tr/assets/mobileimg/${locale}_home_menu.jpg?${new Date().getTime()}`;
  }, [locale]);

  const getResults = async () => {
    try {
      await mobileApi.get(`/${locale}/getSliderV3`).then(response => {
      
        const imageUrls = response?.data?.map(item => item?.image);
        setSliders(imageUrls);
        setResults(response.data);
      });
    } catch (e) {}
  };

  const checkNewYear = async request => {
    try {
      await mobileApi.post(`/${locale}/newYear`, request).then(response => {
        if (response.data.err === 0) {
          setNewYearImage(response.data.image);
          setNewYearlink(response.data.link);
          setNewYearId(response.data.id);
          setAppModal(true);
        }
      });
    } catch (e) {}
  };

  const checkModal = async request => {
    try {
      await mobileApi.post(`/${locale}/checkModal`, request).then(response => {
        console.log(response.data);
        if (response.data.err === 0) {
          setModalImage(response.data.modalImage);
          setMainLink(response.data.mainLink);
          setSubLink(response.data.subLink);
          setButtonText(response.data.buttonText);
          setModal(true);
        }
      });
    } catch (e) {}
  };

  const share = () => {
    const shareOptions = {
      title: 'SushiCo',
      message: `${t('wpShare')} ${newYearLink}`,
    };
    Share.share(shareOptions);
  };

  const requestTrackPermission = async () => {
    const trackingStatus = await requestTrackingPermission();
    if (trackingStatus === 'authorized') {
      await firebase.analytics().setAnalyticsCollectionEnabled(true);
    }
  };

  const _checkTrack = async () => {
    await AsyncStorage.multiGet(['@userdata', '@token', '@memberid'])
      .then(r => {
        const req = {memberId: r[2][1], token: r[1][1]};
        mobileApi.post(`/tr/trackOrder`, req).then(response => {
          if (response?.data?.err === 0) {
            const track = JSON.parse(response?.data?.response);
            if (track.orderid) {
              if (track.created === 1) {
                setTrackStatusText(t('trackStatus.created'));
                setTrackStatus('created');
                setTrackTime(track.created_time);
              }
              if (track.road === 1) {
                setTrackStatusText(t('trackStatus.road'));
                setTrackStatus('road');
                setTrackTime(track.road_time);
                setTrackId(track.orderid);
              }
              if (track.delivery === 1) {
                setTrackStatusText(t('trackStatus.delivery'));
                setTrackStatus('delivery');
                setTrackTime(track.delivery_time);
              }
              setTrack(1);
            } else {
              setTrack(0);
              setTrackStatusText('');
              setTrackStatus('');
              setTrackStatusText('');
            }
          }
          mobileApi.post(`/tr/rateCheck`, req).then(resp => {
            setRateView(resp?.data?.rate);
          });
        });
      })
      .catch(err => {});
  };

  const _readVal = async () => {
    await AsyncStorage.multiGet([
      '@userdata',
      '@birthday',
      '@email',
      '@password',
      '@vip',
    ])
      .then(r => {
        const data = {
          email: r[2][1],
          password: r[3][1],
          locale,
          version: VersionNumber.appVersion,
        };
        login({data: data, alert: t('alert')})
          .then(resp => {
            if (resp.err === 1) {
              return;
            } else {
              AsyncStorage.getItem('@userdata')
                .then(user => {
                  const rm = JSON.parse(user);
                  setUserLoyalty(rm.loyalty);

                  setUpdate(rm.latestVersion);
                  setBirthday(r[1][1]);
                  if (rm.newYear == 1) {
                    checkNewYear({memberId: rm.memberID, token: rm.token});
                  }
                  if (rm.modal == 1) {
                    checkModal({memberId: rm.memberID, token: rm.token});
                  }
                })
                .catch(err => {
                  return;
                });
            }
          })
          .catch(err => {
            console.log(err);
            return;
          });
      })
      .catch(err => {
        console.log(err);
      });
  };

  const requestCameraPermission = async () => {
    if (Platform.OS === 'ios') {
      request(PERMISSIONS.IOS.CAMERA).then(result => {});
    }
    if (Platform.OS === 'android') {
      request(PERMISSIONS.ANDROID.CAMERA).then(result => {});
    }
  };

  const requestUserPermission = async () => {
    const authStatus = await messaging().requestPermission();

    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      getToken();
    } else {
      state.fcmToken = '';
    }
  };

  const requestLocationPermissions = async () => {
    if (Platform.OS === 'ios') {
      const auth = Geolocation.requestAuthorization();
      if (auth === 'granted') {
      }
    }

    if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      }
    }
  };

  const getToken = async () => {
    const userToken = await messaging().getToken();
    if (userToken) Adjust?.setPushToken(userToken);
    if (userToken && state.memberid && state.token) {
      const request = {
        phoneId: userToken,
        phoneOs: Platform.OS,
        memberId: state.memberid,
        token: state.token,
      };

      try {
        mobileApi.post(`/tr/userNotification`, request).then(r => {});
      } catch (e) {}
    }
  };

  const _onConfirm = date => {
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    const memberbirthday = `${year}-${month}-${day}`;

    if (date) {
      setOpen(false);
      setDate(date);
      const request = {
        birthday: memberbirthday,
        memberId: state.memberid,
        token: state.token,
      };
      try {
        mobileApi.post(`/tr/setBirthday`, request).then(r => {
          if (r.data.err === 0) {
            AsyncStorage.setItem('@birthday', memberbirthday);
            setBirthday(memberbirthday);
            Alert.alert(t('alert').successTitle, t('homebirthdaymsg'), [
              {
                text: t('payment').ok,
              },
            ]);
          } else {
            Alert.alert(t('payment').warning, r.data.msg, [
              {
                text: t('payment').ok,
              },
            ]);
          }
        });
      } catch (e) {}
    }
  };

  useEffect(() => {
    getBadge();
    handleBadge(state.totalCount);
    _readVal();
  }, [state.isLogin]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loginCheck();
      _checkTrack();
      requestUserPermission();
      requestLocationPermissions();
      requestCameraPermission();
      requestTrackPermission();
    });
    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    if (typeof locale !== 'undefined' && locale != null) {
      getResults();
    }
  }, [locale]);

  useEffect(() => {
    const interval = setInterval(() => {
      _checkTrack();
    }, 60000);
    return () => clearInterval(interval);
  }, []);

  // useEffect(() => {
  //   const fetchAppConfig = async () => {
  //     try {
  //       const configResponse = await mobileApi.get('/tr/getAllConfigs');
  //       const configData = configResponse?.data;

  //       setAppConfig(configData);
  //     } catch (error) {
  //       console.log('error=>', error);
  //     }
  //   };
  //   fetchAppConfig();
  // }, []);

  if (!results) {
    return <Loading />;
  }

  return (
    
    <ImageBackground
      source={require('../../../assets/img/back_profil.jpg')}
      resizeMode="cover"
      style={{flex: 1, height: '100%'}}>
      
          

      <SafeAreaView style={{backgroundColor: 'black'}}>
      

        <ScrollView style={{backgroundColor: 'white'}}>
          <Image
              style={{height: 300, width: deviceWidth, zIndex: 0, position: 'absolute',  }}
              source={require('../../../assets/img/anasayfa_ust_back2.png')}
              resizeMode="stretch"
            />
     
     
          {newYearImage ? (
            <Modal
              animationType="slide"
              transparent={true}
              visible={appModal}
              statusBarTranslucent={true}
              onRequestClose={() => {
                setAppModal(!appModal);
              }}>
              <TouchableWithoutFeedback onPressOut={() => setAppModal(false)}>
                <View style={pagestyles.centeredView}>
                  <View style={pagestyles.modalView}>
                    <ImageBackground
                      style={{
                        width: deviceWidth,
                        height: deviceWidth,
                        flexDirection: 'column',
                        justifyContent: 'flex-end',
                      }}
                      source={{
                        uri: `${
                          mobileApi.defaults.baseURL
                        }/assets/${locale}/ny2024/${newYearId}.jpg?${new Date().getTime()}`,
                      }}>
                      <View>
                        <Pressable onPress={() => share()}>
                          <View
                            style={[sc.PrimaryButton, {marginVertical: 15}]}>
                            <Text style={sc.PrimaryButton_text}>
                              {t('sendGift')}
                            </Text>
                          </View>
                        </Pressable>
                      </View>
                    </ImageBackground>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Modal>
          ) : null}
          {modalImage ? (
            <Modal
              animationType="slide"
              transparent={true}
              visible={modal}
              statusBarTranslucent={true}
              onRequestClose={() => {
                setAppModal(!appModal);
              }}>
              <TouchableWithoutFeedback onPressOut={() => setModal(false)}>
                <View style={pagestyles.centeredView}>
                  <View style={pagestyles.modalView}>
                    <ImageBackground
                      style={{
                        width: deviceWidth,
                        height: deviceWidth * 1.2,
                        flexDirection: 'column',
                        justifyContent: 'flex-end',
                      }}
                      source={{
                        uri: `${
                          mobileApi.defaults.baseURL
                        }/assets/${locale}/modal/${modalImage}.jpg?${new Date().getTime()}`,
                      }}>
                      <View>
                        <Pressable
                          onPress={() =>
                            subLink
                              ? navigation.navigate(mainLink, {screen: subLink})
                              : navigation.navigate(mainLink)
                          }>
                          <View
                            style={[
                              sc.PrimaryButton,
                              {marginVertical: 30, padding: 5},
                            ]}>
                            <Text
                              style={[
                                sc.PrimaryButton_text,
                                {
                                  textAlign: 'center',
                                  fontSize: 16,
                                  fontFamily: 'IBMPlexSans-SemiBold',
                                },
                              ]}>
                              {buttonText}
                            </Text>
                          </View>
                        </Pressable>
                      </View>
                    </ImageBackground>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Modal>
          ) : null}
          {state.isLogin ? (
            <TouchableOpacity
              style={{zIndex: 1}}
              onPress={() => {
                navigate('SettingsNotifications');
              }}>
              <View
                style={{
                  alignItems: 'flex-end',
                  position: 'absolute',
                  right: 15,
                  marginTop: 20,
                }}>
                <Icon
                  name="bell"
                  type="feather"
                  iconStyle={{color: 'gray', fontSize: 24}}
                />
              </View>
            </TouchableOpacity>
          ) : null}

          {/* <PromotionModalQueue promotions={appConfig?.promotions} /> */}
          <View
            style={{
              alignItems: 'center',
            }}>
            {state.isLogin ? (
              <View style={{marginVertical: 20}}>
                <Image
                  source={require('../../../assets/img/sojubar-logo-beyaz.png')}
                  style={{
                    height: 90,
                    resizeMode: 'contain',
                  }}
                />
              </View>
            ) : (
              <View style={{marginVertical: 0}}>
                <Image
                  source={require('../../../assets/img/sojubar-logo-beyaz.png')}
                  style={{
                    height: 90,
                    marginTop: 15,
                    resizeMode: 'contain',
                  }}
                />
              </View>
            )}

            <View
              style={{
                alignItems: 'center',
                padding: 8,
              }}>
              {state.isLogin ? (
                <Text
                  style={[
                    styles.text18,
                    styles.ozelfont_medium,
                    styles.white,
                    styles.tcenter,
                  ]}>
                  <Text
                    style={[styles.text20, {fontFamily: 'Andale Mono Bold'}]}>
                    {l.HelloTittle}
                  </Text>
                  <Text
                    style={[
                      styles.text20,
                      {fontFamily: 'AndaleMono'},
                    ]}>
                    {state.firstname}
                  </Text>
                  {'\n'}

                  <Text
                    style={{
                      fontSize: 18,
                      color: 'white',
                      fontFamily: 'Andale Mono Bold',
                      lineHeight: 30,
                    }}>
                    {userLoyalty} üyemizsiniz
                  </Text>
                </Text>
              ) : (
                <View>
                  <Text
                    style={[
                      styles.text18,
                      styles.white,
                      styles.tcenter,
                      styles.kurumsalfont_regular,
                    ]}>
                    {l.WelcomeNoLogin}
                  </Text>

                </View>
              )}
            </View>
          </View>
          {update == 0 ? (
            <View
              style={{
                marginTop: 15,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <TouchableOpacity
                onPress={() => {
                  Platform.OS === 'ios'
                    ? Linking.openURL(
                        'https://itunes.apple.com/tr/app/sushico/id448764028?mt=8',
                      )
                    : Linking.openURL(
                        'https://play.google.com/store/apps/details?id=com.done.sushico',
                      );
                }}>
                <Image
                  source={{
                    uri: `${
                      mobileApi.defaults.baseURL
                    }/assets/${locale}/home/<USER>
                    width: 360,
                    height: 50,
                  }}
                />
              </TouchableOpacity>
            </View>
          ) : null}

          <View
            style={{
              display: 'flex', marginTop: 110,
            }}>

            <SliderBox
              images={sliders}
              resizeMethod={'resize'}
              resizeMode={'contain'}
              sliderBoxHeight={200}
              dotStyle={{
                width: 5,
                height: 5,
                borderRadius: 5,
                marginHorizontal: 5,
                padding: 5,
              }}
              paginationBoxStyle={{
                position: 'absolute',
                bottom: 0,
                padding: 0,
                alignItems: 'flex-end',
                alignSelf: 'flex-end',
                justifyContent: 'flex-end',
                paddingVertical: 15,
              }}
              onCurrentImagePressed={index => {
                const currentSlide = results[index];
                console.log('current slide =>', currentSlide);

                if (!currentSlide?.link) return;

                const isLoginRequired = currentSlide.login_required;
                const isExternalLink = currentSlide.open_type === 1;

                if (isLoginRequired && !state.isLogin) {
                  return navigation.navigate('LoginStack', {
                    screen: 'SliderInfo',
                    params: {page: 'HomePage'},
                  });
                }

                if (isExternalLink) {
                  return Linking.openURL(currentSlide.link);
                }

                navigate('SliderInfo', {
                  sliderDetail: currentSlide,
                });
              }}
              inactiveDotColor="#a1a5aa"
              dotColor="#ff0000"
              ImageComponentStyle={{
                borderRadius: 0,
                width: '95%',
                marginTop: 2,

                    borderwidth: 1,
                    bordercolor: 'red',
              }}
              autoplay
              circleLoop
              activeOpacity={0.5}
              autoplayInterval={5000}
            />
          </View>



          {!birthday && state.isLogin ? (
            <TouchableOpacity
              onPress={() => {
                setOpen(true);
              }}>
              <View
                style={{
                  marginTop: 10,
                  marginBottom: 10,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Image
                  style={{borderRadius: 10}}
                  source={{
                    uri: `${mobileApi.defaults.baseURL}/assets/${locale}/home/<USER>
                    width: 352,
                    height: 64
                  }}
                />
              </View>
            </TouchableOpacity>
          ) : null}

          <DatePicker
            modal
            locale={locale}
            open={open}
            date={date}
            onConfirm={date => {
              _onConfirm(date);
            }}
            onCancel={() => {
              setOpen(false);
            }}
            mode="date"
            confirmText="Onayla"
            cancelText="Vazgeç"
            title="Tarih Seç"
          />


          {track ? (
            <View
              style={{
                backgroundColor: '#f8ecde',
                borderRadius: 10,
                marginLeft: 10,
                marginRight: 10,
                marginTop: 15,
                display: 'flex',
                justifyContent: 'center',
                padding: 10,
                height: 100,
                borderColor: 'red',
                borderWidth: 0.6,
              }}>
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  borderBottomWidth: 1,
                  borderColor: '#ffffff',
                }}>
                <Text
                  style={[
                    styles.text18,
                    styles.mb10,
                    styles.mt10,
                    styles.black,
                  ]}>
                  {trackStatusText}
                </Text>
                <Text
                  style={[
                    styles.text18,
                    styles.mb10,
                    styles.mt10,
                    styles.black,
                  ]}>
                  {trackTime}
                </Text>
              </View>



              {trackStatus === 'road' ? (
                <TouchableOpacity
                  onPress={() => {
                    navigation.navigate('LoginStack', {
                      screen: 'TrackScreen',
                      params: {orderid: trackId, page: 'HomePage'},
                    });
                  }}>
                  <View
                    style={[
                      styles.bgrenk5,
                      {
                        alignSelf: 'center',
                        borderRadius: 10,
                        marginLeft: 20,
                        marginRight: 20,
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        width: '90%',
                        height: 50,
                        backgroundColor: '#f8ecde',
                      },
                    ]}>
                    <Image
                      source={require('../../../assets/img/icon_home_kurye.png')}
                      style={{
                        height: 40,
                        width: 40,
                        marginLeft: 10,
                        resizeMode: 'contain',
                      }}
                    />

                    <Text
                      style={[
                        styles.text18,
                        styles.ozelfont_medium,
                        {
                          width: '75%',
                          padding: 10,
                          color: 'red',
                          textAlign: 'center',
                        },
                      ]}>
                      {t('ordertrack')}
                    </Text>
                    <Icon
                      name="chevron-right"
                      type="feather"
                      iconStyle={{
                        color: 'black',
                        fontSize: 18,
                        paddingRight: 8,
                      }}
                    />
                  </View>
                </TouchableOpacity>
              ) : null}
            </View>
          ) : null}



          {rateView ? (
            <TouchableOpacity
              style={{marginTop: 15}}
              onPress={() => {
                navigation.navigate('LoginStack', {
                  screen: 'SettingsPreviousOrder',
                  params: {page: 'HomePage'},
                });
              }}>
              <View
                style={[
                  styles.bgrenk6,
                  {
                    alignSelf: 'center',
                    borderRadius: 5,
                    margin: 6,
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    width: '94%',
                    height: 60,
                  },
                ]}>
                <Image
                  source={require('../../../assets/img/icon_home_degerlendirme.png')}
                  style={{
                    height: 32,
                    width: 32,
                    marginLeft: 10,
                    resizeMode: 'contain',
                  }}
                />
                <Text
                  style={[
                    styles.text16,
                    styles.ozelfont_medium,
                    {
                      width: '80%',
                      padding: 10,
                      color: 'black',
                      textAlign: 'center',
                    },
                  ]}>
                  {t('lastorderrate')}
                </Text>
                <Icon
                  name="chevron-right"
                  type="feather"
                  iconStyle={{color: 'black', fontSize: 18, paddingRight: 8}}
                />
              </View>
            </TouchableOpacity>
          ) : null}








          
<View style={{flex: 1,  paddingHorizontal: 10,  flexDirection: 'row',
              height: '50', width: '100%', marginVertical: 20,
            }}>
            <TouchableOpacity
              activeOpacity={0.5}
              style={{width: '48%', marginRight: '2%'}}
              onPress={() => {
                if (state.isLogin) {
                  navigation.navigate('LoginStack', {
                    screen: 'QrCode',
                    params: {page: 'HomePage'},
                  });
                } else {
                  Alert.alert(t('payment').warning, t('qrerror1'), [
                    {
                      text: t('payment').ok,
                      onPress: () => {
                        navigation.navigate('Orders');
                      },
                    },
                  ]);
                }
              }}>
              <View
                style={{
                   zIndex: 1,
                    position: 'absolute',
                    marginLeft: 65,
                    top: 18,
                }}>
                <Text
                  style={{
                    fontSize: 14, textAlign: 'center',
                    color: '#000', letterSpacing: -0.5,
                    fontFamily: 'Andale Mono Bold',
                  }}>
                  {t('anasayfa.qr')}
                  <Text style={{fontFamily: 'AndaleMono'}}>
                    {' '}
                    {t('anasayfa.scan')}
                  </Text>
                </Text>
              </View>

              <Image
                style={styles.home_item_img_icon}
                source={require('../../../assets/img/btn_qr_kod_tara.png')}
              />
            </TouchableOpacity>

            {state.isLogin ? (
              <TouchableOpacity
                activeOpacity={0.5}
    style={{width: '48%', marginLeft: '2%'}}
                onPress={() => {
                  navigation.navigate('LoginStack', {
                    screen: 'SettingsPoint',
                    params: {page: 'HomePage'},
                  });
                }}>
                <View
                  style={{
                    zIndex: 1,
                    position: 'absolute',
                    marginLeft: 80,
                    top: 18,
                  }}>
                  <Text
                    style={{
                      fontSize: 14, alignSelf: 'flex-end',
                      color: '#000', letterSpacing: -0.5,
                      fontFamily: 'Andale Mono Bold',
                       marginRight:10,
                    }}>
                    {t('anasayfa.puanim')}

                  </Text>
                </View>
                <Image
                  style={styles.home_item_img_icon}
                  source={require('../../../assets/img/btn_puanim.png')}
                />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                activeOpacity={0.5}
                style={pagestyles.home_item50}
                onPress={() => {
                  navigation.navigate('LoginStack', {
                    screen: 'Login',
                    params: {page: 'HomePage'},
                  });
                }}>
                <View
                  style={{
                     zIndex: 1,
                    position: 'absolute',
                    marginLeft: 90,
                    top: 15,
                  }}>
                  <Text
                    style={{
                      fontSize: 14,
                      color: '#000000',
                      fontFamily: 'IBMPlexSans-Regular',
                    }}>
                    {' '}
                    {t('GirisUyelik')}
                  </Text>
                </View>
                <Image
                  style={styles.home_item_img}
                  source={require('../../../assets/img/btn_giris.png')}
                />
              </TouchableOpacity>
            )}
          </View>


 
 




<View style={{flex: 1,  paddingHorizontal: 10,  flexDirection: 'row',
              height: '125', width: '100%',}}>
  <TouchableOpacity
    activeOpacity={0.5}
    style={{width: '48%', marginRight: '2%',}}
    onPress={() => {
      navigation.navigate('Branch', {
        screen: 'BranchMain',
        params: {menu: 1, ex: 1},
      });
    }}>
                      
          <View style={{
            width: '100%',
            height: '50%',
            alignSelf: 'center',
            position: 'relative',
            borderRadius: 5,
            overflow: 'hidden',
            flex: 1,
          }}>
            <ImageBackground
              style={{
                width: '100%',
                height: '100%',
                justifyContent: 'center',
                alignItems: 'center',
              }}
              source={require('../../../assets/img/back_foto_1.jpg')}
              resizeMode="cover"
            >
              <View style={{
                backgroundColor: 'rgba(255, 255, 255, 0.4)',
                paddingHorizontal: 20,
                paddingVertical: 8,
                borderRadius: 2,
              }}>
                <Text style={{
                  fontSize: 22,
                  color: '#000',
                  fontFamily: 'FrontageCondensed-Bold',
                  textAlign: 'center', 
                }}>
                  {t('anasayfa.menu1')}
                </Text>
              </View>
            </ImageBackground>
          </View>

  </TouchableOpacity>

  <TouchableOpacity
    activeOpacity={0.6}
    style={{width: '48%', marginLeft: '2%',}}
    onPress={() => {
      //  navigation.navigate('Orders', { screen: 'DeliveryType' });
      navigation.navigate('Orders', {screen: 'MainScreen'});
    }}>
    
        <View style={{
          width: '100%',
          height: '50%',
          alignSelf: 'center',
          flex: 1,
          position: 'relative',
          borderRadius: 5,
          overflow: 'hidden'
        }}>
          <ImageBackground
            style={{
              width: '100%',
              height: '100%',
              justifyContent: 'center',
              alignItems: 'center',
            }}
            source={require('../../../assets/img/back_foto_2.jpg')}
            resizeMode="cover"
          >
            <View style={{
              backgroundColor: 'rgba(255, 255, 255, 0.4)',
              paddingHorizontal: 20,
              paddingVertical: 8,
              borderRadius: 2,
            }}>
              <Text style={{
                fontSize: 22,
                color: '#000',
                fontFamily: 'FrontageCondensed-Bold',
                textAlign: 'center', 
              }}>
                {t('anasayfa.siparis')}
              </Text>
            </View>
    </ImageBackground>
  </View>
  </TouchableOpacity>
  
</View>





 



 


{/* MENÜDE NELER VAR */}
{/* <TouchableOpacity
              activeOpacity={0.6}
              style={{width:deviceWidth, height: 220,marginTop: 20, }}
              onPress={() => {
                navigation.navigate('Branch', {
                  screen: 'BranchMain',
                  params: {menu: 0, ex: 1},
                });
              }}>

          <View style={{flex: 1, flexDirection: 'column',  width: deviceWidth, height: 200, position: 'relative'}}>
              <View style={{zIndex: 1, position: 'absolute', width: deviceWidth/2, height: '100%', paddingLeft: 20, paddingTop: 10,backgroundColor: 'white', }}>
                  <View
                    style={{ zIndex: 1, }}>
                      <Text
                        style={{ fontSize: 24, marginBottom: 5, color: '#78BAE7', fontFamily: 'FrontageCondensed-Bold',letterSpacing: -4,}}>
                      YİYECEKLER VE İÇECEKLER
                      </Text>
                        <Text
                        style={{ fontSize: 24, marginBottom: 5, color: '#ff4495', fontFamily: 'FrontageCondensed-Bold', letterSpacing: -2,}}>
                      MENÜMÜZÜ KEŞFET
                      </Text>

                        <Text style={{ fontSize: 20, color: '#78BAE7', backgroundColor: 'black',fontFamily: 'FrontageCondensed-Bold', letterSpacing: -2, paddingTop: 10, paddingLeft: 10, height: 50, marginTop: 20,  }}> MENÜDE NELER VAR?</Text>

               </View>


            </View>
          </View>

              <Image
                style={{height: 200, width: deviceWidth, zIndex: 0, position: 'absolute'}}
                source={require('../../../assets/img/subeler_back.jpg')}
                resizeMode="cover"
              />

</TouchableOpacity> */}



 <Image
              style={{height: 300, width: deviceWidth, zIndex: 0, position: 'absolute',  }}
              source={require('../../../assets/img/anasayfa_ust_back2.png')}
              resizeMode="stretch"
            />
     




{/* SADAKAT SİSTEMİ */}
<TouchableOpacity
              activeOpacity={0.6}
              style={{width:deviceWidth, height: 220, marginTop: 20, }}
              onPress={() => {
                navigation.navigate('Branch', {
                  screen: 'BranchMain',
                  params: {menu: 0, ex: 1},
                });
              }}>

          <View style={{flex: 1, flexDirection: 'row',  width: deviceWidth, height: 200, position: 'relative', backgroundColor: '#78BAE7',}}>
              <View style={{zIndex: 1, position: 'absolute', width: deviceWidth, height: '100%', paddingLeft: 20, paddingTop: 10,}}>
                  <View
                    style={{ zIndex: 1, height: 'auto', width: deviceWidth-40, }}>
                      <Text
                        style={{ fontSize: 30, color: '#ffffff', fontFamily: 'FrontageCondensed-Bold',}}>
                      SOJUBAR'DA
                      </Text>
                    <Text
                      style={{ fontSize: 44,color: '#ffffff', fontFamily: 'FrontageCondensed-Outline',}}>
                    PARA PUAN
                    </Text>
                       <Text
                      style={{ fontSize: 30,color: '#ffffff', fontFamily: 'FrontageCondensed-Regular',}}>
                     TOPLA & KAZAN
                    </Text>
               </View>

              <View style={{flexDirection: 'row', justifyContent: 'space-between', backgroundColor: 'black', height: 50, position: 'absolute', bottom: 0, left: 0, width: deviceWidth, zIndex: 2,}}>
                              <Text style={{ fontSize: 24, color: '#78BAE7', fontFamily: 'FrontageCondensed-Bold', paddingLeft: 20,  paddingTop: 8}}> SADAKAT SİSTEMİ</Text>
                              <View style={{paddingRight: 20, height: 50,alignItems: 'flex-end', justifyContent: 'center',}}>
                                      <Icon name="arrow-right-circle" type="feather" iconStyle={{color: '#78BAE7', fontSize: 32}}/>
                            </View>
              </View>
            </View>
          </View>

              <Image
                style={{height: 200, width: deviceWidth, zIndex: 0, position: 'absolute', opacity: 0.5, }}
                source={require('../../../assets/img/subeler_back.jpg')}
                resizeMode="cover"
              />

</TouchableOpacity>












{/* ŞUBELER */}
<TouchableOpacity
              activeOpacity={0.6}
              style={{width:deviceWidth, height: 220, marginTop: 20, }}
              onPress={() => {
                navigation.navigate('Branch', {
                  screen: 'BranchMain',
                  params: {menu: 0, ex: 1},
                });
              }}>

          <View style={{flex: 1, flexDirection: 'row',  width: deviceWidth, height: 200, position: 'relative'}}>
              <View style={{zIndex: 1, position: 'absolute', width: deviceWidth, height: '100%', justifyContent: 'center', alignItems: 'center'}}>
                  <View
                    style={{
                      zIndex: 1,
                      backgroundColor: 'rgba(0, 0, 0, 0.6)',
                      paddingVertical: 20,
                      paddingHorizontal: 30,
                      borderRadius: 15,
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: deviceWidth * 0.9,
                    }}>
                      <Text
                        style={{
                          fontSize: 30,
                          marginBottom: 10,
                          color: '#ffffff',
                          fontFamily: 'FrontageCondensed-Bold',
                          textAlign: 'center',
                        }}>
                      SOJUBAR'A SİZLERİ BEKLİYORUZ!
                      </Text>
                    <Text
                      style={{
                        fontSize: 18,
                        color: '#ffffff',
                        fontFamily: 'FrontageCondensed-Regular',
                        textAlign: 'center',
                        lineHeight: 24,
                      }}>
                    BUGÜN UĞRA VE YENİ FAVORİ LEZZETİNİ KEŞFET. SENİ AĞIRLAMAK İÇİN SABIRSIZLANIYORUZ!
                    </Text>
               </View>

              <View style={{flexDirection: 'row', justifyContent: 'space-between', backgroundColor: 'black', height: 50, position: 'absolute', bottom: 0, left: 0, width: deviceWidth, zIndex: 2}}>
                              <Text style={{ fontSize: 24, color: '#78BAE7', fontFamily: 'FrontageCondensed-Bold', paddingLeft: 20,  paddingTop: 8}}> ŞUBELERİ GÖR</Text>
                              <View style={{paddingRight: 20, height: 50,alignItems: 'flex-end', justifyContent: 'center',}}>
                                      <Icon name="arrow-right-circle" type="feather" iconStyle={{color: '#78BAE7', fontSize: 32}}/>
                            </View>
              </View>
            </View>
          </View>

              <Image
                style={{height: 200, width: deviceWidth, zIndex: 0, position: 'absolute'}}
                source={require('../../../assets/img/subeler_back.jpg')}
                resizeMode="cover"
              />

</TouchableOpacity>








{/* PARA PUAN */}
<TouchableOpacity
              activeOpacity={0.6}
              style={{width:deviceWidth, height: 220,marginTop: 20, }}
              onPress={() => {
                navigation.navigate('Branch', {
                  screen: 'BranchMain',
                  params: {menu: 0, ex: 1},
                });
              }}>

          <View style={{flex: 1, flexDirection: 'row',  width: deviceWidth, height: 200, position: 'relative'}}>
              <View style={{zIndex: 1, position: 'absolute', width: deviceWidth, height: '100%', paddingLeft: 20, paddingTop: 10,}}>
                  <View
                    style={{ zIndex: 1, height: 'auto', width: deviceWidth-100, }}>
                      <Text
                        style={{ fontSize: 44,  color: '#000', fontFamily: 'FrontageCondensed-Outline',letterSpacing: -4,}}>
                      PUAN KAZAN
                      </Text>
                        <Text
                        style={{ fontSize: 30, marginBottom: 5, color: '#ff4495', fontFamily: 'FrontageCondensed-Bold', letterSpacing: -2,}}>
                      MERCH ÜRÜNLERİ YAKINDA
                      </Text>
                    <Text
                      style={{ fontSize: 18,color: '#000', fontFamily: 'FrontageCondensed-Regular',}}>
                    SOJUBAR'A ÖZEL BERCH ÜRÜNLERİ YAKINDA!
                    </Text>
               </View>

              <View style={{flexDirection: 'row', justifyContent: 'space-between', backgroundColor: 'black', height: 50, position: 'absolute', bottom: 0, left: 0, width: deviceWidth, zIndex: 2}}>
                              <Text style={{ fontSize: 24, color: '#78BAE7', fontFamily: 'FrontageCondensed-Bold', paddingLeft: 20,  paddingTop: 8}}> MAĞAZAYI KEŞFET</Text>
                              <View style={{paddingRight: 20, height: 50,alignItems: 'flex-end', justifyContent: 'center',}}>
                                      <Icon name="arrow-right-circle" type="feather" iconStyle={{color: '#78BAE7', fontSize: 32}}/>
                            </View>
              </View>
            </View>
          </View>

              <Image
                style={{height: 200, width: deviceWidth, zIndex: 0, position: 'absolute'}}
                source={require('../../../assets/img/merch_back.jpg')}
                resizeMode="cover"
              />

</TouchableOpacity>







  

 


          {/* SOSYAL MEDYA İCONLARI */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            paddingVertical: 20,
            backgroundColor: 'rgba(0, 0, 0, 0.2)',
            marginTop: 20,
          }}>
            <Text style={{
              color: '#000',
              fontSize: 16,
              fontFamily: 'FrontageCondensed-Bold',
              marginRight: 15,
            }}>
              BİZİ TAKİP EDİN:
            </Text>


             <TouchableOpacity
              style={{
                backgroundColor: '#fff',
                borderRadius: 25,
                width: 50,
                height: 50,
                justifyContent: 'center',
                alignItems: 'center',
                marginHorizontal: 5,
              }}
              onPress={() => {
                Linking.openURL('https://www.instagram.com/sojubar.turkiye/');
              }}
            >
              <Icon
                name="instagram"
                type="feather"
                iconStyle={{
                  color: '#000',
                  fontSize: 24,
                }}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                backgroundColor: '#fff',
                borderRadius: 25,
                width: 50,
                height: 50,
                justifyContent: 'center',
                alignItems: 'center',
                marginHorizontal: 5,
              }}
              onPress={() => {
                Linking.openURL('https://www.tiktok.com/@sojubar.turkiye');
              }}
            >
              <Icon
                name="tiktok"
                type="fa-brands fa-tiktok"
                iconStyle={{
                  color: '#000',
                  fontSize: 24,
                }}
              />
            </TouchableOpacity>

          
            
          </View>

        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
};

const PromotionModalQueue = ({promotions = []}) => {
  const [queue, setQueue] = useState([]);
  const [currentPromotion, setCurrentPromotion] = useState(null);
  const [visible, setVisible] = useState(false);

  const closeModal = () => {
    setVisible(false);
    setCurrentPromotion(null);
  };

  useEffect(() => {
    if (promotions.length > 0) {
      setQueue(promotions);
    }
  }, [promotions]);

  useEffect(() => {
    if (!visible && queue?.length > 0) {
      const next = queue[0];
      setCurrentPromotion(next);
      setVisible(true);
      setQueue(prev => prev?.slice(1));
    }
  }, [queue, visible]);

  if (!currentPromotion || currentPromotion.type != 4) return null;

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={closeModal}
      statusBarTranslucent={true}>
      <View style={promotionModalStyles.centeredView}>
        <View style={promotionModalStyles.modalView}>
          <TouchableOpacity
            onPress={closeModal}
            style={promotionModalStyles.closeButton}>
            <Icon
              name="x"
              type="feather"
              iconStyle={promotionModalStyles.closeIcon}
            />
          </TouchableOpacity>
          <WebView source={{uri: currentPromotion?.link}} />
        </View>
      </View>
    </Modal>
  );
};
const CLOSE_BUTTON_SIZE = 40;
const promotionModalStyles = StyleSheet.create({
  modalView: {
    width: deviceWidth * 0.95,
    height: deviceHeight * 0.8,
    backgroundColor: 'yellow',
    borderRadius: 8,
    overflow: 'hidden',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    zIndex: 999,
  },
  closeButton: {
    position: 'absolute',
    right: 8,
    top: 8,
    height: CLOSE_BUTTON_SIZE,
    width: CLOSE_BUTTON_SIZE,
    backgroundColor: 'rgba(0,0,0,.7)',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: CLOSE_BUTTON_SIZE / 2,
    zIndex: 999,
  },
  closeIcon: {color: 'white', fontSize: 24},
});
const pagestyles = StyleSheet.create({
  home_item: {
    width: deviceWidth / 2,
    height: deviceWidth / 2,
  },
  home_item100: {
    width: deviceWidth,
    height: 98,
    marginVertical: 8,
    borderwidth: 1,
    bordercolor: 'red',
  },
  home_item50: {
    width: deviceWidth / 2,
    height: 48,
    marginVertical: 15,
  },
  home_item_slide: {
    width: deviceWidth,
    height: 200,
    paddingHorizontal: 10,
    marginVertical: 0,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  modalView: {
    borderRadius: 8,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default HomeScreen;
