import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import {Icon} from 'react-native-elements';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import {Context as UserContext} from '../../context/UserContext';
import LocalizationContext from '../../context/LocalizationContext';
import {useIsFocused} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import mobileApi from '../../api/mobileApi';

const SettingsPoint = ({navigation, route}) => {
  const isFocused = useIsFocused();
  const {state} = React.useContext(UserContext);
  const {t, locale} = React.useContext(LocalizationContext);
  const l = t('login');
  const [point, setPoint] = useState(0);
  const [results, setResults] = React.useState();

  const _readVal = async () => {
    await AsyncStorage.multiGet(['@userdata', '@token', '@memberid'])
      .then(r => {
        const data = JSON.parse(r[0][1]);
        const req = {memberId: r[2][1], token: r[1][1]};

        _getResults(req);
      })
      .catch(err => {
        console.log(err);
      });
  };

  const _getResults = async request => {
    await mobileApi
      .post(`/${locale}/getNotification`, request)
      .then(response => {
        if (response.data) {
          setResults(response.data);
        }
      });
  };

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    _readVal();
    if (route?.params?.page === 'HomePage') {
      navigation.setOptions({
        headerTruncatedBackTitle: '',
        headerBackTitle: '',
        headerLeft: () => null,
      });
    }
  }, []);

  if (!results) {
    return (
      <View style={{margin: 40, alignItems: 'center'}}>
        <Text style={{fontSize: 16, color:"black"}}>{t('notificationError')}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <SafeAreaView>
        <ScrollView>
          <View style={styles.mt15}></View>

          {results &&
            results.map((item, i) => {
              return (
                <View key={i}>
                  <View
                    style={[
                      styles.detay_kutu,
                      {marginHorizontal: 5, marginVertical: -5},
                    ]}>
                    <View
                      style={[
                        styles.detay_kutu_sol,
                        styles.bgrenk5,
                        {padding: 10, borderRadius: 10},
                      ]}>
                      <Text style={[styles.Txt_renk3, styles.text18]}>
                        {item.title}
                      </Text>
                      <Text
                        style={[styles.Txt_renk3, styles.text14, styles.mt10]}>
                        {item.msg}
                      </Text>
                      <Text style={[styles.gray, styles.text12, styles.mt10]}>
                        {item.date}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.ara}></View>
                </View>
              );
            })}
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default SettingsPoint;
