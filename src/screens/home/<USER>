import React, { useState, useEffect } from 'react';
import { SafeAreaView } from 'react-native';

import styles from '../../stylesheets/styles';
import LocalizationContext from '../../context/LocalizationContext';
import { useIsFocused } from '@react-navigation/native';
import mobileApi from '../../api/mobileApi';
import { WebView } from 'react-native-webview';

const SettingsInnovationsScreen = ({ navigation, route }) => {
  const isFocused = useIsFocused();
  const { locale } = React.useContext(LocalizationContext);

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    if (route?.params?.page === 'HomePage') {
      navigation.setOptions({
        headerTruncatedBackTitle: '',
        headerBackTitle: '',
        headerLeft: () => null,
      });
    }
  }, [isFocused]);

  return (
    <SafeAreaView style={styles.container}>
      <WebView
        source={{
           uri: `${mobileApi.defaults.baseURL}/page/sushicoclub.php?${new Date().getTime()}&lang=${locale}`,

        }}
        startInLoadingState={true}
        scalesPageToFit={true}
        style={{
          flex: 1,
        }}
      />
    </SafeAreaView>
  );
};

export default SettingsInnovationsScreen;
