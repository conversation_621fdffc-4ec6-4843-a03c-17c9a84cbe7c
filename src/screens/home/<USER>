import React, {useContext, useEffect} from 'react';
import {SafeAreaView} from 'react-native';
import {WebView} from 'react-native-webview';
import LocalizationContext from '../../context/LocalizationContext';
import styles from '../../stylesheets/styles';
const SlideInfoScreen = ({navigation, route}) => {
  const {locale} = useContext(LocalizationContext);
  const {sliderDetail} = route?.params;

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
  }, [locale]);

  return (
    <SafeAreaView style={styles.container}>
      <WebView
        source={{
          uri: sliderDetail?.link,
        }}
        style={{flex: 1}}
        startInLoadingState={true}
        scalesPageToFit={true}
      />
    </SafeAreaView>
  );
};

export default SlideInfoScreen;
