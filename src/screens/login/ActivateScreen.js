import React, {useEffect, useState, useContext} from 'react';
import {
  View,
  Image,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Modal,
  Pressable,
  ImageBackground,
  Dimensions,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import AsyncStorage from '@react-native-async-storage/async-storage';
import CodeInput from 'react-native-confirmation-code-input';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';

import {navigate} from '../../navigationRef';
import Loading from '../../components/Loading';
import mobileApi from '../../api/mobileApi';
import {safeParse} from '../../utils';

const deviceWidth = Dimensions.get('window').width;

const ActivateScreen = ({navigation, route}) => {
  const {state, sendActivation, updateActivation} = useContext(UserContext);
  //const { location } = route?.params;
  const timeout = 180;
  const [isLoading, setLoading] = useState(false);

  const [smsCode, setSmsCode] = useState('');
  const [phone, setPhone] = useState('');
  const [seconds, setSeconds] = useState(timeout);
  const [load, setLoad] = useState(0);
  const [err, setErr] = useState(0);
  const [errormsg, setErrormsg] = useState(0);
  const [cView, setCView] = useState(0);
  const [bView, setBView] = useState(1);

  const [appModal, setAppModal] = useState(false);
  const [returnImage, setReturnImage] = useState('');
  const [buttonText, setButtonText] = useState('');
  const [mainLink, setMainLink] = useState('');
  const [subLink, setSubLink] = useState();

  const {t, locale} = useContext(LocalizationContext);
  const l = t('login');

  const _checkCode = isValid => {
    if (!isValid) {
      setErr(1);
      setErrormsg(t('alert').smsCodeNotValid);
    } else {
      setErr(0);
      setErrormsg('');
      _updateActivation();
    }
  };

  const _sendActivation = async () => {
    await AsyncStorage.multiGet(['@phone', '@token', '@memberid']).then(
      response => {
        const data = {
          phone: response[0][1],
          location: route?.params?.location ? route?.params?.location : '1',
          token: response[1][1],
          memberId: response[2][1],
          locale: locale,
        };

        setPhone(response[0][1]);
        sendActivation({
          data: data,
          alert: t('alert'),
        }).then(r => {
          if (r.err == 1) {
            setBView(0);
            setCView(1);
          } else {
            setSmsCode(r.smsCode);
          }
          // console.log('www', r.err);
          //
        });
      },
    );
  };

  const _updateActivation = async () => {
    const storedValues = await AsyncStorage.multiGet([
      '@token',
      '@memberid',
      '@userdata',
    ]);

    const data = {
      token: storedValues[0][1],
      memberId: storedValues[1][1],
      locale: locale,
    };

    const updateActivationResponse = await updateActivation({
      data: data,
      alert: t('alert'),
    });
    console.log('response=>', updateActivationResponse);

    if (updateActivationResponse?.returnImage) {
      setReturnImage(
        updateActivationResponse.returnImage
          ? updateActivationResponse.returnImage
          : '',
      );
      setButtonText(updateActivationResponse.buttonText);
      setMainLink(updateActivationResponse.mainLink);
      setSubLink(updateActivationResponse.subLink);
      setAppModal(true);
    } else {
      const storedPromoInProgress = await AsyncStorage.getItem(
        'promoInProgress',
      );
      const promoInProgress = safeParse(storedPromoInProgress);

      if (promoInProgress?.deviceId) {
        console.log('CHECKSHAREDPROMO PAYLOAD =>', {
          ...promoInProgress,
          memberId: data.memberId,
          action: 'login',
        });
        const checkSharedPromoResponse = await mobileApi.post(
          `/${locale}/checkSharedPromo`,
          {
            ...promoInProgress,
            memberId: data.memberId,
            action: 'login',
          },
        );
        console.log(
          'CHECKSHAREDPROMO RESPONSE =>',
          checkSharedPromoResponse.data,
        );
        await AsyncStorage.removeItem('promoInProgress');
      }
      navigate('Home');
    }
  };

  const _resendActivation = () => {
    _sendActivation();
    setSeconds(timeout);
    setBView(0);
    setCView(1);
    setErr(0);
  };

  useEffect(() => {
    if (seconds > 0) {
      setTimeout(() => setSeconds(seconds - 1), 1000);
    } else {
      setSeconds(0);
      setCView(0);
      setBView(1);
    }
  }, [seconds]);

  useEffect(() => {
    navigation.setOptions({
      title: l.activateTitle,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
      headerLeft: () => null,
    });
    console.log('geldi');
    if (load == 0) {
      _sendActivation();
      setLoad(1);
      setBView(0);
      setCView(1);
    }
  }, []);

  return (
    <KeyboardAwareScrollView>
      <SafeAreaView>
        {returnImage ? (
          <Modal
            animationType="slide"
            transparent={true}
            visible={appModal}
            statusBarTranslucent={true}
            onRequestClose={() => {
              setAppModal(!appModal);
            }}>
            <View style={pageStyle.centeredView}>
              <View style={pageStyle.modalView}>
                <ImageBackground
                  style={{
                    width: deviceWidth,
                    height: deviceWidth * 1.2,
                    flexDirection: 'column',
                    justifyContent: 'flex-end',
                  }}
                  source={{
                    uri: `${
                      mobileApi.defaults.baseURL
                    }/assets/${locale}/modal/${returnImage}.jpg?${new Date().getHours()}`,
                  }}>
                  <View>
                    <Pressable
                      onPress={() =>
                        subLink
                          ? navigation.navigate(mainLink, {screen: subLink})
                          : navigation.navigate(mainLink)
                      }>
                      <View
                        style={[
                          sc.PrimaryButton,
                          {marginVertical: 30, padding: 5},
                        ]}>
                        <Text
                          style={[
                            sc.PrimaryButton_text,
                            {
                              textAlign: 'center',
                              fontSize: 16,
                              fontFamily: 'IBMPlexSans-SemiBold',
                            },
                          ]}>
                          {buttonText}
                        </Text>
                      </View>
                    </Pressable>
                  </View>
                </ImageBackground>
              </View>
            </View>
          </Modal>
        ) : null}
        <View style={(styles.container, pageStyle.pagecontainer)}>
          <View
            style={{
              alignItems: 'center',
              paddingHorizontal: 20,
            }}>
            <View style={{marginVertical: 40}}>
              <Image source={require('../../../assets/img/logo.png')} />
            </View>
            {state.err || err ? (
              <View style={{width: '100%', margin: 20}}>
                <Text style={{color: 'red'}}>
                  {state.errormsg ? state.errormsg : errormsg}
                </Text>
              </View>
            ) : null}
            <View style={{width: '100%', margin: 10, alignItems: 'center'}}>
              <Text style={[styles.bold, styles.text16, {textAlign: 'center'}]}>
                {phone} {l.activationSms}
              </Text>
            </View>
            <View style={{width: '60%'}}>
              <CodeInput
                //ref="codeInputRef2"
                secureTextEntry
                compareWithCode={smsCode}
                // compareWithCode="9090"
                activeColor="red"
                inactiveColor="#afb9c7"
                keyboardType="numeric"
                autoFocus={false}
                ignoreCase={true}
                inputPosition="center"
                size={50}
                codeLength={4}
                onFulfill={isValid => _checkCode(isValid)}
                containerStyle={{marginTop: 30}}
                codeInputStyle={{borderWidth: 1.5}}
              />
            </View>

            <View
              style={{
                marginHorizontal: 20,
                marginTop: 80,
                marginBottom: 40,
                width: '100%',
                alignItems: 'center',
              }}>
              {cView ? (
                <Text style={[styles.bold, styles.text20]}>
                  {seconds} {t('second')}
                </Text>
              ) : null}
            </View>
            {bView ? (
              <View>
                <View
                  style={{
                    width: '100%',
                    alignItems: 'center',
                  }}>
                  <Text style={[styles.bold, styles.text20]}>
                    {l.activateText1}
                  </Text>

                  <TouchableOpacity onPress={() => _resendActivation()}>
                    <Text
                      style={[
                        styles.bold,
                        styles.text16,
                        styles.red,
                        {marginTop: 20},
                      ]}>
                      {l.activateText2}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Text
                      style={[
                        styles.bold,
                        styles.text16,
                        styles.red,
                        {marginTop: 40},
                      ]}>
                      {l.activateText4}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : null}
          </View>
        </View>
      </SafeAreaView>
    </KeyboardAwareScrollView>
  );
};

const pageStyle = StyleSheet.create({
  pagecontainer: {backgroundColor: '#ffffff', flex: 1},
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  modalView: {
    borderRadius: 8,

    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default ActivateScreen;
