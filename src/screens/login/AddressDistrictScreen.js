import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import {ListItem} from 'react-native-elements';
import LocalizationContext from '../../context/LocalizationContext';

const AddressDistrict = ({navigation, route}) => {
  const {id, results} = route.params;
  const {t, locale} = React.useContext(LocalizationContext);

  React.useEffect(() => {
    navigation.setOptions({
      title: t('login').selectDistrict,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    console.log(route, route?.params?.results?.length);
  }, []);

  const _goBack = (id, name, index) => {
    navigation.goBack();
    route.params._onDistrict(id, name, index);
  };
  return (
    <ScrollView>
      <View style={styles.group}>
        {results?.distinct?.length > 0 ? (
          Object.keys(results.distinct).map((item, i) => (
            <TouchableOpacity
              key={i}
              onPress={() =>
                _goBack(
                  results.distinct[item].id,
                  results.distinct[item].name,
                  i,
                )
              }>
              <ListItem bottomDivider>
                <ListItem.Content>
                  <ListItem.Title>{results.distinct[item].name}</ListItem.Title>
                </ListItem.Content>
                <ListItem.Chevron />
              </ListItem>
            </TouchableOpacity>
          ))
        ) : (
          <View
            style={{
              alignItems: 'center',
              paddingHorizontal: 25,
              paddingTop: 10,
              fontSize: 20,
            }}>
            <Text>
              {results?.name} {t('districtNotFount')}
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 20,
  },
});

export default AddressDistrict;
