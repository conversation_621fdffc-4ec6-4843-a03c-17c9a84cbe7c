import React, {useState, useEffect, useContext} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {Icon} from 'react-native-elements';
import AsyncStorage from '@react-native-async-storage/async-storage';
import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';

import mobileApi from '../../api/mobileApi';
import {Context as UserContext} from '../../context/UserContext';
import {navigate} from '../../navigationRef';
import VersionNumber from 'react-native-version-number';
import Loading from '../../components/Loading';
import {appVersion} from '../../constants';

const AddressScreen = ({navigation, route}) => {
  const {mode, id, page} = route.params;
  const {fromStack} = route.params || {};
  const {t, locale} = useContext(LocalizationContext);
  const {state, loginCheck, saveAddress, login} = useContext(UserContext);
  const [results, setResults] = useState([]);
  const [addressname, setAddressname] = useState();
  const [firstname, setFirstname] = useState();
  const [lastname, setLastname] = useState();
  const [email, setEmail] = useState();
  const [phone, setPhone] = useState();
  const [address, setAddress] = useState();
  const [addressDesc, setAddressDesc] = useState();
  const [branchs, setBranchs] = useState([]);
  const [cityIndex, setCityIndex] = useState(null);
  const [cityId, setCityId] = useState(null);
  const [cityName, setCityName] = useState(null);
  const [townIndex, setTownIndex] = useState(null);
  const [townId, setTownId] = useState(null);
  const [townName, setTownName] = useState(null);
  const [districtId, setDistrictId] = useState(null);
  const [districtName, setDistrictName] = useState(null);
  const [addressMode, setAddressMode] = useState('new');
  const [addressId, setAddressId] = useState();
  const [loginEmail, setLoginEmail] = useState();
  const [loginPassword, setLoginPassword] = useState();
  const [loginToken, setLoginToken] = useState();
  const [loginMemberId, setLoginMemberId] = useState();
  const [isLoading, setIsLoading] = useState(false);
  const l = t('login');
  const p = t('payment');

  const _readVal = async (mode, id, data) => {
    await AsyncStorage.multiGet([
      '@userdata',
      '@email',
      '@password',
      '@token',
      '@memberid',
    ]).then(response => {
      const r = response ? JSON.parse(response[0][1]) : [];
      setLoginEmail(response[1][1]);
      setLoginPassword(response[2][1]);
      setLoginToken(response[3][1]);
      setLoginMemberId(response[4][1]);

      if (mode === 'update') {
        const index = getIndex(r.address, id);

        const cityIndex = getIndex(data.city, r.address[index].city);
        const townIndex = getIndex(
          data.city[cityIndex].county,
          r.address[index].district,
        );
        setAddressname(r.address[index].label);
        setEmail(r.address[index].email);
        setPhone(r.address[index].phone);
        setLastname(r.address[index].surname);
        setFirstname(r.address[index].name);
        setCityName(r.address[index].cityName);
        setCityId(r.address[index].city);
        setCityIndex(cityIndex);
        setTownName(r.address[index].districtName);
        setTownId(r.address[index].district);
        setTownIndex(townIndex);
        setDistrictName(r.address[index].regionName);
        setDistrictId(r.address[index].region);
        setAddress(r.address[index].detail);
        setAddressDesc(r.address[index].directions);
        setBranchs(r.address[index].branchs);
      } else {
        setEmail(r.email);
        setPhone(r.phone);
        setLastname(r.lastname);
        setFirstname(r.firstname);
        setAddressname('');
        setCityName('');
        setCityId('');
        setCityIndex('');
        setTownName('');
        setTownId('');
        setTownIndex('');
        setDistrictName('');
        setDistrictId('');
        setAddress('');
        setAddressDesc('');
        setBranchs([]);
      }
    });
  };

  function getIndex(arr, id) {
    return arr.findIndex(obj => obj.id === id);
  }

  const _saveAddress = () => {
    const data = {
      label: addressname,
      name: firstname,
      surname: lastname,
      email,
      phone,
      detail: address,
      directions: addressDesc,
      region: districtId,
      city: cityId,
      district: townId,
      token: loginToken,
      memberId: loginMemberId,
      action: addressMode,
      addressid: addressId,
      locale,
    };

    setIsLoading(true);
    saveAddress({
      data: data,
      alert: t('alert'),
    })
      .then(resp => {
        let msg = '';
        addressMode === 'new'
          ? (msg = t('login').addedAddress)
          : (msg = t('login').updatedAddress);
        if (resp.err === '') {
          const data = {
            email: loginEmail,
            password: loginPassword,
            locale,
            version: appVersion,
          };
          login({data: data, alert: t('alert')})
            .then(() => {
              Alert.alert(t('login').congratulations, msg, [
                {
                  text: t('alert').okBtn,
                  onPress: () => {
                    //navigates back to the previous stack and screen
                    navigate(fromStack);
                    // navigate('Settings');
                  },
                },
              ]);
            })
            .catch(err => {
              console.log('login promise chain error', err);
            })
            .finally(() => {
              setIsLoading(false);
            });
        }
      })
      .catch(err => {
        console.log('saveAddress promise chain error', err);
        setIsLoading(false);
      });
  };

  const _onCity = (id, name, index) => {
    setCityName(name);
    setCityId(id);
    setCityIndex(index);
    setTownName('');
    setTownId('');
    setTownIndex('');
    setDistrictName('');
    setDistrictId('');
    setBranchs([]);
  };

  const _onTown = (id, name, index) => {
    setTownName(name);
    setTownId(id);
    setTownIndex(index);
    setDistrictName('');
    setDistrictId('');
    setBranchs([]);
  };

  const _onDistrict = (id, name, index) => {
    setDistrictName(name);
    setDistrictId(id);
  };

  const _getResults = async (mode, id) => {
    await mobileApi.get(`/${locale}/getDistincts`).then(response => {
      _readVal(mode, id, response.data);
      setResults(response.data);
    });
  };

  const getBranchs = async () => {
    const response = await mobileApi.get(
      `/${locale}/getDistinctRestaurants?id=${districtId}`,
    );
    if (response.data) {
      setBranchs(response.data);
    } else {
      setBranchs([]);
    }
  };

  useEffect(() => {
    if (districtId) {
      getBranchs();
    }
  }, [districtId]);

  useEffect(() => {
    if (mode === 'update') {
      setAddressMode(mode);
      setAddressId(id);
    }
    navigation.setOptions({
      title: mode === 'new' ? l.addressAddTitle : l.addressEditTitle,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    loginCheck();
    _getResults(mode, id);
  }, [id]);

  if (!results) {
    return null;
  }

  if (isLoading) {
    return <Loading />;
  }

  return (
    <KeyboardAwareScrollView
      style={(pageStyle.container, pageStyle.pagecontainer)}>
      <ScrollView>
        <View
          style={{
            alignItems: 'center',
            paddingHorizontal: 10,
            paddingTop: 10,
          }}>
          {/* <View style={[styles.labelline, { alignSelf: 'flex-start' }]}>
            <Text style={styles.labelStyle}>{l.addressName}</Text>
          </View> */}
          <View style={styles.kutu_full}>
            <TextInput
              style={styles.InputStyle}
              placeholder={l.addressName}
              autoCorrect={false}
              value={addressname}
              autoFocus
              placeholderTextColor="#000"
              onChangeText={value => setAddressname(value)}
            />
          </View>
          <View style={styles.kutu_full}>
            <TextInput
              style={styles.InputStyle}
              placeholder={l.firstname}
              autoCorrect={false}
              value={firstname}
              placeholderTextColor="#000"
              onChangeText={value => setFirstname(value)}
            />
          </View>
          <View style={styles.kutu_full}>
            <TextInput
              style={styles.InputStyle}
              placeholder={l.lastname}
              autoCorrect={false}
              value={lastname}
              placeholderTextColor="#000"
              onChangeText={value => setLastname(value)}
            />
          </View>
          <View style={styles.kutu_full}>
            <TextInput
              style={styles.InputStyle}
              placeholder={l.phone}
              autoCorrect={false}
              value={phone}
              maxLength={10}
              placeholderTextColor="#000"
              onChangeText={value => setPhone(value)}
            />
          </View>

          <View
            style={{
              width: '100%',
            }}></View>
          <View style={{width: '96%'}}>
            <TouchableOpacity
              style={[styles.listeleme]}
              onPress={() =>
                navigate('AddressCity', {
                  results,
                  _onCity: _onCity,
                })
              }>
              <View style={styles.kutu_full}>
                <View style={styles.listeleme_orta}>
                  <Text style={[styles.text16, styles.black]}>
                    {cityName ? cityName : t('login').selectCity}
                  </Text>
                </View>
                <View style={styles.listeleme_sag}>
                  <Icon name="angle-right" color="gray" type="font-awesome" />
                </View>
              </View>
            </TouchableOpacity>
          </View>
          {cityId || addressMode == 'update' ? (
            <View style={{width: '96%'}}>
              <TouchableOpacity
                style={styles.listeleme}
                onPress={() =>
                  navigate('AddressTown', {
                    results: results.city[cityIndex],
                    _onTown: _onTown,
                  })
                }>
                <View style={styles.kutu_full}>
                  <View style={styles.listeleme_orta}>
                    <Text style={[styles.text16, styles.black]}>
                      {townName ? townName : t('login').selectTown}
                    </Text>
                  </View>
                  <View style={styles.listeleme_sag}>
                    <Icon name="angle-right" color="gray" type="font-awesome" />
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          ) : null}
          {townId || addressMode == 'update' ? (
            <View style={{width: '96%'}}>
              <TouchableOpacity
                style={styles.listeleme}
                onPress={() => {
                  console.log(results.city[cityIndex].county[townIndex]),
                    navigate('AddressDistrict', {
                      results: results.city[cityIndex].county[townIndex],
                      _onDistrict: _onDistrict,
                    });
                }}>
                <View style={styles.kutu_full}>
                  <View style={styles.listeleme_orta}>
                    <Text style={[styles.text16, styles.black]}>
                      {districtName ? districtName : t('login').selectDistrict}
                    </Text>
                  </View>
                  <View style={styles.listeleme_sag}>
                    <Icon name="angle-right" color="gray" type="font-awesome" />
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          ) : null}

          {branchs && branchs.length > 0 && districtId ? (
            <View style={{width: '90%', marginTop: 20}}>
              <Text style={[styles.text16,styles.Txt_renk5, {paddingLeft: 5, fontFamily: 'AndaleMono', letterSpacing: -1}]}>
                {p.branchlist}
              </Text>
            </View>
          ) : districtId ? (
            <View style={[styles.alertbox, {width: '90%'}]}>
              <Text style={[styles.text18, styles.Txt_renk1]}>
                {p.nobranchlist}
              </Text>
            </View>
          ) : null}

          {branchs &&
            branchs.map(item => {
              return (
                <View style={{width: '100%'}} key={item.branchId}>
                  <View style={styles.listeleme}>
                    <View style={styles.kutu_full}>
                      <View style={styles.listeleme_orta}>
                        <Text style={[styles.SubeAd, styles.Txt_renk1]}>
                          {item.branchName}
                        </Text>
                        <Text style={[styles.SubeAdres, styles.black]}>
                          {' '}
                          {p.minamount}: {item.price} {t('currency')}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              );
            })}
          <View style={styles.kutu_full}>
            <TextInput
              style={styles.InputStyle}
              placeholder={l.addressDetail}
              autoCorrect={false}
              value={address}
              placeholderTextColor="#000"
              onChangeText={value => setAddress(value)}
            />
          </View>
          <View style={styles.kutu_full}>
            <TextInput
              style={styles.InputStyle}
              placeholder={l.addressDesc}
              autoCorrect={false}
              value={addressDesc}
              placeholderTextColor="#000"
              onChangeText={value => setAddressDesc(value)}
            />
          </View>
          {state.err ? (
            <View style={styles.alertbox}>
              <Text style={{color: 'red'}}>{state.errormsg}</Text>
            </View>
          ) : null}
          {page === 1 ? (
            <View style={styles.detay_kutu}>
              <View style={styles.detay_kutu_center}>
                <View style={styles.buton_block}>
                  <TouchableOpacity
                    onPress={() => navigation.navigate('Settings')}
                    style={styles.btn_1}>
                    <Text style={[styles.text14, styles.ozelfont_bold]}>
                      {t('alert.cancelBtn')}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity onPress={() => _saveAddress()}>
                    <View style={styles.btn_13}>
                      <Text
                        style={[
                          styles.text14,
                          styles.white,
                          styles.ozelfont_bold,
                        ]}>
                        {t('button.save')}
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          ) : (
            <View style={styles.detay_kutu}>
              <TouchableOpacity
                onPress={() => _saveAddress()}
                style={[sc.SecondaryButton_small, styles.bgrenk1]}>
                <Text style={[sc.SecondaryButton_text, {lineHeight: 25}]}>
                  {t('button.save')}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>
    </KeyboardAwareScrollView>
  );
};

const pageStyle = StyleSheet.create({
  pagecontainer: {backgroundColor: '#ffffff', flex: 1},
  item: {
    fontSize: 25,
    color: 'red',
    textAlign: 'left',
    fontWeight: 'bold',
    flex: 1,
  },
});

export default AddressScreen;
