import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import {ListItem} from 'react-native-elements';
import LocalizationContext from '../../context/LocalizationContext';

const AddressTown = ({navigation, route}) => {
  const {id, results} = route.params;
  const {t, locale} = React.useContext(LocalizationContext);

  React.useEffect(() => {
    navigation.setOptions({
      title: t('login').selectTown,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
  }, []);

  const _goBack = (id, name, index) => {
    navigation.goBack();
    route.params._onTown(id, name, index);
  };

  if (!results) {
    return null;
  }
  return (
    <ScrollView>
      <View style={styles.group}>
        {Object.keys(results.county).map(key => (
          <TouchableOpacity
            key={key}
            onPress={() =>
              _goBack(results.county[key].id, results.county[key].name, key)
            }>
            <ListItem bottomDivider>
              <ListItem.Content>
                <ListItem.Title style={{fontFamily: 'AndaleMono', letterSpacing: -1}}
                >{results.county[key].name}</ListItem.Title>
              </ListItem.Content>
              <ListItem.Chevron />
            </ListItem>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 20,
  },
});

export default AddressTown;
