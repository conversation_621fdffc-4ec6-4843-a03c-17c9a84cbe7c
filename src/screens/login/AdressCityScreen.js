import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, LogBox } from 'react-native';
import { ListItem } from 'react-native-elements';
import LocalizationContext from '../../context/LocalizationContext';

LogBox.ignoreLogs(['Non-serializable values were found in the navigation state']);

const AddressCity = ({ navigation, route }) => {
  const { results, id } = route.params;
  const { t, locale } = React.useContext(LocalizationContext);

  React.useEffect(() => {
    navigation.setOptions({
      title: t('login').selectCity,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
  }, []);

  const _goBack = (id, name, index) => {
    route.params._onCity(id, name, index);
    navigation.goBack();
  };

  if (!results) {
    return null;
  }

  return (
    <ScrollView>
      <View style={styles.group}>
        {Object.keys(results.city).map((item, index) => (
          <TouchableOpacity key={index} onPress={() => _goBack(results.city[item].id, results.city[item].name, index)}>
            <ListItem bottomDivider>
              <ListItem.Content>
                <ListItem.Title style={{ fontFamily: 'AndaleMono', letterSpacing: -1 }}
                >{results.city[item].name}</ListItem.Title>
              </ListItem.Content>
            </ListItem>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  group: {
    borderRadius: 20,
    margin: 20,
  },
  list: { borderRadius: 20 },
});

export default AddressCity;
