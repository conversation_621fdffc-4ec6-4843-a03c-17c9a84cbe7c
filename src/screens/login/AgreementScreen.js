import React, { useState, useEffect } from "react";
import { View, Image, Text, TextInput, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView } from "react-native";
import { CheckBox } from "react-native-elements";

import styles from "../../stylesheets/styles";
import * as sc from "../../stylesheets/stylesShort";
import LocalizationContext from "../../context/LocalizationContext";
import { Context as UserContext } from "../../context/UserContext";
import { useIsFocused } from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { WebView } from "react-native-webview";
import mobileApi from "../../api/mobileApi";

const AgreementScreen = ({ navigation, route }) => {
  const isFocused = useIsFocused();
  const { state } = React.useContext(UserContext);
  const { t, locale } = React.useContext(LocalizationContext);
  const p = t("profile");

  useEffect(() => {
    const unsubscribe = navigation.addListener("focus", () => {
      navigation.setOptions({
        title: t("login").registerTitle,
        headerTruncatedBackTitle: "",
        headerBackTitle: "",
      });
      console.log(route.params);
    });

    return unsubscribe;
  }, [navigation]);

  return (
    <SafeAreaView style={styles.container}>
      <WebView
        source={{
          uri: `${mobileApi.defaults.baseURL}/${locale}-site-kullanim-sartlari.php`,
        }}
        cacheEnabled={false}
        startInLoadingState={true}
        scalesPageToFit={true}
        style={{
          flex: 1,
        }}
      />
    </SafeAreaView>
  );
};

export default AgreementScreen;
