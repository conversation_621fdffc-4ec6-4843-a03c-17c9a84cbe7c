import React, {useState, useContext, useEffect} from 'react';
import {
  View,
  Image,
  Text,
  TextInput,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';

import {navigate} from '../../navigationRef';
import {useIsFocused} from '@react-navigation/native';
import Loading from '../../components/Loading';
import {PasswordChecklist} from './components';

const ChangePasswordScreen = ({navigation}) => {
  const isFocused = useIsFocused();

  const [isLoading, setLoading] = useState(false);

  const {state, changepassword, clearErrorMessage} = useContext(UserContext);
  const {t, locale} = useContext(LocalizationContext);
  const [password, setPassword] = useState();
  const [repassword, setRepassword] = useState();

  const l = t('login');

  useEffect(() => {
    navigation.setOptions({
      title: l.changePasswordTitle,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
      headerLeft: () => null,
    });
    clearErrorMessage();
  }, [isFocused]);

  const _changepassword = async () => {
    setLoading(true);
    const data = {password, repassword, memberid: state.memberid, locale};
    await changepassword({data: data, alert: t('alert'), locale}).then(resp => {
      setLoading(false);
      console.log('res', resp);
      if (resp.err === 0) {
        navigate('Login');
      }
    });
  };
  if (isLoading) {
    return <Loading />;
  }
  return (
    <View style={(styles.container, pageStyle.pagecontainer)}>
      <SafeAreaView>
        <View
          style={{
            alignItems: 'center',
            paddingHorizontal: 30,
          }}>
          <View style={{marginVertical: 40}}>
            <Image source={require('../../../assets/img/logo.png')} />
          </View>
          <View style={[styles.labelline, {alignSelf: 'flex-start'}]}>
            <Text style={styles.labelStyle}>{l.password}</Text>
          </View>
          <View style={styles.kutu_full}>
            <TextInput
              style={styles.InputStyle}
              placeholder=""
              autoCapitalize="none"
              autoCorrect={false}
              secureTextEntry={true}
              value={password}
              placeholderTextColor="#000"
              onChangeText={value => setPassword(value)}
            />
          </View>
          <View style={[styles.labelline, {alignSelf: 'flex-start'}]}>
            <Text style={styles.labelStyle}>{l.repassword}</Text>
          </View>
          <View style={styles.kutu_full}>
            <TextInput
              style={styles.InputStyle}
              placeholder=""
              autoCapitalize="none"
              autoCorrect={false}
              secureTextEntry={true}
              value={repassword}
              placeholderTextColor="#000"
              onChangeText={value => setRepassword(value)}
            />
          </View>
          <View style={styles.kutu_full}>
            <PasswordChecklist />
          </View>

          {state.err ? (
            <View style={{width: '100%', margin: 20}}>
              <Text style={{color: 'red'}}>{state.errormsg}</Text>
            </View>
          ) : null}

          <TouchableOpacity
            style={[sc.PrimaryButton]}
            onPress={() => _changepassword()}>
            <Text style={sc.PrimaryButton_text}>{t('button.update')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </View>
  );
};

const pageStyle = StyleSheet.create({
  pagecontainer: {backgroundColor: '#F3EFE5', flex: 1},
});

export default ChangePasswordScreen;
