import React, {useEffect, useState, useContext} from 'react';
import {
  View,
  Image,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import CodeInput from 'react-native-confirmation-code-input';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';

import {navigate} from '../../navigationRef';
import Loading from '../../components/Loading';

const ForgotCodeScreen = ({navigation}) => {
  const {state, forgot} = useContext(UserContext);
  const [isLoading, setLoading] = useState(false);

  const timeout = 180;

  const [seconds, setSeconds] = useState(timeout);
  const [load, setLoad] = useState(0);
  const [err, setErr] = useState(0);
  const [errormsg, setErrormsg] = useState(0);
  const [cView, setCView] = useState(0);
  const [bView, setBView] = useState(1);

  const {t, locale} = useContext(LocalizationContext);
  const l = t('login');

  const _checkCode = isValid => {
    if (!isValid) {
      setErr(1);
      setErrormsg(t('alert').emailCodeNotValid);
    } else {
      setErr(0);
      setErrormsg('');
      navigate('ChangePassword');
    }
  };

  const _resendActivation = () => {
    setSeconds(timeout);
    setBView(0);
    setCView(1);
    setErr(0);
    navigation.goBack();
  };

  useEffect(() => {
    navigation.setOptions({
      title: l.forgotTitle,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
      headerLeft: () => null,
    });
    if (load == 0) {
      setLoad(1);
      setBView(0);
      setCView(1);
    }
  }, []);

  useEffect(() => {
    if (seconds > 0) {
      setTimeout(() => setSeconds(seconds - 1), 1000);
    } else {
      setSeconds(0);
      setCView(0);
      setBView(1);
    }
  }, [seconds]);

  return (
    <KeyboardAwareScrollView>
      <SafeAreaView>
        <View style={(styles.container, pageStyle.pagecontainer)}>
          <ImageBackground
            source={require('../../../assets/img/back_login.jpg')}
            resizeMode="cover"
            style={{flex: 1, marginTop: 0}}>
            <SafeAreaView>
              <View
                style={{
                  alignItems: 'center',
                  paddingHorizontal: 20,
                }}>
                <View style={{marginVertical: 40}}>
                  <Image source={require('../../../assets/img/logo.png')} />
                </View>
                {state.err || err ? (
                  <View style={{width: '100%', margin: 20}}>
                    <Text style={{color: 'red'}}>
                      {state.errormsg ? state.errormsg : errormsg}
                    </Text>
                  </View>
                ) : null}
                <View style={{width: '100%', margin: 10, alignItems: 'center'}}>
                  <Text style={[styles.bold, styles.text16, styles.black]}>
                    {l.forgotPasswordTitle}
                  </Text>
                </View>
                <View style={{width: '60%'}}>
                  <CodeInput
                    //ref="codeInputRef2"
                    secureTextEntry
                    compareWithCode={state.code}
                    activeColor="red"
                    inactiveColor="#afb9c7"
                    keyboardType="numeric"
                    autoFocus={false}
                    ignoreCase={true}
                    inputPosition="center"
                    size={50}
                    codeLength={4}
                    onFulfill={isValid => _checkCode(isValid)}
                    containerStyle={{marginTop: 30}}
                    codeInputStyle={{borderWidth: 1.5}}
                  />
                </View>

                <View
                  style={{
                    marginHorizontal: 20,
                    marginTop: 80,
                    marginBottom: 40,
                    width: '100%',
                    alignItems: 'center',
                  }}>
                  {cView ? (
                    <Text style={[styles.bold, styles.text20, styles.black]}>
                      {seconds} {l.saniye}
                    </Text>
                  ) : null}
                </View>
                {bView ? (
                  <View
                    style={{
                      width: '100%',
                      alignItems: 'center',
                    }}>
                    <Text style={[styles.bold, styles.text20, styles.black]}>
                      {l.activateText5}
                    </Text>

                    <TouchableOpacity onPress={() => _resendActivation()}>
                      <Text
                        style={[
                          styles.bold,
                          styles.text16,
                          styles.red,
                          {marginTop: 20},
                        ]}>
                        {l.activateText2}
                      </Text>
                    </TouchableOpacity>
                  </View>
                ) : null}
              </View>
            </SafeAreaView>
          </ImageBackground>
        </View>
      </SafeAreaView>
    </KeyboardAwareScrollView>
  );
};

const pageStyle = StyleSheet.create({
  pagecontainer: {backgroundColor: '#F3EFE5', flex: 1},
});

export default ForgotCodeScreen;
