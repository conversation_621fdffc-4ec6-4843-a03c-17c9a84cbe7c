import React, {useState, useContext, useEffect} from 'react';
import {
  View,
  Image,
  Text,
  TextInput,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ImageBackground,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';
import {CheckBox} from 'react-native-elements';

import {navigate} from '../../navigationRef';
import {useIsFocused} from '@react-navigation/native';
import Loading from '../../components/Loading';

const ForgotScreen = ({navigation}) => {
  const {t, locale} = useContext(LocalizationContext);
  const {state, forgot, clearErrorMessage} = useContext(UserContext);
  const l = t('login');
  const isFocused = useIsFocused();
  const [phone, setPhone] = useState();
  const [isLoading, setLoading] = useState(false);
  const [location, setLocation] = useState(1);

  const _forgot = async () => {
    setLoading(true);
    const req = {phone, location, alert: t('alert'), locale};
    await forgot(req).then(response => {
      setLoading(false);
      // console.log(response);
      if (response.err === 0) {
        navigate('ForgotCode');
      }
    });
  };

  useEffect(() => {
    navigation.setOptions({
      title: l.forgotTitle,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    clearErrorMessage();
  }, [isFocused]);

  if (isLoading) {
    return <Loading />;
  }
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView contentContainerStyle={{flexGrow: 1}}>
          <View style={(styles.container, pageStyle.pagecontainer)}>
            <ImageBackground
              source={require('../../../assets/img/back_register.jpg')}
              resizeMode="cover"
              style={{flex: 1, marginTop: 0}}>
              <SafeAreaView>
                <View style={{alignItems: 'center', paddingHorizontal: 30}}>
                  <View style={{marginVertical: 40}}>
                    <Image
                      source={require('../../../assets/img/logo.png')}
                      style={styles.logokucuk}
                    />
                  </View>
                  <View style={[styles.labelline, {alignSelf: 'flex-start'}]}>
                    <Text style={styles.labelStyle}>
                      {l.phone} {l.phoneFormat}
                    </Text>
                  </View>
                  <View style={styles.kutu_full}>
                    <TextInput
                      style={styles.InputStyle}
                      autoCapitalize="none"
                      autoCorrect={false}
                      value={phone}
                      keyboardType="decimal-pad"
                      onChangeText={value => setPhone(value)}
                    />
                  </View>
                  <View style={[styles.labelline, {alignSelf: 'flex-start'}]}>
                    <Text style={styles.labelStyle}>{l.location}</Text>
                  </View>

                  <View style={{width: '100%'}}>
                    <CheckBox
                      left
                      title={l.Turkiye}
                      checkedIcon="check"
                      uncheckedIcon="circle-o"
                      checkedColor="green"
                      textStyle={{fontFamily: 'IBMPlexSans-Medium'}}
                      checked={location === 2 ? '' : location}
                      onPress={() => setLocation(1)}
                    />
                  </View>
                  <View style={{width: '100%'}}>
                    <CheckBox
                      left
                      title={l.KKTC}
                      checkedIcon="check"
                      uncheckedIcon="circle-o"
                      checkedColor="green"
                      textStyle={{fontFamily: 'IBMPlexSans-Medium'}}
                      checked={location === 1 ? '' : location}
                      onPress={() => setLocation(2)}
                    />
                  </View>

                  {state.err ? (
                    <View style={[styles.alertbox, {alignSelf: 'center'}]}>
                      <Image
                        source={require('../../../assets/img/icon_uyari.png')}
                        style={{
                          height: 32,
                          width: 32,
                          marginBottom: 10,
                          resizeMode: 'contain',
                        }}
                      />
                      <Text style={{color: 'red'}}>{state.errormsg}</Text>
                    </View>
                  ) : null}

                  <TouchableOpacity
                    style={{marginVertical: 30}}
                    onPress={() => _forgot()}>
                    <View style={sc.PrimaryButton}>
                      <Text
                        style={[sc.PrimaryButton_text, styles.ozelfont_medium]}>
                        {t('button.send')}
                      </Text>
                    </View>
                  </TouchableOpacity>

                  <View
                    style={{margin: 10, width: '100%', alignItems: 'center'}}>
                    <TouchableOpacity onPress={() => navigate('Login')}>
                      <Text
                        style={[
                          styles.red,
                          styles.bold,
                          styles.text16,
                          styles.ozelfont_medium,
                        ]}>
                        {l.forgotText2}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </SafeAreaView>
            </ImageBackground>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
};

const pageStyle = StyleSheet.create({
  pagecontainer: {backgroundColor: '#F3EFE5', flex: 1},
});

export default ForgotScreen;
