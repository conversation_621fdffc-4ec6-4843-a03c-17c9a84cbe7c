import React, {useState} from 'react';
import {
  View,
  Image,
  Text,
  TextInput,
  SafeAreaView,
  TouchableOpacity,
  ImageBackground,
} from 'react-native';

import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';

import {navigate} from '../../navigationRef';
import {useIsFocused} from '@react-navigation/native';
import VersionNumber from 'react-native-version-number';
import Loading from '../../components/Loading';
import AsyncStorage from '@react-native-async-storage/async-storage';
import mobileApi from '../../api/mobileApi';
import {safeParse} from '../../utils';
import Clipboard from '@react-native-clipboard/clipboard';

const LoginScreen = ({navigation, route}) => {
  const isFocused = useIsFocused();

  const {state, login, clearErrorMessage} = React.useContext(UserContext);
  const {t, locale} = React.useContext(LocalizationContext);
  const [email, setEmail] = useState();
  const [password, setPassword] = useState();
  const l = t('login');
  const [isLoading, setLoading] = useState(false);

  const [promoInProgress, setPromoInProgress] = useState({});

  React.useEffect(() => {
    navigation.setOptions({
      title: l.loginTitle,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
      headerLeft: () => null,
    });
    clearErrorMessage();
    setEmail('');
    setPassword('');

    AsyncStorage.getItem('promoInProgress').then(storedPromoInProgress => {
      const promoInProgress = safeParse(storedPromoInProgress);
      setPromoInProgress(promoInProgress || {});
    });
  }, [isFocused]);

  const _login = async () => {
    setLoading(true);
    const data = {
      email,
      password,
      locale,
      redirect: 1,
      basketreset: 1,
      fast: -1,
      version: VersionNumber.appVersion,
    };

    const loginResponse = await login({data: data, alert: t('alert')});

    setLoading(false);

    if (loginResponse.err === 0) {
      if (loginResponse?.activation !== '1') {
        navigation.navigate('Activate');
      }

      const memberId = await AsyncStorage.getItem('@memberid');
      console.log('CHECKSHAREDPROMO PAYLOAD ON LOGIN =>', {
        ...promoInProgress,
        memberId,
        action: 'login',
      });

      try {
        const checkSharedPromoResponse = await mobileApi.post(
          `/${locale}/checkSharedPromo`,
          {
            ...promoInProgress,
            memberId,
            action: 'login',
          },
        );
        console.log(
          'CHECKSHAREDPROMO RESPONSE =>',
          checkSharedPromoResponse.data,
        );
        await AsyncStorage.removeItem('promoInProgress');
        Clipboard.setString('');
      } catch (error) {
        console.log('ERROR =>', error);
      }
      if (route?.params?.page) {
        return navigation.navigate('Home');
      }
      return navigation.navigate('Orders', {screen: 'MainScreen'});
    }
  };

  if (isLoading) {
    return <Loading />;
  }
  return (
    <ImageBackground
      source={require('../../../assets/img/back_alt.jpg')}
      resizeMode="cover"
      style={{flex: 1, marginTop: 0}}>
      <KeyboardAwareScrollView>
        <SafeAreaView>
          <View
            style={{
              alignItems: 'center',
              paddingHorizontal: 30,
            }}>
            <View style={{marginTop: 200}}>
              {promoInProgress?.hash && (
                <View
                  style={{
                    position: 'absolute',
                    bottom: 76,
                    alignSelf: 'center',
                    borderWidth: 1,
                    borderRadius: 10,
                    padding: 12,
                    backgroundColor: 'rgba(255,255,255,.6)',
                  }}>
                  <Text style={{textAlign: 'center'}}>
                    {promoInProgress?.message}
                  </Text>
                </View>
              )}
              <Image
                source={require('../../../assets/img/sojubar-logo.png')}
                style={styles.logokucuk}
              />
            </View>

            <View style={styles.kutu_full}>
              <TextInput
                style={[
                  styles.InputStyle,
                  {borderWidth: 0.5, borderColor: 'silver'},
                ]}
                placeholder={t('login.email')}
                autoCapitalize="none"
                autoCorrect={false}
                value={email}
                keyboardType="email-address"
                placeholderTextColor="#000"
                onChangeText={value => setEmail(value)}
              />
            </View>

            {/* <View style={[styles.labelline, {alignSelf: 'flex-start'}]}>
              <Text style={[styles.labelStyle, styles.ozelfont_medium]}>
                {l.password}
              </Text>
            </View> */}

            <View style={styles.kutu_full}>
              <TextInput
                style={[
                  styles.InputStyle,
                  {borderWidth: 0.5, borderColor: 'silver'},
                ]}
                placeholder={t('login.password')}
                autoCapitalize="none"
                autoCorrect={false}
                secureTextEntry={true}
                value={password}
                placeholderTextColor="#000"
                onChangeText={value => setPassword(value)}
              />
            </View>

            {state.errormsg ? (
              <View style={{width: '100%', margin: 10, alignItems: 'center'}}>
                <Image
                  source={require('../../../assets/img/icon_uyari.png')}
                  style={{
                    height: 32,
                    width: 32,
                    marginBottom: 10,
                    resizeMode: 'contain',
                  }}
                />
                <Text style={{color: 'red'}}>{state.errormsg}</Text>
              </View>
            ) : null}
            <View style={{marginHorizontal: 10, width: '100%'}}>
              <TouchableOpacity onPress={() => _login()}>
                <View style={sc.PrimaryButton}>
                  <Text style={[sc.PrimaryButton_text, styles.ozelfont_medium]}>
                    {t('button.login')}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
            <View
              style={{
                margin: 15,
                width: '100%',
                alignItems: 'center',
              }}>
              <TouchableOpacity onPress={() => navigate('Forgot')}>
                <Text
                  style={[
                    styles.bold,
                    styles.text16,
                    styles.ozelfont_medium,
                    styles.black,
                  ]}>
                  {l.forgotText1}
                </Text>
              </TouchableOpacity>
            </View>
            <View
              style={{
                margin: 10,
                width: '100%',
                alignItems: 'center',
              }}>
              <TouchableOpacity onPress={() => navigate('Register')}>
                <Text
                  style={[
                    styles.bold,
                    styles.text16,
                    styles.ozelfont_medium,
                    styles.black,
                  ]}>
                  {l.registerText1}

                  <Text style={styles.red}> {l.registerText2}</Text>
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </KeyboardAwareScrollView>
    </ImageBackground>
  );
};

export default LoginScreen;
