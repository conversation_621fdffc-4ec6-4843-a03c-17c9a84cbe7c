import React, {useState} from 'react';
import {
  View,
  Image,
  Text,
  TextInput,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ImageBackground,
  ScrollView,
} from 'react-native';
import {CheckBox} from 'react-native-elements';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';
import Loading from '../../components/Loading';

import {navigate} from '../../navigationRef';
import {PasswordChecklist} from './components';

const RegisterScreen = ({navigation, route}) => {
  const {state, register} = React.useContext(UserContext);
  const {t, locale} = React.useContext(LocalizationContext);

  const [firstname, setFirstname] = useState();
  const [lastname, setLastname] = useState();
  const [email, setEmail] = useState();
  const [phone, setPhone] = useState();
  const [sms, setSms] = useState();
  const [emsg, setEmsg] = useState();
  const [location, setLocation] = useState(1);
  const [agreement, setAgreement] = useState();
  const [password, setPassword] = useState();
  const [repassword, setRepassword] = useState();
  const [refCode, setRefCode] = useState(
    route?.params?.refCode ? route?.params?.refCode : '',
  );
  const l = t('login');
  const [isLoading, setLoading] = useState(false);

  const _register = () => {
    setLoading(true);
    const data = {
      firstname,
      lastname,
      email,
      phone,
      sms,
      emsg,
      agreement,
      refCode,
      password,
      repassword,
      location,
      action: 'new',
      locale,
    };

    register({
      data: data,
      alert: t('alert'),
    }).then(resp => {
      console.log('resp', resp);
      setLoading(false);
    });
  };

  React.useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      navigation.setOptions({
        title: l.registerTitle,
        headerTruncatedBackTitle: '',
        headerBackTitle: '',
      });

      if (state.isLogin) {
        navigate('Home');
      }
    });
    return unsubscribe;
  }, [navigation]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <ImageBackground
      source={require('../../../assets/img/back_login.jpg')}
      resizeMode="cover"
      style={{flex: 1, marginTop: 0}}>
      <KeyboardAwareScrollView>
        <SafeAreaView>
          <ScrollView>
            <View
              style={{
                paddingHorizontal: 25,
              }}>
              <View style={{marginTop: 20, alignItems: 'center'}}>
                <Image
                  source={require('../../../assets/img/sojubar-logo.png')}
                  style={styles.logokucuk}
                />
              </View>

              {/* <View style={styles.labelline}>
                <Text style={styles.labelStyle}>{l.firstname}</Text>
              </View>
               */}

              <View style={styles.kutu_full}>
                <TextInput
                  style={styles.InputStyle}
                  placeholder={l.firstname}
                  autoCorrect={false}
                  value={firstname}
                  autoFocus
                  onChangeText={value => setFirstname(value)}
                  placeholderTextColor="#000"
                />
              </View>

              <View style={styles.kutu_full}>
                <TextInput
                  style={styles.InputStyle}
                  placeholder={l.lastname}
                  autoCorrect={false}
                  value={lastname}
                  onChangeText={value => setLastname(value)}
                  placeholderTextColor="#000"
                />
              </View>

              <View style={styles.kutu_full}>
                <TextInput
                  style={styles.InputStyle}
                  placeholder={l.email}
                  autoCapitalize="none"
                  autoCorrect={false}
                  value={email}
                  keyboardType="email-address"
                  onChangeText={value => setEmail(value)}
                  placeholderTextColor="#000"
                />
              </View>

              <View style={styles.kutu_full}>
                <TextInput
                  style={styles.InputStyle}
                  placeholder={l.phone}
                  autoCapitalize="none"
                  autoCorrect={false}
                  value={phone}
                  maxLength={12}
                  onChangeText={value => setPhone(value)}
                  placeholderTextColor="#000"
                />
              </View>

              <View style={styles.kutu_full}>
                <TextInput
                  style={styles.InputStyle}
                  placeholder={l.password}
                  autoCapitalize="none"
                  autoCorrect={false}
                  secureTextEntry={true}
                  value={password}
                  onChangeText={value => setPassword(value)}
                  placeholderTextColor="#000"
                  blurOnSubmit={false}
                />
              </View>

              <View style={styles.kutu_full}>
                <TextInput
                  style={styles.InputStyle}
                  placeholder={l.repassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                  secureTextEntry={true}
                  value={repassword}
                  onChangeText={value => setRepassword(value)}
                  placeholderTextColor="#000"
                  blurOnSubmit={false}
                />
              </View>

              <View style={styles.kutu_full}>
                <TextInput
                  style={styles.InputStyle}
                  placeholder={l.refcode}
                  autoCapitalize="none"
                  autoCorrect={false}
                  value={refCode}
                  maxLength={12}
                  onChangeText={value => setRefCode(value)}
                  placeholderTextColor="#000"
                />
              </View>

              <View style={styles.kutu_full}>
                <PasswordChecklist />
              </View>

              {/* Lokasyon */}
              <View style={styles.kutu_full}>
                <Text
                  style={[
                    styles.sectionTitle,
                    styles.ozelfont_medium,
                    styles.black,
                  ]}>
                  {l.location}
                </Text>
              </View>
              <View
                style={{
                  width: '100%',
                }}>
                <CheckBox
                  left
                  title={l.Turkiye}
                  checkedIcon="check"
                  uncheckedIcon="circle-o"
                  checkedColor="green"
                  textStyle={{fontFamily: 'IBMPlexSans-Medium'}}
                  checked={location === 2 ? '' : location}
                  onPress={() => setLocation(1)}
                />
              </View>
              <View
                style={{
                  width: '100%',
                }}>
                <CheckBox
                  left
                  title={l.KKTC}
                  checkedIcon="check"
                  uncheckedIcon="circle-o"
                  checkedColor="green"
                  textStyle={{fontFamily: 'IBMPlexSans-Medium'}}
                  checked={location === 1 ? '' : location}
                  onPress={() => setLocation(2)}
                />
              </View>

              {/* Lokasyon */}

              <View style={styles.kutu_full}>
                <Text
                  style={[
                    styles.sectionTitle,
                    styles.ozelfont_medium,
                    styles.black,
                  ]}>
                  {l.campaign}
                </Text>
              </View>
              <View
                style={{
                  width: '100%',
                }}>
                <CheckBox
                  left
                  title={l.emsgAccept}
                  checkedIcon="check"
                  uncheckedIcon="circle-o"
                  checkedColor="green"
                  textStyle={{fontFamily: 'IBMPlexSans-Medium'}}
                  checked={emsg}
                  onPress={emsg ? () => setEmsg(false) : () => setEmsg(true)}
                />
              </View>
              <View
                style={{
                  width: '100%',
                }}>
                <CheckBox
                  left
                  title={l.smsAccept}
                  checkedIcon="check"
                  uncheckedIcon="circle-o"
                  checkedColor="green"
                  textStyle={{fontFamily: 'IBMPlexSans-Medium'}}
                  checked={sms}
                  onPress={sms ? () => setSms(false) : () => setSms(true)}
                />
              </View>

              <View
                style={{
                  marginLeft: 0,
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                }}>
                <CheckBox
                  left
                  title=""
                  checkedIcon="check"
                  uncheckedIcon="circle-o"
                  checkedColor="red"
                  checked={agreement}
                  onPress={
                    agreement
                      ? () => setAgreement(false)
                      : () => setAgreement(true)
                  }
                />
                <TouchableOpacity
                  onPress={() => {
                    navigate('Agreement');
                  }}>
                  <Text
                    style={{fontFamily: 'IBMPlexSans-Medium', color: 'black'}}>
                    {l.agreementText}
                  </Text>
                </TouchableOpacity>
              </View>
              {state.errormsg ? (
                <View style={[styles.alertbox, {alignSelf: 'center'}]}>
                  <Image
                    source={require('../../../assets/img/icon_uyari.png')}
                    style={{
                      height: 32,
                      width: 32,
                      marginBottom: 10,
                      resizeMode: 'contain',
                    }}
                  />

                  <Text style={{color: 'red', alignItems: 'center'}}>
                    {state.errormsg}
                  </Text>
                </View>
              ) : null}

              <View style={{marginHorizontal: 0, marginVertical: 20}}>
                <TouchableOpacity
                  style={sc.PrimaryButton}
                  onPress={() => _register()}>
                  <Text style={[sc.PrimaryButton_text, styles.ozelfont_medium]}>
                    {t('button.register')}
                  </Text>
                </TouchableOpacity>
              </View>

              <View
                style={{
                  marginTop: 30,
                  marginBottom: 50,
                  width: '100%',
                  alignItems: 'center',
                }}>
                <TouchableOpacity onPress={() => navigate('Login')}>
                  <Text
                    style={[
                      styles.bold,
                      styles.text16,
                      styles.ozelfont_medium,
                      styles.black,
                    ]}>
                    {l.loginText1}

                    <Text style={styles.red}> {l.forgotText2}</Text>
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </KeyboardAwareScrollView>
    </ImageBackground>
  );
};

const pageStyle = StyleSheet.create({
  pagecontainer: {backgroundColor: '#F3EFE5', flex: 1},
});

export default RegisterScreen;
