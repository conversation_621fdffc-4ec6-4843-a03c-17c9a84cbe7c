import React, {useContext, useEffect} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import LocalizationContext from '../../../context/LocalizationContext';
import {passwordRequirements} from '../../../utils';

const PasswordChecklist = () => {
  const {t} = useContext(LocalizationContext);
  const requirementTitles = t('alert.passwordRequirement');
  const requirements = passwordRequirements(requirementTitles);

  return (
    <View style={styles.container}>
      <Text style={styles.checklistTitle}>
        {t('alert.passwordChecklist.title')}
      </Text>
      <View style={styles.requirementsContainer}>
        {requirements?.map(requirement => (
          <PasswordRequirement
            key={`requirement-${requirement.key}`}
            requirement={requirement}
          />
        ))}
      </View>
    </View>
  );
};
export default PasswordChecklist;

const PasswordRequirement = ({requirement}) => {
  return (
    <View
      key={`requirement-${requirement.key}`}
      style={styles.requirementContainer}>
      <Text
        key={`requirement-${requirement.key}-label`}
        style={styles.requirement}>
        {requirement.label}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {gap: 4, width: '100%'},
  checklistTitle: {fontWeight: '600'},
  requirementsContainer: {gap: 2},
  requirementContainer: {flexDirection: 'row', gap: 4},
  requirement: {color: 'black'},
});
