import * as React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
  Alert,
  Modal,
  Animated,
} from 'react-native';
import {Icon} from 'react-native-elements';
import AsyncStorage from '@react-native-async-storage/async-storage';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import {navigate} from '../../navigationRef';
import {ScrollView} from 'react-native-gesture-handler';

import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';
import {useIsFocused} from '@react-navigation/native';
import mobileApi from '../../api/mobileApi';
import VersionNumber from 'react-native-version-number';
import analytics from '@react-native-firebase/analytics';
import Loading from '../../components/Loading';

const DeliveryScreen = () => {
  const isFocused = useIsFocused();
  const {state, login, setBranch} = React.useContext(UserContext);
  const {t, locale} = React.useContext(LocalizationContext);
  const [address, setAddress] = React.useState([]);
  const [results, setResults] = React.useState(null);
  const [isLoading, setIsLoading] = React.useState(true);

  const l = t('payment');

  const getResults = async () => {
    const response = await mobileApi.get(
      `/${locale}/getRestaurants?t=${new Date().getTime()}`,
    );
    setResults(response.data);
  };

  const getBranchIndex = (arr, id) => {
    if (arr?.err) {
      return -1;
    } else {
      return arr?.findIndex(obj => parseInt(obj?.id) === parseInt(id));
    }
  };

  const checkBranchSingle = async id => {
    await mobileApi
      .get(`/${locale}/getRestaurants?id=${id}&t=${new Date().getTime()}`)
      .then(resp => {
        if (resp.data.orderAlert === '1') {
          return 1;
        } else if (resp.data.status === 'N' || resp.data.orderStatus === 0) {
          return 2;
        } else {
          return 3;
        }
      });
  };

  const checkBranch = id => {
    const index = getBranchIndex(results, id);
    let s = 0;
    if (index > -1) {
      const orderStatus = results[index].orderStatus;
      const status = results[index].status;
      const orderAlert = results[index].orderAlert;
      const orderAlertMsg = results[index].orderAlertMsg;
      const outwork = results[index].outwork;
      const orderHour = results[index].orderHour;

      if (outwork === 1) {
        return (
          <Text
            style={[styles.SubeAdres, styles.ozelfont_medium, {color: 'red'}]}>
            {l.outofservice1}
            {orderHour}
            {l.outofservice2}
          </Text>
        );
      } else if (orderAlert === '1') {
        return (
          <Text
            style={[styles.SubeAdres, styles.ozelfont_medium, {color: 'red'}]}>
            {orderAlertMsg}
          </Text>
        );
      } else if (status === 'Y' && orderStatus === 1) {
        return null;
      } else {
        return (
          <Text
            style={[styles.SubeAdres, styles.ozelfont_medium, {color: 'red'}]}>
            {l.gecici_pakete_kapali_txt}
          </Text>
        );
      }
    } else {
      return (
        <Text
          style={[styles.SubeAdres, styles.ozelfont_medium, {color: 'red'}]}>
          {l.gecici_pakete_kapali_txt}
        </Text>
      );
    }
  };
  const _readVal = async () => {
    await AsyncStorage.multiGet([
      '@email',
      '@password',
      '@branchId',
      '@branchName',
      '@addressid',
    ]).then(response => {
      if (response[2][1] && response[4][1]) {
        navigate('MenuList', {
          _refresh: _refresh,
          id: response[2][1],
          name: response[3][1],
          menu: 0,
        });
      } else {
        const data = {
          email: response[0][1],
          password: response[1][1],
          locale,
          version: VersionNumber.appVersion,
        };
        login({data: data, alert: t('alert')})
          .then(resp => {
            if (resp.err === 1) {
              navigate('LoginStack', {screen: 'Login'});
            } else {
              setIsLoading(true);
              AsyncStorage.getItem('@userdata')
                .then(user => {
                  const r = JSON.parse(user);
                  setAddress(r.address);
                })
                .catch(err => {
                  //console.log(err);
                  navigate('LoginStack');
                })
                .finally(() => {
                  setIsLoading(false);
                });
            }
          })
          .catch(err => {
            //console.log(err);
            navigate('LoginStack');
          });
      }
    });
  };

  const _refresh = () => {
    _readVal();
  };

  const _setBranch = async (
    id,
    name,
    addressid,
    regionLimit,
    deliveryPrice,
    serviceTime,
    district,
    city,
  ) => {
    const service = '1';
    await setBranch(
      id,
      name,
      service,
      addressid,
      regionLimit,
      deliveryPrice,
      serviceTime,
    )
      .then(() => {
        const fdatas = {
          restaurant_id: id,
          city: city,
          district: district,
        };
        analytics().logEvent('select_restaurant', fdatas);

        navigate('MenuList', {id, name, _refresh: _refresh, menu: 0});
      })
      .catch(err => {
        console.log(err);
      });
  };

  React.useEffect(() => {
    _readVal();
    getResults();
  }, [isFocused]);

  return (
    <View
      // source={require('../../../assets/img/back_language.jpg')}
      // resizeMode="cover"
      style={{flex: 1}}>
      {isLoading && <Loading />}

      <View style={pageStyle.pagecontainer}>
        <ScrollView style={{flex: 1}}>

        <View style={[styles.alertbox, {marginVertical: 10, backgroundColor: '#78bae750'}]}>
           <Text
              style={[
                styles.text16,
                styles.tcenter,
                styles.black,
                styles.ls1,
                styles.ozelfont_medium,
              ]}>
              {l.delivery_txt1}
            </Text>
          </View>


          <TouchableOpacity
            onPress={() =>
              navigate('LoginStack', {
                screen: 'Address',
                params: {mode: 'new', id: ''},
              })
            }
            style={sc.PrimaryButton}>
            <Text style={sc.PrimaryButton_text}>
              {t('login.addressAddTitle')}
            </Text>
          </TouchableOpacity>

          <View style={{gap: 12, marginTop: 30}}>
            {address ? (
              address.map((item, i) => {
                let branchs = [];
                if (item.branchs) {
                  branchs = item.branchs;
                }

                return (
                  <View key={i}>
                    <View
                      style={{
                        width: '100%',
                        backgroundColor: '#f2f2f2',
                        paddingHorizontal: 15,
                        paddingVertical: 7,
                        marginBottom: 0,
                        borderTopRightRadius: 15,
                        borderTopLeftRadius: 15,
                      }}>
                      <Text
                        style={[
                          styles.text16,
                          styles.ozelfont_bold,
                          styles.black,
                        ]}>
                        {item.label}
                      </Text>
                      <Text
                        style={[
                          styles.text18,
                          styles.ozelfont_regular,
                          styles.gray,
                        ]}>
                        {item.districtName} - {item.cityName}
                      </Text>
                    </View>
                    {branchs.map((branch, j) => {
                      //console.log(branchs);
                      return (
                        <View
                          style={{
                            width: '100%',
                            //   backgroundColor: '#f8ecde',
                            //  borderBottomWidth: 1,
                            //  borderBottomColor: '#D8D8D8',
                          }}
                          key={j}>
                          <TouchableOpacity
                            style={styles.listelemedelivery}
                            onPress={() => {
                              if (branch.bdurum === 1) {
                                mobileApi
                                  .get(
                                    `/${locale}/getRestaurants?id=${
                                      branch.branchId
                                    }&t=${new Date().getTime()}`,
                                  )
                                  .then(resp => {
                                    if (resp) {
                                      if (resp.data[0].outwork === 1) {
                                        Alert.alert(
                                          t('payment').warning,
                                          `${l.outofservice1}${resp.data[0].orderHour}${l.outofservice2}`,
                                          [
                                            {
                                              text: t('payment').ok,
                                            },
                                          ],
                                        );
                                      } else if (
                                        resp.data[0].orderStatus === 0
                                      ) {
                                        Alert.alert(
                                          t('payment').warning,
                                          t('payment').gecici_pakete_kapali_txt,
                                          [
                                            {
                                              text: t('payment').ok,
                                            },
                                          ],
                                        );
                                      } else {
                                        _setBranch(
                                          branch.branchId,
                                          branch.branchName,
                                          item.id,
                                          branch.price.toString(),
                                          branch.deliveryPrice.toString(),
                                          branch.service,
                                          item.districtName,
                                          item.cityName,
                                        );
                                      }
                                    } else {
                                      Alert.alert(
                                        t('payment').warning,
                                        t('payment').gecici_pakete_kapali_txt,
                                        [
                                          {
                                            text: t('payment').ok,
                                          },
                                        ],
                                      );
                                    }
                                  });
                              } else {
                                Alert.alert(
                                  t('payment').warning,
                                  t('closedistrict'),
                                  [
                                    {
                                      text: t('payment').ok,
                                    },
                                  ],
                                );
                              }
                            }}>
                            <View style={styles.kutu_full}>
                              <View style={styles.listeleme_orta}>
                                <Text
                                  style={[
                                    styles.SubeAd,
                                    styles.kurumsalfont_regular,
                                    styles.red,
                                  ]}>
                                  {branch.branchName}
                                </Text>
                                <Text
                                  style={[
                                    styles.SubeAdres,
                                    styles.ozelfont_medium,
                                    styles.gray,
                                  ]}>
                                  {branch.service} {l.dk} -{' '}
                                  {t('payment.minamountshort')} : {branch.price}{' '}
                                  {t('currency')}
                                </Text>

                                {checkBranch(branch.branchId)}
                              </View>
                              <View style={styles.listeleme_sag}>
                                <Icon
                                  name="chevron-right"
                                  color="gray"
                                  size={14}
                                  type="font-awesome-5"
                                />
                              </View>
                            </View>
                          </TouchableOpacity>
                        </View>
                      );
                    })}
                  </View>
                );
              })
            ) : (
              <View style={{marginVertical: 20}}>
                <Text style={{textAlign: 'center'}}>{l.adres_txt}</Text>
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

const pageStyle = StyleSheet.create({
  pagecontainer: {paddingHorizontal: 15, flex: 1},
});

export default DeliveryScreen;
