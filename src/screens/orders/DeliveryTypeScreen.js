import {useContext, useEffect, useState} from 'react';
import {View, Text, StyleSheet, LogBox, Pressable} from 'react-native';

import DeliveryScreen from './DeliveryScreen';
import PickupScreen from './PickupScreen';
import {Context as UserContext} from '../../context/UserContext';
import {useIsFocused} from '@react-navigation/native';
import {navigate} from '../../navigationRef';
import LocalizationContext from '../../context/LocalizationContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Loading from '../../components/Loading';
import VersionNumber from 'react-native-version-number';
import {
  SegmentedTabWrapper,
  SegmentedTab,
} from '../../components/SegmentedTabs';
LogBox.ignoreAllLogs();
const DeliveryTypeScreen = ({navigation}) => {
  const isFocused = useIsFocused();
  const [isLoading, setLoading] = useState(true);

  const {login} = useContext(UserContext);
  const {t, locale} = useContext(LocalizationContext);

  const _checkLogin = async () => {
    await AsyncStorage.multiGet(['@email', '@password']).then(response => {
      setLoading(false);
      const data = {
        email: response[0][1],
        password: response[1][1],
        locale,
        fast: 1,
        version: VersionNumber.appVersion,
      };

      login({data: data, alert: t('alert')})
        .then(resp => {
          if (resp.err === 1 || resp.activation === '0') {
            navigate('LoginStack');
          }
        })
        .catch(err => {
          //console.log(err);
          navigate('LoginStack');
        });
    });
  };

  useEffect(() => {
    navigation.setOptions({
      title: t('payment').servicetype,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
      headerLeft: () => null,
    });
    _checkLogin();
  }, [isFocused]);
  if (isLoading) {
    return <Loading />;
  }

  return (
    <View style={{flex: 1, backgroundColor: '#fff'}}>
      <SegmentedTabWrapper>
        <SegmentedTab title={t('deliveryService1')}>
          <DeliveryScreen />
        </SegmentedTab>
        <SegmentedTab title={t('deliveryService2')}>
          <PickupScreen />
        </SegmentedTab>
      </SegmentedTabWrapper>
    </View>
  );
};

export default DeliveryTypeScreen;
