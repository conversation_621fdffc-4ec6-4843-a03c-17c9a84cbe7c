import React, { useContext, useEffect } from 'react';
import { StyleSheet } from 'react-native';

import { navigate } from '../../navigationRef';

import { Context as UserContext } from '../../context/UserContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import mobileApi from '../../api/mobileApi';
import LocalizationContext from '../../context/LocalizationContext';

const OrderScreen = ({ navigation }) => {
  const { setBranch } = useContext(UserContext);
  const { t, locale } = React.useContext(LocalizationContext);

  const _readVal = async () => {
    await AsyncStorage.multiGet(['@branchId', '@branchName', '@addressid', '@token', '@memberid', '@isLogin', '@userdata', '@branchTime'])
      .then(r => {
        //console.log(r);
        if (!r[3][1]) {
          // console.log("login yok");
          navigate('LoginStack', { screen: 'Login' });
          return false;
        }

        if (!r[0][1]) {
          //console.log("şube seçili değil");
          navigate('DeliveryType');
          return false;
        }

        if (!r[6][1]) {
          // user dataya erişilemedi.
          navigate('LoginStack', { screen: 'Login' });
          return false;
        }

        const req = {
          memberId: r[4][1],
          subeId: r[0][1],
          addressid: r[2][1],
          token: r[3][1],
        };

        mobileApi.post(`/tr/checkAddress`, req).then(res => {
          // bölge kapalı mı kontrol et
          if (res?.data?.districtstatus === 0) {
            setBranch('', '').then(() => {
              navigate('DeliveryType');
            });
          }
        });

        mobileApi.get(`/${locale}/getRestaurants?id=${r[0][1]}&t=${new Date().getTime()}`).then(resp => {
          if (resp) {
            if (resp.data[0].orderStatus === 0 || resp.data[0].status === 'N') {
              setBranch('', '').then(() => {
                navigate('DeliveryType');
              });
            } else {
              const now = new Date();
              outTime = new Date(r[7][1]);
              diffTime = Math.floor((now.getTime() - outTime.getTime()) / (1000 * 60));
              if (diffTime > 120) {
                setBranch('', '').then(() => {
                  navigate('DeliveryType');
                });
              } else {
                navigate('MenuList', { menu: 0, g: 0 });
              }
            }
          } else {
            setBranch('', '').then(() => {
              navigate('DeliveryType');
            });
          }
        });
      })
      .catch(err => {
        // _goBack();
      });
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      _readVal();
    });
    return unsubscribe;
  }, [navigation]);

  return null;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 20,
  },
});

export default OrderScreen;
