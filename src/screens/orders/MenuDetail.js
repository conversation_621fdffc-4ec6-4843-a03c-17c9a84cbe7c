import React, {useState, useEffect, useContext} from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Image,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Pressable,
} from 'react-native';
import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import mobileApi from '../../api/mobileApi';
import {Icon} from 'react-native-elements';
import LocalizationContext from '../../context/LocalizationContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import allergen from '../../lang/allergen';
import {Context as UserContext} from '../../context/UserContext';
import analytics from '@react-native-firebase/analytics';

const MenuDetail = ({navigation, route}) => {
  const {id, item, handleBadge, basketCount, category} = route.params;
  const [count, setCount] = useState(basketCount);
  const [total, setTotal] = useState(0);
  const [optionsCount, setOptionsCount] = useState(0);
  const [optionTotal, setOptionTotal] = useState(0);
  const {state, saveBasket, getBadge} = useContext(UserContext);
  const {t, locale} = useContext(LocalizationContext);
  const [loginToken, setLoginToken] = useState();
  const [loginMemberId, setLoginMemberId] = useState();
  const [favId, setFavId] = useState();
  const [onFavId, setOnFavId] = useState();
  const [modalVisible, setModalVisible] = useState(false);
  const [loadingModal, setLoadingModal] = useState(false);

  const b = t('button');

  const [pagestate, setPageState] = useState([]);

  const _addFavorite = async (token, memberId) => {
    const req = {
      token,
      memberId,
      favName: 'Favorilerim',
    };
    await mobileApi.post(`/${locale}/addFavorite`, req).then(res => {
      setFavId(res.data.favId);
    });
  };

  const _getFavorites = async (token, memberId) => {
    const req = {token, memberId};
    await mobileApi.post(`/${locale}/getFavorites`, req).then(res => {
      // console.log(res.data);
      if (res.data) {
        setFavId(res.data.menu[0].menuid);

        const favIndex =
          typeof res.data.menu[0].products !== 'undefined'
            ? getIndex(res.data.menu[0].products, id)
            : -1;
        favIndex > -1 ? setOnFavId(1) : setOnFavId(0);
      } else {
        _addFavorite(token, memberId);
      }
    });
  };

  function getIndex(arr, id) {
    return arr.findIndex(obj => obj.productId === id);
  }

  const getAlIndex = (arr, id) => {
    return arr.findIndex(obj => obj.id === id.toString());
  };

  const _addFavoriteProduct = async id => {
    if (loginMemberId && loginToken) {
      const req = {
        memberId: loginMemberId,
        favId: favId,
        productId: id,
        token: loginToken,
      };
      await mobileApi.post(`/${locale}/addFavoriteProduct`, req).then(res => {
        if (res.data.err === 1) {
          Alert.alert(t('payment').warning, res.data.msg, [
            {
              text: t('payment').ok,
            },
          ]);
        } else {
          setOnFavId(1);
          Alert.alert(t('login').congratulations, res.data.msg, [
            {
              text: t('payment').ok,
            },
          ]);
          const fdatas = {
            items: [
              {
                item_id: item.productId,
                item_name: item.name,
                item_category: category,
                item_brand: 'Sushico',
                price: parseFloat(item.price),
                item_variant: item.desc,
              },
            ],
            currency: t('currency_a'),
            value: parseFloat(item.price),
          };
          analytics().logEvent('add_to_wishlist', fdatas);
        }
      });
    } else {
      Alert.alert(t('login').congratulations, t('faverror1'), [
        {
          text: t('payment').ok,
        },
      ]);
    }
  };

  const _delFavoriteProduct = async () => {
    const req = {
      memberId: loginMemberId,
      favId: favId,
      productId: id,
      token: loginToken,
    };
    await mobileApi.post(`/${locale}/delFavoriteProduct`, req).then(res => {
      if (res.data.err === 1) {
        Alert.alert(t('payment').warning, res.data.msg, [
          {
            text: t('payment').ok,
          },
        ]);
      } else {
        setOnFavId(0);
        Alert.alert(t('login').congratulations, res.data.msg, [
          {
            text: t('payment').ok,
          },
        ]);
      }
    });
  };

  const _saveBasket = async action => {
    if (Object.keys(pagestate).length < optionsCount) {
      Alert.alert(t('payment').warning, t('subProductSelect'), [
        {
          text: t('payment').ok,
        },
      ]);
      return;
    }
    let newMenu = [];

    Object.keys(pagestate).map(item => {
      newMenu.push({
        itemcode: pagestate[item].itemcode,
        itemname: pagestate[item].itemname,
        itemprice: pagestate[item].itemprice,
      });
    });

    const data = {
      title: item.name,
      price: item.price,
      menu: newMenu,
      sos: item.sos ? item.sos : '',
      category,
      desc: item.desc,
      id,
      count,
      optionTotal,
      optionsCount,
      promoId: '',
      promoCode: '',
      action,
      reset: 1,
    };

    await saveBasket({data: data, alert: t('alert')}).then(response => {
      // console.log("response ", response);
      setLoadingModal(!loadingModal);
      _getBadge().then(() => {
        _goBack(id, count);
      });
      // handleBadge(state.totalCount);
      //navigation.goBack();
    });
  };

  const _goBack = async (id, count) => {
    navigation.goBack();
    route.params._updateCount(id, count);
  };
  const _getBadge = async () => {
    await AsyncStorage.getItem('@basket')
      .then(response => {
        const r = response ? JSON.parse(response) : [];
        let totalAmount = 0;
        if (r.length > 0) {
          r.map(i => {
            totalAmount +=
              i.count * (parseFloat(i.optionTotal) + parseFloat(i.price));
          });
        } else {
          totalAmount = 0;
        }
        handleBadge(r.length);
      })
      .catch(e => {
        handleBadge(0);
      });
  };

  const _basketOptions = async (
    optionid,
    optionitemid,
    optionitemname,
    optionitemprice,
  ) => {
    setPageState({
      ...pagestate,
      [`${optionid}`]: {
        itemcode: optionitemid,
        itemname: optionitemname,
        itemprice: optionitemprice,
      },
    });
  };

  const _checkState = (optionid, optionitemid) => {
    if (pagestate[`${optionid}`]) {
      return pagestate[`${optionid}`].itemcode === optionitemid ? true : false;
    }
  };

  const _setTotal = (count, price) => {
    let optionPriceTotal = 0;
    Object.keys(pagestate).map(item => {
      optionPriceTotal =
        parseFloat(optionPriceTotal) + parseFloat(pagestate[item].itemprice);
    });
    setOptionTotal(parseFloat(optionPriceTotal));
  };

  const _checkBasket = async id => {
    await AsyncStorage.multiGet(['@token', '@memberid'])
      .then(response => {
        setLoginToken(response[0][1]);
        setLoginMemberId(response[1][1]);
        _getFavorites(response[0][1], response[1][1]);
      })
      .catch(e => {
        console.log(e);
      });
  };
  useEffect(() => {
    getBadge();
    handleBadge(state.totalCount);
  }, [state.totalCount]);

  useEffect(() => {
    _setTotal(count, total);
  }, [pagestate, count]);

  useEffect(() => {
    navigation.setOptions({
      title: route?.params?.menu === 1 ? t('menuView') : t('menu').orders,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    setTotal(item.price);
    setCount(basketCount);
    _setTotal(basketCount, item.price);
    item.option ? setOptionsCount(item.option.length) : setOptionsCount(0);
    _checkBasket(id);
    //console.log(item.alerjen[1]);
    //console.log(JSON.stringify(item));

    const firedatas = {
      items: [
        {
          item_id: item.productId,
          item_name: item.name,
          item_category: category,
          item_brand: 'Sojubar',
          price: parseFloat(item.price),
        },
      ],
      item_list_name: 'Ürünler',
      item_list_id: 1,
      restaurant_menu: 'True',
      currency: 'TRY',
      value: parseFloat(item.price),
    };
    analytics().logEvent('view_item', firedatas);
  }, [route]);

  if (!item) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {
          Alert.alert('Modal has been closed.');
          setModalVisible(!modalVisible);
        }}>
        <View style={pagestyles.centeredView}>
          <View style={pagestyles.modalView}>
            {allergen.map(i => {
              const index = getAlIndex(item.alerjen, i.id);
              if (index > -1) {
                return (
                  <View
                    key={i.id}
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignSelf: 'flex-start',
                      height: 30,
                    }}>
                    <Image source={i.require} style={{width: 20, height: 20}} />
                    <Text
                      style={[
                        styles.text16,
                        styles.black,
                        styles.ozelfont_regular,
                        {marginLeft: 10},
                      ]}>
                      {item.alerjen[index].desc}
                    </Text>
                  </View>
                );
              }
            })}
            <Pressable onPress={() => setModalVisible(!modalVisible)}>
              <View style={sc.PrimaryButton}>
                <Text style={sc.PrimaryButton_text}>{t('button.close')}</Text>
              </View>
            </Pressable>
          </View>
        </View>
      </Modal>

      <Modal
        animationType="fade"
        transparent={true}
        visible={loadingModal}
        onRequestClose={() => {
          setModalVisible(!loadingModal);
        }}>
        <View style={pagestyles.centeredView}>
          <View
            style={[
              pagestyles.modalView,
              styles.ozelfont_medium,
              styles.bgrenk3,
              {alignContent: 'center'},
            ]}>
            <Text
              style={[
                styles.text20,
                styles.ozelfont_medium,
                styles.white,
                {alignItems: 'center'},
              ]}>
              <Icon
                name="check"
                color="#ffffff"
                type="simple-line-icon"
                size={24}
                style={{marginRight: 10, marginTop: 2}}
              />{' '}
              {t('addbasket')}
            </Text>
          </View>
        </View>
      </Modal>

      <ScrollView>
        <View style={{zIndex: 2, position: 'absolute', top: 20, right: 20}}>
          <TouchableOpacity
            onPress={() =>
              !onFavId ? _addFavoriteProduct(id) : _delFavoriteProduct(id)
            }>
            <View>
              <Icon
                name="heart"
                color={onFavId ? 'red' : 'silver'}
                type="font-awesome"
                size={25}
                style={{paddingTop: 2}}
              />
            </View>
          </TouchableOpacity>
        </View>

        <View>
          <Image
            source={{
              uri: item.big,
            }}
            style={styles.DetayUrunFoto}
          />
        </View>

        <View style={styles.detay_kutu}>
          <View style={styles.detay_kutu_sol}>
            <Text style={sc.DetayUrunAdi}>{item.name}</Text>
          </View>
          <View style={{alignItems: 'flex-end'}}>
            <Text style={sc.DetayUrunAdi}>
              {item.price} {t('currency')}
            </Text>

            <View
              style={{alignItems: 'flex-end', flex: 1, flexDirection: 'row', marginVertical: 12}}>
              {allergen.map(i => {
                const index = getAlIndex(item.alerjen, i.id);
                if (index > -1) {
                  return (
                    <TouchableOpacity
                      key={i.id}
                      onPress={() => setModalVisible(true)}>
                      <Image
                        source={i.require}
                        style={{width: 24, height: 24}}
                      />
                    </TouchableOpacity>
                  );
                }
              })}
            </View>
          </View>
        </View>

        <View style={[styles.detay_kutu, {flex: 1, flexDirection: 'column'}]}>
          <View style={{alignItems: 'flex-start'}}>
            <Text style={sc.DetayUrunDesc}>{item.desc}</Text>
          </View>
          <View style={{alignItems: 'flex-start'}}>
            <View style={styles.detay_kutu_center}>
              <View style={{flex: 1, flexDirection: 'row', marginTop: 5}}>
                {item.yeni === '1' && (
                  <View style={styles.urunozl}>
                    <Text style={{flex: 1, color: '#393939', fontSize: 12}}>
                      {' '}
                      <Icon
                        name="star"
                        type="font-awesome-5"
                        iconStyle={{color: 'orange', fontSize: 13}}
                      />{' '}
                      {t('yeni')}{' '}
                    </Text>
                  </View>
                )}
                {item.hot === '1' && (
                  <View style={styles.urunozl}>
                    <Text style={{flex: 1, color: '#393939', fontSize: 12}}>
                      {' '}
                      <Icon
                        name="pepper-hot"
                        type="font-awesome-5"
                        iconStyle={{color: 'red', fontSize: 13}}
                      />{' '}
                      {t('acili')}{' '}
                    </Text>
                  </View>
                )}
                {item.vegi === '1' && (
                  <View style={styles.urunozl}>
                    <Text style={{color: '#393939', fontSize: 12}}>
                      {' '}
                      <Icon
                        name="seedling"
                        type="font-awesome-5"
                        iconStyle={{color: 'green', fontSize: 13}}
                      />{' '}
                      {t('vegeteryan')}{' '}
                    </Text>
                  </View>
                )}
                {item.pismis === '1' && (
                  <View style={styles.urunozl}>
                    <Text style={{color: '#393939', fontSize: 12}}>
                      {' '}
                      <Icon
                        name="fire"
                        type="font-awesome-5"
                        iconStyle={{color: 'purple', fontSize: 13}}
                      />{' '}
                      {t('pismis')}{' '}
                    </Text>
                  </View>
                )}

                {item.thai === '1' && (
                  <View style={styles.urunozl}>
                    <Text style={{color: '#393939', fontSize: 12}}>
                      {' '}
                      <Icon
                        name="tumblr"
                        type="font-awesome-5"
                        iconStyle={{color: 'purple', fontSize: 13}}
                      />{' '}
                      {t('thai')}{' '}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>
        </View>

        {item.option &&
          item.option.map(o => {
            return (
              <View key={o.optionid}>
                <View style={styles.detay_kutu}>
                  <Text style={styles.DetayAraBaslik}>{o.optionname}</Text>
                </View>

                {o.optionitem.map(oi => {
                  return (
                    <TouchableOpacity
                      key={oi.optionitemid}
                      onPress={() => {
                        _basketOptions(
                          o.optionid,
                          oi.optionitemid,
                          oi.optionitemname,
                          oi.optionitemprice,
                        );
                      }}>
                      <View style={styles.detay_kutu}>
                        <View style={(styles.detay_kutu_center, styles.left)}>
                          <Icon
                            name={
                              _checkState(o.optionid, oi.optionitemid)
                                ? 'check'
                                : 'circle'
                            }
                            color={
                              _checkState(o.optionid, oi.optionitemid)
                                ? 'red'
                                : '#bcbcbc'
                            }
                            type="font-awesome"
                            size={20}
                            style={{marginRight: 15}}
                          />
                        </View>
                        <View
                          style={[
                            styles.detay_kutu_center,
                            {
                              display: 'flex',
                              flexDirection: 'row',
                              justifyContent: 'space-between',
                            },
                          ]}>
                          <Text style={sc.Fiyat}>{oi.optionitemname}</Text>
                          {oi.optionitemprice > 0 && (
                            <Text>
                              +{oi.optionitemprice} {t('currency')}
                            </Text>
                          )}
                        </View>
                      </View>
                    </TouchableOpacity>
                  );
                })}
              </View>
            );
          })}
        {route?.params?.menu === 0 && (
          <View
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <View style={styles.basket_count_container2}>
              <TouchableOpacity
                onPress={() => {
                  if (count > 1) {
                    setCount(count - 1);
                  }
                }}>
                <View style={styles.basket_count_left2}>
                  <Text style={{color: '#fff', fontSize: 32}}>-</Text>
                </View>
              </TouchableOpacity>

              <View style={styles.basket_count_middle2}>
                <Text style={sc.Adet2}>{count}</Text>
              </View>

              <TouchableOpacity
                onPress={() => {
                  setCount(count + 1);
                }}>
                <View style={styles.basket_count_right2}>
                  <Text style={{color: '#fff', fontSize: 30}}>+</Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>

      {route?.params?.menu === 0 && (
        <View style={styles.fiyat_block}>
          <View style={styles.fiyat_text}>
            <Text style={[styles.text22, styles.ozelfont_bold, styles.black]}>
              {count * (parseFloat(total) + parseFloat(optionTotal))}
              <Text style={styles.text16}> {t('currency')}</Text>
            </Text>
          </View>

          <TouchableOpacity onPress={() => _saveBasket('add')}>
            <View style={styles.fiyat_btn}>
              <Text style={[sc.PrimaryButton_text, styles.ozelfont_bold,{color: '#78BAE7'}]}>
                {b.addbasket}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

const pagestyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#00000099',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    borderRadius: 10,
    padding: 10,
    elevation: 2,
  },
  buttonOpen: {
    backgroundColor: '#F194FF',
  },
  buttonClose: {
    backgroundColor: '#2196F3',
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontFamily: 'IBMPlexSans-Light',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
});

export default MenuDetail;
