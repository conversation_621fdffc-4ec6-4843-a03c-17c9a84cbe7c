import React, {
  useContext,
  useState,
  useRef,
  useEffect,
  useCallback,
} from 'react';
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  Image,
  Alert,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import {Icon} from 'react-native-elements';
import Loading from '../../components/Loading';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';
import AsyncStorage from '@react-native-async-storage/async-storage';

import SectionList from 'react-native-tabs-section-list';
import styless from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import mobileApi from '../../api/mobileApi';
import {navigate} from '../../navigationRef';

const MenuScreen = ({navigation, route}) => {
  const {handleBadge} = route.params;

  const listRef = useRef(null);

  const {state, setBranch, getBadge, saveBasket} = useContext(UserContext);
  const {t, locale} = useContext(LocalizationContext);

  const _keyExtractor = useCallback(item => item.productId, []);

  const [searchView, setSearchView] = useState(false);
  const [isLoading, setLoading] = useState(true);
  const [search, setSearch] = useState();
  const [pagestate, setPageState] = useState([]);
  const [products, setProducts] = useState(null);
  const [tempProducts, setTempProducts] = useState(null);
  const [branchName, setBranchName] = useState(null);

  const _readVal = async () => {
    await AsyncStorage.multiGet([
      '@branchId',
      '@branchName',
      '@addressid',
      '@token',
      '@basket',
    ])
      .then(r => {
        //console.log('menu', r);
        getResults(r[0][1], r[1][1]);
        const b = r[4][1] ? JSON.parse(r[4][1]) : [];
        _setBasket(b).then(() => {
          //console.log('page', pagestate);
        });
        //setBasket(b);
        //console.log(b);
        //const c = getAlIndex(b, '1478269192');
        //console.log('c', c, b[c].count);
      })
      .catch(err => {
        // _goBack();
      });
  };

  const _setBasket = async data => {
    let basket = {};
    data.map((item, i) => {
      basket[item.id] = {count: item.count};
    });
    setPageState(basket);
  };

  const _decBasket = async req => {
    let count = pagestate[`${req.item.productId}`]?.count
      ? pagestate[`${req.item.productId}`]?.count
      : 0;
    await AsyncStorage.multiGet(['@basket'])
      .then(r => {
        const b = r[0][1] ? JSON.parse(r[0][1]) : [];
        const inx = getAlIndex(b, req.item.productId);
        count--;
        // const data = {
        //   title: req.item.name,
        //   price: req.item.price,
        //   id: req.item.productId,
        //   count,
        //   action: req.action,
        //   inx,
        // };
        const data = {
          title: req.item.name,
          price: req.item.price,
          desc: req.item.desc,
          category: req.category,
          menu: [],
          sos: req.item.sos ? req.item.sos : '',
          id: req.item.productId,
          count,
          optionTotal: 0,
          optionsCount: 0,
          promoId: '',
          promoCode: '',
          action: req.action,
          inx,
        };

        setPageState({
          ...pagestate,
          [`${req.item.productId}`]: {
            count,
          },
        });
        saveBasket({data: data, alert: t('alert')}).then(() => {
          _getBadge();
        });
      })
      .catch(err => {
        // _goBack();
      });
  };

  const _addBasket = async req => {
    let count = pagestate[`${req.item.productId}`]?.count
      ? pagestate[`${req.item.productId}`]?.count
      : 0;
    count++;
    setPageState({
      ...pagestate,
      [`${req.item.productId}`]: {
        count,
      },
    });

    const data = {
      title: req.item.name,
      price: req.item.price,
      desc: req.item.desc,
      category: req.category,
      menu: [],
      sos: req.item.sos ? req.item.sos : '',
      id: req.item.productId,
      count,
      optionTotal: 0,
      optionsCount: 0,
      promoId: '',
      promoCode: '',
      action: req.action,
    };

    await saveBasket({data: data, alert: t('alert')}).then(response => {
      _getBadge();
    });
  };

  const _getBadge = async () => {
    await AsyncStorage.getItem('@basket')
      .then(response => {
        const r = response ? JSON.parse(response) : [];
        let totalAmount = 0;
        if (r?.length > 0) {
          r.map(i => {
            totalAmount +=
              i.count * (parseFloat(i.optionTotal) + parseFloat(i.price));
          });
        } else {
          totalAmount = 0;
        }
        handleBadge(r?.length);
      })
      .catch(e => {
        handleBadge(0);
      });
  };

  const getAlIndex = (arr, id) => {
    return arr.findIndex(obj => obj.id === id.toString());
  };

  const getResults = async (r, n) => {
    let rest = route?.params?.id ? route?.params?.id : r;
    let restname = route?.params?.name ? route?.params?.name : n;
    setBranchName(restname);

    mobileApi
      .get(`/${locale}/getAllProducts?restaurant=${rest}`)
      .then(response => {
        if (response.data) {
          setProducts(response.data);
          setTempProducts(response.data);
        } else {
          setProducts([]);
          setTempProducts([]);
          Alert.alert('', t('product-unavailable', {branchName: restname}), [
            {
              text: t('alert').okBtn,
              onPress: () => {
                setBranch('', '').then(() => {
                  console.log(route?.params);
                  navigate(route?.params?.fromStack, {
                    screen: route?.params?.fromScreen,
                    params: {
                      id: route?.params?.id,
                      results: route?.params?.results,
                      menu: route.params.menu,
                    },
                  });
                });
              },
            },
          ]);
        }
        setLoading(false);
      })
      .catch(error => {
        Alert.alert(t('payment').warning, t('alert').responseTime, [
          {
            text: t('alert').retry,
            onPress: () => {
              setBranch('', '').then(() => {
                _goBack();
              });
            },
          },
        ]);
        setLoading(false);
      });
  };

  const _updateCount = (itemid, count) => {
    //console.log(itemid, count);
    setPageState({
      ...pagestate,
      [`${itemid}`]: {
        count,
      },
    });
  };

  const _goBack = () => {
    navigate('DeliveryType');
  };

  const _setBranch = async () => {
    Alert.alert(t('payment').warning, t('alert').changeBranch, [
      {
        text: t('alert').okBtn,
        onPress: () => {
          setBranch('', '').then(() => {
            _goBack();
          });
        },
      },
      {
        text: t('alert').cancelBtn,
        onPress: () => {},
      },
    ]);
  };

  const _searchProduct = async () => {
    setLoading(true);
    let filteredItems = [];
    let categoryFound;
    if (search?.length >= 3) {
      products.map((item, i) => {
        categoryFound = 0;
        filteredProducts = [];
        item.data.map((subitem, j) => {
          if (subitem.name.toLowerCase().match(search.toLowerCase())) {
            categoryFound = 1;
            filteredProducts.push(subitem);
          }
        });
        if (categoryFound == 1) {
          filteredItems.push({
            id: item.id,
            title: item.title,
            data: filteredProducts,
          });
        }
      });
      setTempProducts(filteredItems);
    } else {
      setTempProducts(products);
    }
    setLoading(false);
  };

  useEffect(() => {
    _searchProduct();
  }, [search]);

  useEffect(() => {
    navigation.setOptions({
      title: route?.params?.menu === 0 ? t('menu').orders : t('menuView'),
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
      headerLeft: () => null,
    });
    if (route?.params?.menu === 1) {
      navigation.setOptions({
        headerLeft: () => (
          <TouchableOpacity
            onPress={() => {
              route?.params?.detail === true
                ? navigate('Branch', {
                    screen: 'BranchDetail',
                    params: {
                      id: route?.params?.id,
                      results: route?.params?.results,
                    },
                  })
                : navigate('Branch', {screen: 'BranchMain'});
            }}>
            <Icon
              name="angle-left"
              type="font-awesome"
              iconStyle={{color: 'white', fontSize: 40, paddingLeft: 10}}
            />
          </TouchableOpacity>
        ),
      });
    }
    _readVal();
    getBadge();
    handleBadge(state.totalCount);
    setLoading(true);
  }, [route?.params?.id]);

  useEffect(() => {
    if (route?.params?.menu === 0) {
      AsyncStorage.getItem('@branchId')
        .then(r => {
          if (!r) {
            navigate('DeliveryType');
            return false;
          }

          mobileApi
            .get(`/${locale}/getRestaurants?id=${r}&t=${new Date().getTime()}`)
            .then(resp => {
              //console.log(resp.data[0]);
              if (resp.data) {
                const now = new Date();

                if (resp.data[0].outwork === 1) {
                  Alert.alert(
                    t('payment').warning,
                    `${t('payment').outofservice1}${resp.data[0].orderHour}${
                      t('payment').outofservice2
                    }`,
                    [
                      {
                        text: t('payment').ok,
                        onPress: () => {
                          setBranch('', '').then(() => {
                            navigate('DeliveryType');
                          });
                        },
                      },
                    ],
                  );
                } else if (
                  resp.data[0].orderStatus === 0 ||
                  resp.data[0].status === 'N'
                ) {
                  Alert.alert(
                    t('payment').warning,
                    t('payment').gecici_pakete_kapali_txt,
                    [
                      {
                        text: t('payment').ok,
                        onPress: () => {
                          setBranch('', '').then(() => {
                            navigate('DeliveryType');
                          });
                        },
                      },
                    ],
                  );
                } else if (resp.data[0].orderAlert === '1') {
                  Alert.alert(
                    t('payment').warning,
                    resp.data[0].orderAlertMsg,
                    [
                      {
                        text: t('payment').ok,
                      },
                    ],
                  );
                } else {
                  //navigate('MenuList', {menu: 0, g: 0});
                }
              } else {
                setBranch('', '').then(() => {
                  navigate('DeliveryType');
                });
              }
            });
        })
        .catch(err => {
          // _goBack();
        });
    }
  }, []);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          backgroundColor: '#000000',
          height: 40,
          marginBottom: 10,
        }}>
        <TouchableOpacity onPress={() => _setBranch('', '')}>
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              paddingTop: 10,
            }}>
            <Icon
              name="check"
              type="font-awesome"
              iconStyle={{color: '#06f200', fontSize: 16, paddingLeft: 10}}
            />
            <Text
              style={{
                color: 'white',
                fontSize: 16,
                paddingLeft: 10,
                fontFamily: 'AndaleMono',
              }}>
              <Text numberOfLines={1}>
                {branchName?.length < 22
                  ? `${branchName}`
                  : `${branchName.substring(0, 19)}...`}
                <Text style={{color: '#d0cad4'}}>
                  {' '}
                  [{t('btnChangeBranch')}]
                </Text>
              </Text>
            </Text>
          </View>
        </TouchableOpacity>

        {route?.params?.menu === 0 && (
          <TouchableOpacity
            onPress={() => {
              if (searchView) {
                setSearchView(false);
                setSearch('');
              } else {
                setSearchView(true);
              }
            }}>
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#be0f16',
                height: 40,
                width: 45,
                paddingLeft: 15,
              }}>
              <Icon
                name="magnifier"
                type="simple-line-icon"
                iconStyle={{color: 'white', fontSize: 18}}
              />
            </View>
          </TouchableOpacity>
        )}
      </View>

      {/* ARAMA BÖLÜMÜ  / CSS dosyasını sayfanın altından alıyor. O yüzden burada still çalışmıyor   
      <View style={styless.kutu_full}>
        <TextInput
          style={styless.InputStyle}
          autoCorrect={false}
          value={search}
          onChangeText={value => _searchProduct(value)}
        />
      </View>
        */}
      {searchView ? (
        <View style={styless.kutu_full}>
          <TextInput
            style={[
              styless.InputStyle,
              {borderWidth: 0.5, borderColor: 'silver'},
            ]}
            autoCorrect={false}
            value={search}
            placeholder={t('searchPlaceholder')}
            placeholderTextColor="#000"
            onChangeText={value => setSearch(value)}
          />
        </View>
      ) : null}
      <SectionList
        sections={tempProducts}
        ref={listRef}
        keyExtractor={_keyExtractor}
        stickySectionHeadersEnabled={false}
        scrollToLocationOffset={5}
        tabBarStyle={styles.tabBar}
        initialNumToRender={400}
        onScrollToIndexFailed={info => {
          console.log(info);
        }}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        renderTab={({title, isActive}) => (
          <View
            style={[
              styles.tabContainer,
              {
                color: isActive ? '#ffffff' : '#000000',
                backgroundColor: isActive ? '#78BAE7' : '#78BAE7',
              },
            ]}>
            <Text
              style={[
                styles.tabText,
                {
                  color: isActive ? '#000' : '#fff',
                  backgroundColor: isActive ? '#78BAE7' : '#78BAE7',
                },
              ]}>
              {title}
            </Text>
          </View>
        )}
        renderSectionHeader={({section}) => (
          <View>
            <View style={styles.sectionHeaderContainer} />
            <Text style={styles.sectionHeaderText}>{section.title}</Text>
          </View>
        )}
        renderItem={({item, index, section}) => (
          <TouchableOpacity
            key={item.productId}
            onPress={() => {
              navigate('MenuDetail', {
                id: item.productId,
                item: item,
                menu: route?.params?.menu === 1 ? route?.params?.menu : 0,
                test: 1,
                basketCount: pagestate[`${item.productId}`]?.count
                  ? pagestate[`${item.productId}`]?.count
                  : 1,
                _updateCount: _updateCount,
                category: section?.title,
              });
            }}>
            <View style={styless.kutu_full}>
              <View>
                <Image
                  source={{
                    uri: item.thumb,
                  }}
                  style={styless.urunfoto}
                />
              </View>
              <View style={styless.kutu_child}>
                <View>
                  <Text style={sc.UrunAdi}>{item.name}</Text>
                </View>
                <View style={{alignItems: 'flex-start', paddingTop: 5}}>
                  <View
                    style={[
                      styles.detay_kutu,
                      {display: 'flex', flexDirection: 'row'},
                    ]}>
                    {item.yeni === '1' && (
                      <Icon
                        name="star"
                        type="font-awesome-5"
                        iconStyle={{
                          color: 'orange',
                          fontSize: 13,
                          marginRight: 5,
                        }}
                      />
                    )}
                    {item.hot === '1' && (
                      <Icon
                        name="pepper-hot"
                        type="font-awesome-5"
                        iconStyle={{
                          color: 'red',
                          fontSize: 13,
                          marginRight: 5,
                        }}
                      />
                    )}
                    {item.vegi === '1' && (
                      <Icon
                        name="seedling"
                        type="font-awesome-5"
                        iconStyle={{
                          color: 'green',
                          fontSize: 13,
                          marginRight: 5,
                        }}
                      />
                    )}
                    {item.pismis === '1' && (
                      <Icon
                        name="fire"
                        type="font-awesome-5"
                        iconStyle={{
                          color: 'purple',
                          fontSize: 13,
                          marginRight: 5,
                        }}
                      />
                    )}
                  </View>
                </View>
              </View>

              <View
                style={{
                  alignItems: 'flex-start',
                  marginRight: 6,
                }}>
                {route?.params?.menu === 0 && ( // referred here
                  <View style={[styles.basket_count_container, {marginTop: 0}]}>
                    {item?.option?.length === 0 ||
                    pagestate[`${item.productId}`]?.count > 0 ? (
                      <TouchableOpacity
                        onPress={() => {
                          _decBasket({
                            action: 'dec',
                            item,
                            category: section?.title,
                          });
                        }}>
                        <View style={styles.basket_count_left}>
                          {pagestate[`${item.productId}`]?.count === 1 ? (
                            <Icon
                              name="trash-o"
                              type="font-awesome"
                              iconStyle={{color: 'silver', fontSize: 15}}
                            />
                          ) : (
                            <Text>
                              <Icon
                                name="minus"
                                type="font-awesome"
                                iconStyle={{
                                  color: '#fff',
                                  fontSize: 14,
                                  paddingTop: 3,
                                }}
                              />
                            </Text>
                          )}
                        </View>
                      </TouchableOpacity>
                    ) : null}
                    <View style={styles.basket_count_middle}>
                      <Text style={sc.Adet}>
                        {pagestate[`${item.productId}`]?.count
                          ? pagestate[`${item.productId}`]?.count
                          : ''}
                      </Text>
                    </View>
                    <TouchableOpacity
                      onPress={() => {
                        if (item?.option?.length > 0) {
                          navigate('MenuDetail', {
                            id: item.productId,
                            item: item,
                            menu:
                              route?.params?.menu === 1
                                ? route?.params?.menu
                                : 0,
                            test: 1,
                            basketCount: pagestate[`${item.productId}`]?.count
                              ? pagestate[`${item.productId}`]?.count
                              : 1,
                            _updateCount: _updateCount,
                            category: section?.title,
                          });
                        } else {
                          [1, 2].map(___ => {
                            setTimeout(() => {
                              _addBasket({
                                action: 'add',
                                item,
                                category: section?.title,
                              });
                            }, 1000);
                          });
                        }
                      }}>
                      <View style={styles.basket_count_right}>
                        <Icon
                          name="plus"
                          type="font-awesome"
                          iconStyle={{
                            color: '#fff',
                            fontSize: 15,
                            paddingTop: 2,
                            paddingLeft: 0,
                          }}
                        />
                      </View>
                    </TouchableOpacity>
                  </View>
                )}

                {/* Sepete Ekleme */}
              </View>

              <View
                style={{alignItems: 'flex-end', paddingTop: 5, minWidth: 70}}>
                <Text style={sc.Fiyat}>
                  {item.price} {t('currency')}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        )}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  tabBar: {
    backgroundColor: '#ffffff',
  },

  tabContainer: {
    borderRadius: 4,
    margin: 4,
    paddingVertical: 5,
    paddingHorizontal: 10,
    marginBottom: 5,
  },

  tabText: {
    color: '#000000',
    fontSize: 14,
    fontWeight: '500',
    padding: 4,
    fontFamily: 'IBMPlexSans-Medium',
  },

  separator: {
    height: 0.5,
    width: '96%',
    alignSelf: 'flex-end',
    backgroundColor: '#eaeaea',
  },
  sectionHeaderContainer: {
    height: 10,
    backgroundColor: '#f6f6f6',
    borderTopColor: '#f4f4f4',
    borderTopWidth: 1,
    borderBottomColor: '#f4f4f4',
    borderBottomWidth: 1,
  },
  sectionHeaderText: {
    color: '#78BAE7',
    backgroundColor: '#000',
    fontSize: 20,
    fontWeight: 'normal',
    paddingTop: 10, paddingBottom: 15,
    paddingLeft: 15,
    fontFamily: 'FrontageCondensed-Bold',
    width: '40%',
  },
  itemContainer: {
    paddingVertical: 20,
    paddingHorizontal: 15,
    backgroundColor: '#fff',
  },
  itemTitle: {
    flex: 1,
    fontSize: 20,
    color: '#131313',
  },
  itemPrice: {
    fontSize: 14,
    color: '#131313',
    fontFamily: 'IBMPlexSans-Medium',
  },
  itemDescription: {
    marginTop: 10,
    color: '#b6b6b6',
    fontSize: 16,
    fontFamily: 'IBMPlexSans-Medium',
  },
  itemRow: {
    flexDirection: 'row',
  },

  basket_count_container: {
    display: 'flex',
    flexDirection: 'row',
    height: 25,
    width: 60,
    justifyContent: 'space-between',
    borderRadius: 0,
  },
  basket_count_left: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 20,
    height: 20,
    backgroundColor: '#000',
    borderRadius: 0,
  },
  basket_count_middle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 50,
    
  },
  basket_count_right: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 20,
    height: 20,
    backgroundColor: '#000',
   
    borderRadius: 0,
  },
});
export default MenuScreen;
