import React, {useContext, useState, useEffect} from 'react';
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  Image,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import {Icon} from 'react-native-elements';
import Loading from '../../components/Loading';
import LocalizationContext from '../../context/LocalizationContext';
import styless from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import mobileApi from '../../api/mobileApi';
import {navigate} from '../../navigationRef';
import {Context as UserContext} from '../../context/UserContext';
const YENILER_MEVSIMI_CATEGORY_ID = 45;
const NewProductsScreen = ({navigation, route}) => {
  const {id: branchId, name: branchName} = route?.params;

  const {t, locale} = useContext(LocalizationContext);
  const {setBranch} = useContext(UserContext);

  const [isLoading, setIsLoading] = useState(true);
  const [newProducts, setNewProducts] = useState([]);

  const fetchNewProducts = async () => {
    setIsLoading(true);
    try {
      const response = await mobileApi.get(
        `/${locale}/getAllProducts?restaurant=${branchId}`,
      );
      if (!response.data) return;
      const newProductsCategory = response.data.find(
        category => category.id == YENILER_MEVSIMI_CATEGORY_ID,
      );
      if (newProductsCategory) setNewProducts([newProductsCategory]);
    } catch (error) {
      console.error('Error fetching yeniler products:', error);
      Alert.alert(t('payment').warning, t('alert').responseTime, [
        {
          text: t('alert').retry,
          onPress: () => {
            setBranch('', '').then(() => {
              _goBack();
            });
          },
        },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const _setBranch = async () => {
    Alert.alert(t('payment').warning, t('alert').changeBranch, [
      {
        text: t('alert').okBtn,
        onPress: () => {
          setBranch('', '').then(() => {
            _goBack();
          });
        },
      },
      {
        text: t('alert').cancelBtn,
        onPress: () => {},
      },
    ]);
  };

  const _goBack = () => {
    navigate('DeliveryType');
  };

  useEffect(() => {
    navigation.setOptions({
      title: t('anasayfa.yeniler1'),
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    console.log('new branch name=>', branchName);
    fetchNewProducts();
  }, []);

  if (isLoading) {
    return <Loading />;
  }

  const renderProduct = (item, categoryTitle) => (
    <TouchableOpacity
      key={item.productId}
      onPress={() => {
        navigate('MenuDetail', {
          id: item.productId,
          item: item,
          category: categoryTitle,
        });
      }}>
      <View style={styless.kutu_full}>
        <View>
          <Image
            source={{
              uri: item.thumb,
            }}
            style={styless.urunfoto}
          />
        </View>
        <View style={styless.kutu_child}>
          <View>
            <Text style={sc.UrunAdi}>{item.name}</Text>
          </View>
          <View style={{alignItems: 'flex-start', paddingTop: 5}}>
            <View
              style={[
                styles.detay_kutu,
                {display: 'flex', flexDirection: 'row'},
              ]}>
              <Icon
                name="star"
                type="font-awesome-5"
                iconStyle={{
                  color: 'orange',
                  fontSize: 13,
                  marginRight: 5,
                }}
              />
              {item.hot === '1' && (
                <Icon
                  name="pepper-hot"
                  type="font-awesome-5"
                  iconStyle={{
                    color: 'red',
                    fontSize: 13,
                    marginRight: 5,
                  }}
                />
              )}
            </View>
          </View>
        </View>

        <View style={{alignItems: 'flex-end', paddingTop: 5, minWidth: 70}}>
          <Text style={sc.Fiyat}>
            {item.price} {t('currency')}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          backgroundColor: '#ec1d23',
          height: 40,
          marginBottom: 10,
        }}>
        <TouchableOpacity onPress={() => _setBranch('', '')}>
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              paddingTop: 10,
            }}>
            <Icon
              name="check"
              type="font-awesome"
              iconStyle={{color: '#06f200', fontSize: 16, paddingLeft: 10}}
            />
            <Text
              style={{
                color: 'white',
                fontSize: 16,
                paddingLeft: 10,
                fontFamily: 'IBMPlexSans-SemiBold',
              }}>
              <Text numberOfLines={1}>
                {branchName?.length < 22
                  ? `${branchName}`
                  : `${branchName?.substring(0, 19)}...`}
                <Text style={{color: '#d0cad4'}}>
                  {' '}
                  [{t('btnChangeBranch')}]
                </Text>
              </Text>
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      <ScrollView>
        {newProducts.map(category => (
          <View key={category.title}>
            <Text style={styles.categoryTitle}>{category.title}</Text>
            {category.data.map(item => renderProduct(item, category.title))}
          </View>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  categoryTitle: {
    color: '#010101',
    backgroundColor: '#fff',
    fontSize: 20,
    fontWeight: 'normal',
    paddingTop: 20,
    paddingBottom: 5,
    paddingHorizontal: 15,
    fontFamily: 'IBMPlexSans-Medium',
  },
  detay_kutu: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default NewProductsScreen;
