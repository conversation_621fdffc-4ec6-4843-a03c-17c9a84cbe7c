import React, {useContext, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  Image,
  ImageBackground,
} from 'react-native';
import {Icon} from 'react-native-elements';
import AsyncStorage from '@react-native-async-storage/async-storage';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import {navigate} from '../../navigationRef';
import mobileApi from '../../api/mobileApi';

import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';

const PickupScreen = () => {
  const {t, locale} = useContext(LocalizationContext);
  const {state, login, setBranch} = React.useContext(UserContext);
  const [results, setResults] = React.useState(null);
  const [branchs, setBranchs] = React.useState([]);
  const [cityIndex, setCityIndex] = React.useState(null);
  const [cityId, setCityId] = React.useState(null);
  const [cityName, setCityName] = React.useState(null);
  const [townIndex, setTownIndex] = React.useState(null);
  const [townId, setTownId] = React.useState(null);
  const [townName, setTownName] = React.useState(null);
  const l = t('payment');

  const _onCity = (id, name, index) => {
    setCityName(name);
    setCityId(id);
    setCityIndex(index);
    setTownName('');
    setTownId('');
    setTownIndex('');
    setBranchs([]);
  };

  const _onTown = (id, name, index) => {
    setTownName(name);
    setTownId(id);
    setTownIndex(index);
  };

  const _getResults = async () => {
    const response = await mobileApi.get(`/${locale}/getDistincts`);
    //console.log(response.data);
    setResults(response.data);
  };

  const _getBranchs = async () => {
    const response = await mobileApi.get(
      `/${locale}/getRegionRestaurants?id=${townId}&t=${new Date().getTime()}`,
    );
    //console.log(`/${locale}/getRegionRestaurants?id=${townId}&t=${new Date().getTime()}`);
    //console.log('response', response.data, townId);
    if (response.data) {
      setBranchs(response.data);
    } else {
      setBranchs([]);
    }
  };

  const _setBranch = async (
    id,
    name,
    regionLimit,
    deliveryPrice,
    serviceTime,
  ) => {
    const service = '2';
    const addressid = '-1';
    await setBranch(
      id,
      name,
      service,
      addressid,
      regionLimit,
      deliveryPrice,
      serviceTime,
    )
      .then(() => navigate('MenuList', {id, name, service, menu: 0}))
      .catch(err => {
        console.log(err);
      });
  };

  React.useEffect(() => {
    if (townId) _getBranchs();
  }, [townId]);

  React.useEffect(() => {
    _getResults();
  }, []);

  if (!results) {
    return null;
  }

  return (
    <ImageBackground
      source={require('../../../assets/img/back_alt.jpg')}
      resizeMode="cover"
      style={{flex: 1, marginTop: 0}}>
      <View style={pageStyle.pagecontainer}>
        <ScrollView style={{flex: 1}}>
          <View style={[styles.alertbox, {marginVertical: 10, backgroundColor: '#78bae750'}]}>
            <Text
              style={[
                styles.text16,
                styles.tcenter,
                styles.black,
                styles.ls1,
                styles.ozelfont_medium,
              ]}>
              {l.pickup_txt1}
            </Text>
          </View>
          <View style={{width: '100%', marginTop: 30}}>
            <TouchableOpacity
              style={[styles.listeleme, {marginHorizontal: 0}]}
              onPress={() =>
                navigate('AddressCity', {
                  results,
                  _onCity: _onCity,
                })
              }>
              <View style={styles.kutu_full}>
                {/* <View style={styles.listeleme_sol}>
                <Icon
                  name="angle-right" 
                  type="font-awesome"
                 iconStyle={sc.ListIconStyle}
                />
              </View> */}
                <View style={styles.listeleme_orta}>
                  <Text style={sc.AdresBaslikSecim}>
                    {cityName ? cityName : t('login').selectCity}
                  </Text>
                </View>
                <View style={styles.listeleme_sag}>
                  <Icon name="angle-right" color="gray" type="font-awesome" />
                </View>
              </View>
            </TouchableOpacity>
          </View>
          {cityId ? (
            <View style={{width: '100%'}}>
              <TouchableOpacity
                style={[styles.listeleme, {marginHorizontal: 0}]}
                onPress={() =>
                  navigate('AddressTown', {
                    results: results.city[cityIndex],
                    _onTown: _onTown,
                  })
                }>
                <View style={styles.kutu_full}>
                  <View style={styles.listeleme_orta}>
                    <Text style={sc.AdresBaslikSecim}>
                      {townName ? townName : t('login').selectTown}
                    </Text>
                  </View>
                  <View style={styles.listeleme_sag}>
                    <Icon name="angle-right" color="gray" type="font-awesome" />
                  </View>
                </View>
              </TouchableOpacity>
            </View>
          ) : null}
          {branchs.length > 0 && townId ? (
            <View style={{width: '100%', marginTop: 40}}>
              <Text style={[styles.text16,{fontFamily: 'AndaleMono', letterSpacing: -1}]}>{l.branchlist}</Text>
            </View>
          ) : townId ? (
            <View style={[styles.alertbox]}>
              <Image
                source={require('../../../assets/img/uzgun.png')}
                style={{
                  height: 90,
                  width: 90,
                  marginVertical: 20,
                  alignSelf: 'center',
                  resizeMode: 'contain',
                }}
              />

              <Text style={[styles.text18, styles.tcenter, styles.Txt_renk1]}>
                {l.nobranchlist}
              </Text>
            </View>
          ) : null}

          {branchs.map(item => {
            const orderStatus = item.orderStatus;
            const status = item.status;

            const outwork = item.outwork;
            const orderHour = item.orderHour;

            return (
              <View style={{width: '100%'}} key={item.branchId}>
                <TouchableOpacity
                  style={[styles.listeleme, {marginHorizontal: 0}]}
                  onPress={() => {
                    if (status === 'Y' || orderStatus === 1) {
                      _setBranch(
                        item.branchId,
                        item.branchName,
                        '0',
                        '0',
                        item.service,
                      );
                    }
                  }}>
                  <View style={styles.kutu_full}>
                    <View style={styles.listeleme_orta}>
                      <Text
                        style={[
                          styles.SubeAd,
                          styles.kurumsalfont_bold,
                          styles.red,
                          styles.text22,
                        ]}>
                        {item.branchName}
                      </Text>
                      <Text style={[styles.SubeAdres, styles.black]}>
                        {' '}
                        {l.workhour} : {item.hour}
                      </Text>

                      {/*status === 'N' || orderStatus === 0 ? (
                      <Text style={[styles.SubeAdres, {color: 'red'}]}>
                        {l.gecici_pakete_kapali_txt}
                      </Text>
                    ) : null*/}
                    </View>
                    <View style={styles.listeleme_sag}>
                      <Icon
                        name="angle-right"
                        color="gray"
                        type="font-awesome"
                      />
                    </View>
                  </View>
                </TouchableOpacity>
              </View>
            );
          })}
        </ScrollView>
      </View>
    </ImageBackground>
  );
};

const pageStyle = StyleSheet.create({
  pagecontainer: {paddingHorizontal: 15, flex: 1},
});

export default PickupScreen;
