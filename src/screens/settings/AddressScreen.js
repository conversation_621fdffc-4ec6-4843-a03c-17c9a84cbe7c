import React, {useState, useEffect} from 'react';
import {
  View,
  Image,
  Text,
  TextInput,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';

import {Icon, ListItem, Input} from 'react-native-elements';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import AsyncStorage from '@react-native-async-storage/async-storage';

import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';
import {navigate} from '../../navigationRef';
import mobileApi from '../../api/mobileApi';

const SettingsAddress = ({navigation}) => {
  const {state, login} = React.useContext(UserContext);

  const [userAddress, setUserAddress] = useState([]);
  const [loginMemberId, setLoginMemberId] = React.useState();
  const [loginToken, setLoginToken] = React.useState();
  const [data, setData] = React.useState();

  const {t, locale} = React.useContext(LocalizationContext);
  const l = t('login');

  const _readVal = async () => {
    await AsyncStorage.multiGet(['@userdata', '@token', '@memberid'])
      .then(r => {
        const data = JSON.parse(r[0][1]);
        setUserAddress(data.address);
        setLoginToken(r[1][1]);
        setLoginMemberId(r[2][1]);
        setData(data);
      })
      .catch(err => {
        console.log(err);
      });
  };

  const _delAddress = addressid => {
    Alert.alert(t('payment').warning, t('alert.delAddress'), [
      {
        text: t('alert').cancelBtn,
        onPress: () => console.log('Cancel Pressed'),
        style: 'cancel',
      },
      {
        text: t('alert').okBtn,
        onPress: () => {
          const request = {
            memberId: loginMemberId,
            token: loginToken,
            addressid: addressid,
          };

          mobileApi.post(`/${locale}/delAddress`, request).then(response => {
            console.log(response.data);
            if (response.err === 1) {
              Alert.alert('HATA!', response.msg, [
                {
                  text: t('payment').ok,
                },
              ]);
            } else {
              Alert.alert('TEBRİKLER', 'Adresiniz silinmiştir.', [
                {
                  text: t('payment').ok,
                },
              ]);
              const addressIndex = getAddressIndex(data.address, addressid);
              data.address.splice(addressIndex, 1);
              AsyncStorage.setItem('@userdata', JSON.stringify(data));
              _readVal();
            }
          });
        },
      },
    ]);
  };

  const getAddressIndex = (arr, id) => {
    return arr.findIndex(obj => obj.id === id);
  };

  const addOrEditAddress = ({mode, id}) => {
    navigate('LoginStack', {
      screen: 'Address',
      params: {page: 1, mode, id},
    });
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      navigation.setOptions({
        headerTruncatedBackTitle: '',
        headerBackTitle: '',
      });
      _readVal();
    });
    return unsubscribe;
  }, [navigation]);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <View style={styles.topmargin}></View>
        {userAddress &&
          userAddress.map(item => {
            return (
              <View style={[styles.blokkutu, styles.bgrenk5]} key={item.id}>
                <View style={styles.detay_kutu_sol}>
                  <Text style={sc.AdresBaslik}>{item.label}</Text>
                  <Text style={sc.AdresDetay}>
                    {item.detail +
                      ' ' +
                      item.regionName +
                      '\n' +
                      item.directions}
                  </Text>
                  <Text style={sc.AdresDetay}>
                    {item.districtName + ' ' + item.cityName}
                  </Text>
                  <Text style={sc.AdresDetay}>{item.phone}</Text>
                </View>

                <View style={styles.detay_kutu}>
                  <View style={styles.detay_kutu_center}>
                    <TouchableOpacity
                      onPress={() => {
                        addOrEditAddress({mode: 'update', id: item.id});
                      }}>
                      <View
                        style={[
                          styles.button_yeni,
                          styles.bgrenk6,
                        ]}>
                        <Text
                          style={[
                            sc.PrimaryButtonOutline_text_small,
                            styles.mavi,
                            styles.ozelfont_bold,
                          ]}>
                          {/* <Icon name="note" color="gray" type="simple-line-icon" size={14} style={{ marginRight: 10, marginTop: 5 }} /> */}
                          {t('button.edit')}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                  <View style={styles.detay_kutu_center}>
                    <TouchableOpacity onPress={() => _delAddress(item.id)}>
                      <View
                        style={[
                          styles.button_yeni,
                          styles.bgrenk7,
                        ]}>
                        <Text
                          style={[
                            sc.PrimaryButtonOutline_text_small,
                            styles.black,
                            styles.ozelfont_bold,
                          ]}>
                          {/* <Icon name="trash" color="gray" type="simple-line-icon" size={14} style={{ marginRight: 10, marginTop: 5 }} /> */}
                          {t('button.delete')}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            );
          })}
        {!userAddress ? (
          <View>
            <Text style={[styles.text16, {padding: 20, textAlign: 'center'}]}>
              {t('alert').addressNotFound}
            </Text>
          </View>
        ) : null}
      </ScrollView>

      <TouchableOpacity
        style={[sc.PrimaryButton, {marginVertical: 20}]}
        onPress={() => addOrEditAddress({mode: 'new', id: ''})}>
        <Text style={sc.PrimaryButton_text}>{t('login.addressAddTitle')}</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default SettingsAddress;
