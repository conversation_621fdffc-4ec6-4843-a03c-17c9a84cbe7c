import React, { useState, useEffect } from "react";
import { SafeAreaView } from "react-native";

import styles from "../../stylesheets/styles";
import LocalizationContext from "../../context/LocalizationContext";
import { useIsFocused } from "@react-navigation/native";
import mobileApi from "../../api/mobileApi";
import { WebView } from "react-native-webview";

const SettingsAgreement = ({ navigation }) => {
  const isFocused = useIsFocused();
  const { locale } = React.useContext(LocalizationContext);

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: "",
      headerBackTitle: "",
    });
  }, [isFocused]);

  return (
    <SafeAreaView style={styles.container}>
      <WebView
        source={{
          uri: `${mobileApi.defaults.baseURL}/page/site-kullanim-sartlari.php?lang=${locale}&t=${new Date().getTime()}`,
        }}
        startInLoadingState={true}
        scalesPageToFit={true}
        style={{
          flex: 1,
        }}
      />
    </SafeAreaView>
  );
};

export default SettingsAgreement;
