import React, {useEffect} from 'react';
import {View, Image, Text, SafeAreaView, ScrollView} from 'react-native';

import styles from '../../stylesheets/styles';
import LocalizationContext from '../../context/LocalizationContext';
import {useIsFocused} from '@react-navigation/native';

const CouponDetailScreen = ({navigation, route}) => {
  const {t} = React.useContext(LocalizationContext);

  const {item} = route.params;
  const isFocused = useIsFocused();

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
  }, [isFocused]);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <View>
          <View style={styles.detay_kutu}>
            <Image
              source={{
                uri: item.resim,
              }}
              style={{width: '100%', height: 150}}
            />
          </View>
          <View style={{margin: 20}}>
            <Text style={[styles.text18, {marginVertical: 10, color: '#000'}]}>
              {item.shortdesc}
            </Text>
            <Text
              style={[
                styles.text16,
                styles.bold,
                {marginVertical: 10, color: '#000'},
              ]}>
              {t('offerterm')}
            </Text>
            <Text style={[styles.text14, {color: '#000'}]}>
              {item.longdesc}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default CouponDetailScreen;
