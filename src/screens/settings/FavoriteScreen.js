import React, {useState, useEffect, useContext} from 'react';
import {
  View,
  Image,
  Text,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import {Icon} from 'react-native-elements';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';
import {useIsFocused} from '@react-navigation/native';
import mobileApi from '../../api/mobileApi';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {navigate} from '../../navigationRef';

const SettingsFavorite = ({navigation}) => {
  const isFocused = useIsFocused();
  const {state, login, usersave} = useContext(UserContext);
  const {t, locale} = useContext(LocalizationContext);
  const p = t('profile');
  const [results, setResults] = useState([]);
  const [favId, setFavId] = useState();
  const [loginToken, setLoginToken] = useState();
  const [loginMemberId, setLoginMemberId] = useState();
  const [branchId, setBranchId] = useState();

  const _readVal = async () => {
    await AsyncStorage.multiGet(['@token', '@memberid', '@branchId'])
      .then(r => {
        setLoginToken(r[0][1]);
        setLoginMemberId(r[1][1]);
        setBranchId(r[2][1]);
        const req = {memberId: r[1][1], token: r[0][1]};
        _getResults(req);
      })
      .catch(err => {
        //console.log("a", err);
      });
  };

  const _getResults = async request => {
    await mobileApi.post(`/${locale}/getFavorites`, request).then(response => {
      if (!response.data) {
        setResults([]);
      } else {
        setFavId(response.data.menu[0].menuid);
        if (response.data.menu[0].products) {
          setResults(response.data.menu[0].products);
        } else {
          setResults([]);
        }
      }
    });
  };

  _updateCount = (itemid, count) => {
    //console.log(itemid, count);
  };

  const gotoMenu = async product => {
    if (branchId) {
      await mobileApi
        .get(
          `/${locale}/getProductDetail?restaurant=${branchId}&product=${product}`,
        )
        .then(response => {
          if (response.data?.err === 1) {
            Alert.alert(t('payment').warning, t('nofavadd'), [
              {
                text: t('payment').ok,
              },
            ]);
          } else {
            navigate('MenuDetail', {
              id: product,
              item: response.data[0],
              menu: 0,
              basketCount: 1,
              category: response.data[0].category,
              _updateCount: _updateCount,
            });
          }
        });
    } else {
      Alert.alert(t('payment').warning, t('nobranch'), [
        {
          text: t('payment').ok,
        },
      ]);
    }
  };

  const _delFavoriteProduct = async productId => {
    const req = {
      memberId: loginMemberId,
      favId: favId,
      productId: productId,
      token: loginToken,
    };
    await mobileApi.post(`/${locale}/delFavoriteProduct`, req).then(res => {
      if (res.data.err === 1) {
        Alert.alert(t('payment').warning, res.data.msg, [
          {
            text: t('payment').ok,
          },
        ]);
      } else {
        _readVal();
        Alert.alert(t('login').congratulations, res.data.msg, [
          {
            text: t('payment').ok,
          },
        ]);
      }
    });
  };

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    _readVal();
  }, [isFocused]);

  return (
    <View style={styles.container}>
      <SafeAreaView>
        <ScrollView>
          <View style={styles.topmargin}></View>
          {results &&
            results.length > 0 &&
            results.map((item, i) => {
              return (
                <View style={styles.kutu_full} key={i}>
                  <View>
                    <Image
                      source={{
                        uri: item.thumb,
                      }}
                      style={styles.urunfoto}
                    />
                  </View>
                  <View style={styles.kutu_child}>
                    <Text style={sc.UrunAdi}>{item.name}</Text>
                    <Text style={([sc.Fiyat], {color: '#83048c',fontFamily: 'AndaleMono',fontWeight: 'bold'})}>
                      {item.price} {t('currency')}
                    </Text>
                    <Text style={sc.UrunDesc}>{item.desc}</Text>
                  </View>
                  <View
                    style={{
                      width: 50,
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                    }}>

                    <TouchableOpacity onPress={() => gotoMenu(item.productId)}>
                      <View style={[styles.basket_count_right,{maxHeight:30, marginBottom:30 }]}>
                          <Icon name="plus" type="font-awesome" iconStyle={{color: '#78bae7', fontSize: 14, paddingTop:2}}/>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity
                      onPress={() => _delFavoriteProduct(item.productId)}>
                      <View style={[styles.basket_count_right,{maxHeight:30, backgroundColor:'#c9e3f5'}]}>                      
                          <Icon name="trash" type="font-awesome" iconStyle={{color: 'black', fontSize: 16}}/>
                      </View>
                    </TouchableOpacity>

                  </View>
                </View>
              );
            })}
        </ScrollView>
        {results.length === 0 && (
          <View
            style={{
              display: 'flex',
              alignItems: 'center',
            }}>
            <Text>
            <Text style={styles.black}>{t('nofavorite')}</Text>
            </Text>
          </View>
        )}
      </SafeAreaView>
    </View>
  );
};

export default SettingsFavorite;
