import * as React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity,ImageBackground } from 'react-native';
import { ListItem, Icon, Image, Button, Divider } from 'react-native-elements';
import LocalizationContext from '../../context/LocalizationContext';
import { color } from 'react-native-elements/dist/helpers';

const LanguageScreen = props => {
  const { localizationChange } = props;

  const { t, locale, setLocale } = React.useContext(LocalizationContext);
  const isLogin = true;
  const list = t('langs');
  return (
    <ImageBackground source={require('../../../assets/img/back_language.jpg')} resizeMode="cover" style={{ flex: 1, marginTop: 0 }}>
    <ScrollView>
      <View style={styles.group}>
        {list.map((item, i) => (
          <TouchableOpacity key={i}  
            onPress={() => {
              localizationChange(item.lang);
              console.log(item.lang);
            }}
          >
            <ListItem key={i}  containerStyle={styles.listitem} >
              <Icon name={locale === item.lang ? 'done' : 'radio-button-off'} color={locale === item.lang ? 'green' : 'gray'} />
              <ListItem.Content>
                <ListItem.Title>{item.langDesc}</ListItem.Title>
              </ListItem.Content>
            </ListItem>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  group: {
    borderRadius: 20,
    margin: 20,
  },
  list: { borderRadius: 20 },
});

export default LanguageScreen;
