import React, {useContext, useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  LogBox,
  Image,
  ImageBackground,
  Linking,
  Alert,
} from 'react-native';
import {ListItem, Icon, Button, Divider} from 'react-native-elements';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';
import {navigate} from '../../navigationRef';
import {useIsFocused} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Loading from '../../components/Loading';
import styles from '../../stylesheets/styles';
import VersionNumber from 'react-native-version-number';
import mobileApi from '../../api/mobileApi';

LogBox.ignoreAllLogs();

const SetttinsScreen = ({navigation, route}) => {
  const isFocused = useIsFocused();
  const {state, login, logout, getBadge} = useContext(UserContext);
  const {t, locale} = useContext(LocalizationContext);
  const {handleBadge} = route.params;
  const [activation, setActivation] = useState('0');
  const [isLoading, setLoading] = useState(true);
  const [userPoint, setUserPoint] = useState();
  const [userLevel, setUserLevel] = useState();
  const [userLevelTotal, setUserLevelTotal] = useState(0);
  const [userLevelUp, setUserLevelUp] = useState(0);
  const [userLevelImg, setUserLevelImg] = useState(0);
  const [percent, setPercent] = useState(1);
  const l = t('login');
  const menu = t('settingsMenu');

  const _readVal = async () => {
    await AsyncStorage.multiGet(['@email', '@password']).then(response => {
      const data = {
        email: response[0][1],
        password: response[1][1],
        locale,
        version: VersionNumber.appVersion,
      };
      if (response[1][1] && response[0][1]) {
        login({data: data, alert: t('alert')})
          .then(resp => {
            if (resp.err === 1) {
              setLoading(false);
              // navigate("LoginStack", { Screen: "Login" });
            } else {
              setLoading(false);
              setActivation(resp.activation);
              AsyncStorage.multiGet(['@userdata']).then(r => {
                const data = JSON.parse(r[0][1]);
                // console.log(data);
                //console.log(data)
                setUserPoint(data.point ? data.point : 0);
                setUserLevel(data.loyalty ? data.loyalty : 'Ninja');
                setUserLevelImg(data.level ? data.level : 'N');
                setUserLevelTotal(data.loyaltyTotal ? data.loyaltyTotal : 0);
                setUserLevelUp(data.loyaltyUpLimit ? data.loyaltyUpLimit : 0);
                const p = Math.round(
                  (data.loyaltyTotal / data.loyaltyUpLimit) * 100,
                );
                setPercent(p > 100 ? 100 : p < 100 || p > 0 ? p : 0);
                //setPercent(p < 100 || p > 0 ? p : 0);
              });
            }
          })
          .catch(err => {
            console.log(err);
          });
      } else {
        setActivation('0');
        setLoading(false);
      }
    });
  };

  const _logout = () => {
    logout().then(() => {
      handleBadge(0);
      navigate('Home');
    });
  };

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    _readVal();
  }, [isFocused]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <ImageBackground
      source={require('../../../assets/img/back_alt.jpg')}
      style={{flex: 1, marginTop: 0}}>
      <ScrollView>
        {activation === '1' ? (
          <View
            style={{
              margin: 0,
              marginBottom: 0,
              width: '100%',
              alignItems: 'flex-start',
            }}>
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginTop: 20,
              }}>
              <View
                style={{
                  display: 'flex',
                  flex: 1,
                  alignItems: 'center',
                  marginTop: 20,
                }}>
                <View>
                  <Image
                    source={require('../../../assets/img/sojubar-logo.png')}
                    style={{
                      height: 60,
                      width: 120,
                      marginBottom: 10,
                      alignSelf: 'center',
                      resizeMode: 'contain',
                    }}
                  />
                  <Text
                    style={[
                      styles.text22,
                      styles.kurumsalfont_regular,
                      styles.black,
                      styles.tcenter,
                    ]}>
                    {state.firstname}
                  </Text>
                  <Text
                    style={[
                      styles.text14,
                      styles.mb10,
                      styles.ozelfont_medium,
                      styles.black,
                      styles.tcenter,
                    ]}>
                    {userLevel}
                  </Text>
                </View>
                <View style={{width: '80%'}}>
                  <View
                    style={{
                      backgroundColor: '#f4e5d0',
                      display: 'flex',
                      flexDirection: 'row',
                      borderRadius: 10,
                      maxHeight: 20,
                    }}>
                    <View
                      style={{
                        backgroundColor: '#ec1d23',
                        width: `${percent}%`,
                        padding: 2,
                        borderRadius: 10,
                      }}>
                      <Image
                        source={{
                          uri: `${mobileApi.defaults.baseURL}/assets/img/${userLevelImg}.png?1`,
                        }}
                        style={{
                          height: 60,
                          width: 60,
                          alignSelf: 'flex-end',
                          marginRight: -30,
                          bottom: 20,
                        }}
                      />
                    </View>

                    {/* <Text style={{ color: 'black', fontSize: 12, marginTop:30, zIndex:1, position:'absolute' }}>
                        {userLevelTotal} {t('currency')}
                  </Text> */}
                  </View>
                  <View
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      flexDirection: 'row',
                      paddingTop: 20,
                    }}>
                    <Text style={{color: 'black'}}>0 {t('currency')}</Text>
                    <Text>
                      {userLevelUp} {t('currency')}
                    </Text>
                  </View>
                </View>







          
<View style={{flex: 1,  paddingHorizontal: 10,  flexDirection: 'row',
              height: '50', width: '100%', marginVertical: 20,
            }}>
            <TouchableOpacity
              activeOpacity={0.5}
              style={{width: '48%', marginRight: '2%'}}
              onPress={() => {
                if (state.isLogin) {
                  navigation.navigate('LoginStack', {
                    screen: 'QrCode',
                    params: {page: 'HomePage'},
                  });
                } else {
                  Alert.alert(t('payment').warning, t('qrerror1'), [
                    {
                      text: t('payment').ok,
                      onPress: () => {
                        navigation.navigate('Orders');
                      },
                    },
                  ]);
                }
              }}>
              <View
                style={{
                   zIndex: 1,
                    position: 'absolute',
                    marginLeft: 65,
                    top: 18,
                }}>
                <Text
                  style={{
                    fontSize: 14, textAlign: 'center',
                    color: '#000', letterSpacing: -0.5,
                    fontFamily: 'Andale Mono Bold',
                  }}>
                  {t('anasayfa.qr')}
                  <Text style={{fontFamily: 'AndaleMono'}}>
                    {' '}
                    {t('anasayfa.scan')}
                  </Text>
                </Text>
              </View>

              <Image
                style={styles.home_item_img_icon}
                source={require('../../../assets/img/xp.png')}
              />
            </TouchableOpacity>

           
                 <TouchableOpacity
                activeOpacity={0.5}
    style={{width: '48%', marginLeft: '2%'}}
                onPress={() => {
                  navigation.navigate('LoginStack', {
                    screen: 'SettingsPoint',
                    params: {page: 'HomePage'},
                  });
                }}>
                <View
                  style={{
                    zIndex: 1,
                    position: 'absolute',
                    marginLeft: 80,
                    top: 18,
                  }}>
                  <Text
                    style={{
                      fontSize: 14, alignSelf: 'flex-end',
                      color: '#000', letterSpacing: -0.5,
                      fontFamily: 'Andale Mono Bold',
                       marginRight:10,
                    }}>
                    {t('anasayfa.puanim')}

                  </Text>
                </View>
                <Image
                  style={styles.home_item_img_icon}
                  source={require('../../../assets/img/btn_puanim.png')}
                />
              </TouchableOpacity>
           
          
            
          </View>








                <View>
                  <Text
                    style={[
                      styles.text14,
                      styles.mt15,
                      styles.ozelfont_bold,
                      styles.black,
                    ]}>
                    {l.pointtxt}: {userPoint}
                  </Text>
                </View>
                 <View>
                  <Text
                    style={[
                      styles.text14,
                      styles.mt15,
                      styles.ozelfont_bold,
                      styles.black,
                    ]}>
                    XP: {userPoint}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        ) : null}
        <View style={[styles.group, styles.mt15]}>
          <TouchableOpacity
            onPress={() => {
              if (state.isLogin && activation === '1') {
                navigate('QrCode');
              } else {
                Alert.alert(t('payment').warning, t('qrerror1'), [
                  {
                    text: t('payment').ok,
                    onPress: () => {
                      navigate('LoginStack', {screen: 'Login'});
                    },
                  },
                ]);
              }
            }}>
            {/* <View style={pagestyles.shadow}>
              <Icon name="qr-code-scanner" type="Feather" iconStyle={{ color: 'black', fontSize: 20, paddingRight: 10 }} />
              <Text style={styles.text16}>{t('button.qr')}</Text>
            </View> */}
          </TouchableOpacity>

          {Object.keys(menu).map(item => {
            if (menu[item].login === '1' && activation === '0') {
              return;
            } else {
              return (
                <TouchableOpacity
                  key={item}
                  onPress={() => {
                    if (menu[item].page === '1') {
                      Linking.openURL(`tel:${t('phonenumber')}`);
                    } else if (menu[item].page === '2') {
                      Linking.openURL(`tel:${t('phonenumber2')}`);
                    } else {
                      navigation.navigate(menu[item].name);
                    }
                  }}>
                  <ListItem
                    bottomDivider
                    containerStyle={styles.listitemprofil}>
                    {/* <Icon name={menu[item].icon} type="simple-line-icon" color="#747474" /> */}

                    <ListItem.Content>
                      <ListItem.Title
                        style={[styles.text18, styles.ozelfont_regular, {letterSpacing: -1}]}>
                        {menu[item].title}
                      </ListItem.Title>
                    </ListItem.Content>
                    <ListItem.Chevron />
                  </ListItem>
                </TouchableOpacity>
              );
            }
          })}
        </View>
        {state.isLogin && activation === '1' ? (
          <View
            style={{
              margin: 10,
              width: '100%',
              alignItems: 'center',
              marginBottom: 20,
            }}>
            <TouchableOpacity onPress={() => _logout()}>
              <Text style={[styles.red, styles.bold, styles.text16]}>
                {l.logout}
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View
            style={{
              margin: 10,
              width: '100%',
              alignItems: 'center',
              marginBottom: 20,
            }}>
            <TouchableOpacity
              onPress={() =>
                navigate('LoginStack', {
                  screen: 'Login',
                  params: {
                    page: 'profil',
                  },
                })
              }>
              <Text style={[styles.red, styles.bold, styles.text16]}>
                {l.login}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        <View
          style={{
            margin: 10,
            width: '95%',
            alignItems: 'flex-start',
            marginBottom: 20,
          }}>
          <Text style={[styles.text11, styles.black]}>
            {' '}
            {l.version} {VersionNumber.appVersion}
          </Text>
        </View>
      </ScrollView>
    </ImageBackground>
  );
};

const pagestyles = StyleSheet.create({
  shadow: {
    shadowOffset: {width: 2, height: 2},
    shadowColor: 'black',
    shadowOpacity: 0.1,
    shadowRadius: 4,
    backgroundColor: 'white',
    display: 'flex',
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: 'silver',
    paddingVertical: 10,
    paddingHorizontal: 30,
    width: 250,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
});

export default SetttinsScreen;
