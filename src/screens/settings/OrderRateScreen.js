import React, { useEffect } from 'react';
import { SafeAreaView } from 'react-native';

import styles from '../../stylesheets/styles';
import LocalizationContext from '../../context/LocalizationContext';
import { WebView } from 'react-native-webview';
import mobileApi from '../../api/mobileApi';

const OrderRateScreen = ({ navigation, route }) => {
  const { t, locale } = React.useContext(LocalizationContext);
  const p = t('profile');

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      navigation.setOptions({
        title: t('orderRateTitle'),
        headerTruncatedBackTitle: '',
        headerBackTitle: '',
      });
      //console.log(route.params);
    });

    return unsubscribe;
  }, [navigation]);

  return (
    <SafeAreaView style={styles.container}>
      <WebView
        source={{
          uri:
            route?.params?.type === '1'
              ? `${mobileApi.defaults.baseURL}/orderRatePackage.php?orderid=${route.params.id}&memberid=${route.params.memberid}&token=${
                  route.params.token
                }&locale=${locale}&t=${new Date().getTime()}`
              : `${mobileApi.defaults.baseURL}/orderRateTable.php?orderid=${route.params.id}&memberid=${route.params.memberid}&token=${
                  route.params.token
                }&locale=${locale}&t=${new Date().getTime()}`,
        }}
        cacheEnabled={false}
        startInLoadingState={true}
        scalesPageToFit={true}
        style={{
          flex: 1,
        }}
      />
    </SafeAreaView>
  );
};

export default OrderRateScreen;
