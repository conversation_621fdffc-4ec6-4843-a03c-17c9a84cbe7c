import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import {Icon} from 'react-native-elements';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import {Context as UserContext} from '../../context/UserContext';
import LocalizationContext from '../../context/LocalizationContext';
import {useIsFocused} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import mobileApi from '../../api/mobileApi';

const SettingsPoint = ({navigation, route}) => {
  const isFocused = useIsFocused();
  const {state} = React.useContext(UserContext);
  const {t, locale} = React.useContext(LocalizationContext);
  const l = t('login');
  const [point, setPoint] = useState(0);
  const [results, setResults] = React.useState([]);

  const _readVal = async () => {
    await AsyncStorage.multiGet(['@userdata', '@token', '@memberid'])
      .then(r => {
        const data = JSON.parse(r[0][1]);
        setPoint(data.point);
        const req = {memberId: r[2][1], token: r[1][1]};
        _getResults(req);
      })
      .catch(err => {
        console.log(err);
      });
  };

  const _getResults = async request => {
    await mobileApi.post(`/${locale}/userPoint`, request).then(response => {
      if (!response.data.err) {
        setResults(response.data);
      }
      //console.log('res', response.data);
    });
  };

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
      headerRight: () => (
        <TouchableOpacity
          onPress={() => {
            navigation.navigate('SettingsPointInfo');
          }}
          style={{display: 'flex', flexDirection: 'row', alignItems: 'center'}}>
          <Icon
            name="info"
            type="feather"
            iconStyle={{color: 'white', paddingRight: 10}}
          />
        </TouchableOpacity>
      ),
    });
    _readVal();
    if (route?.params?.page === 'HomePage') {
      navigation.setOptions({
        headerTruncatedBackTitle: '',
        headerBackTitle: '',
        headerLeft: () => null,
      });
    }
  }, []);

  if (!results) {
    return (
      <View style={{margin: 40, alignItems: 'center'}}>
        <Text style={{fontSize: 16}}>{l.nopointinfo}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <SafeAreaView>
        <ScrollView>
          <View style={styles.topmargin}></View>

          <Image
            source={require('../../../assets/img/puangorsel.jpg')}
            style={{
              height: 350,
              width: 350,
              marginVertical: 20,
              alignSelf: 'center',
              resizeMode: 'contain',
            }}
          />

          <View style={styles.kutu}>
            <View style={styles.detay_kutu_sol}>
              <Text style={sc.PuanBilgi}>
                {' '}
                {l.totalpoint}:{' '}
                <Text style={[styles.ozelfont_bold, styles.text26, {color: '#78bae7'}]}>
                  {point}
                </Text>
              </Text>
            </View>
          </View>
          {results &&
            results.map((item, i) => {
              return (
                <View key={i}>
                  <View
                    style={[
                      styles.detay_kutu,
                      {marginHorizontal: 5, marginVertical: -6},
                    ]}>
                    <View
                      style={[
                        styles.detay_kutu_sol,
                        styles.bgrenk5,
                        {padding: 10, borderRadius: 2},
                      ]}>
                      <Text
                        style={
                          item.point > 0 ? sc.PuanKazanma : sc.PuanHarcama
                        }>{item.desc}
                      </Text>
                      <Text style={sc.ListeDesc}>{item.pointDate}</Text>
                    </View>
                    <View
                      style={[
                        styles.bgrenk4,
                        {
                          borderRadius: 2,
                          padding: 10,
                          justifyContent: 'flex-start',
                          marginLeft: 5,
                          width: 90,
                          alignItems: 'flex-end',
                        },
                      ]}>
                      <Text
                        style={
                          item.point > 0 ? sc.PuanKazanma : sc.PuanHarcama
                        }>
                        {item.point}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.ara}></View>
                </View>
              );
            })}
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default SettingsPoint;
