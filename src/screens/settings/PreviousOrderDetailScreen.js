import React, { useState, useEffect } from 'react';
import { View, Image, Text, Alert, SafeAreaView, TouchableOpacity, ScrollView,ImageBackground } from 'react-native';

import { Icon, ListItem, Input } from 'react-native-elements';
import Loading from '../../components/Loading';
import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';
import { Context as UserContext } from '../../context/UserContext';
import { useIsFocused } from '@react-navigation/native';

import AsyncStorage from '@react-native-async-storage/async-storage';

import { navigate } from '../../navigationRef';
import mobileApi from '../../api/mobileApi';

const SettingsPreviousOrderDetail = ({ navigation, route }) => {
  const isFocused = useIsFocused();
  const { handleBadge, item, total } = route.params;

  const { state, saveBasket, setBranch } = React.useContext(UserContext);

  const { t, locale } = React.useContext(LocalizationContext);
  const p = t('profile');
  const l = t('login');
  const [isLoading, setIsLoading] = React.useState(false);
  const buttonView = 0;

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
  }, [locale]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <ImageBackground source={require('../../../assets/img/back_cart.jpg')} resizeMode="cover" style={{ flex: 1, marginTop: 0 }}>

    <View>
      <SafeAreaView>
        <ScrollView>
          <View style={styles.topmargin}></View>
          <View style={styles.detay_kutu}>
            <View style={styles.detay_kutu_sol}>
              <Text style={[styles.text22, styles.kurumsalfont_regular, styles.black, styles.ls1]}>{item.restaurant}</Text>
            </View>
            <View style={styles.detay_kutu_sag}>
              <Text style={[sc.UrunAdi, { fontSize: 16 }]}>{total > 0 ? `+${total} ${t('currency')}` : ''}</Text>
            </View>
          </View>
          <View style={[styles.detay_kutu_dar,{borderBottomWidth:0.2,borderbottomcolor:'silver'}]}>
            <View style={styles.detay_kutu_sol}>
              <Text style={[sc.UrunDesc, styles.black]}>{item.orderDate}</Text>
            </View>
            <View style={styles.detay_kutu_sag}>
              <Text style={[sc.UrunDesc, styles.black]}>{item.type === '1' ? p.delivery : p.table}</Text>
            </View>
          </View>
          {item.item.map((subitem, j) => {
            return (
              <View style={styles.detay_kutu} key={j}>
                <View style={styles.detay_kutu_sol}>
                  <Text style={[sc.UrunDesc,{fontFamily: 'AndaleMono', color: '#000'}]}>
                    {subitem.number} x {subitem.productName}{' '}
                  </Text>
                  {subitem.option?.map((option, l) => {
                    return (
                      <View
                        key={j}
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                        }}
                      >
                        <Text style={{ fontStyle: 'italic', fontSize: 10 }}>{option.productName}</Text>

                        <Text style={{ fontStyle: 'italic', fontSize: 10 }}>
                          {option.price > 0 ? `+${option.price} ${t('currency')}` : ''}
                        </Text>
                      </View>
                    );
                  })}
                </View>
                <View style={styles.detay_kutu_sag}>
                  <Text style={sc.UrunDesc}>{subitem.price > 0 ? `+${subitem.price} ${t('currency')}` : ''}</Text>
                </View>
              </View>
            );
          })}

     
          {item.type === '1' ? (
            <View style={styles.detay_kutu}>
              <View style={styles.detay_kutu_sol}>
                   <Text style={[sc.UrunDesc,{fontFamily: 'AndaleMono', color: '#9f00aa'}]}>
                {t('login.addressDetail')} 
                  </Text>
                  <Text style={[sc.UrunDesc,{fontFamily: 'AndaleMono', color: '#000'}]}>
                  {item.address}
                  {'\n'}
                  {item.addressdetail}
                </Text>

              </View>
            </View>
          ) : null}
          {item.type === '1' ? (
            <View style={styles.detay_kutu}>
              <View style={styles.detay_kutu_sol}>
                <Text style={[sc.UrunDesc,{fontFamily: 'AndaleMono', color: '#9f00aa'}]}>
                  {t('payment.notes')}
                  </Text>
                  <Text style={[sc.UrunDesc,{fontFamily: 'AndaleMono', color: '#000'}]}>
                  {item.notes}
                </Text>
              </View>
            </View>
          ) : null}
        </ScrollView>
      </SafeAreaView>
    </View>
    </ImageBackground>
  );
};

export default SettingsPreviousOrderDetail;
