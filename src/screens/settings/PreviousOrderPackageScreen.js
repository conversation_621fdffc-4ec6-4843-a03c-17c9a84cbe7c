import React, {useState, useEffect, useContext} from 'react';
import {
  View,
  Text,
  Alert,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  ImageBackground,
  Modal,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';

import {Icon} from 'react-native-elements';
import Loading from '../../components/Loading';
import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';

import AsyncStorage from '@react-native-async-storage/async-storage';
import analytics from '@react-native-firebase/analytics';

import {navigate} from '../../navigationRef';
import mobileApi from '../../api/mobileApi';
import {getBadge} from '../../utils';
const {width, height} = Dimensions.get('screen');

const BUTTON_SIZE = 36;
const SettingsPreviousOrder = ({navigation, route, handleBadge}) => {
  const {state, saveBasket, setBranch} = useContext(UserContext);
  const [isLoading, setLoading] = useState(true);
  const [loginToken, setLoginToken] = useState();
  const [loginMemberId, setLoginMemberId] = useState();
  const {t, locale} = useContext(LocalizationContext);
  const [results, setResults] = useState([]);
  const [isSelectAddressModalVisible, setIsSelectAddressModalVisible] =
    useState(false);
  const [orderDetails, setOrderDetails] = useState([]);
  const buttonView = 0;
  const _readVal = async () => {
    await AsyncStorage.multiGet(['@token', '@memberid'])
      .then(r => {
        setLoginToken(r[0][1]);
        setLoginMemberId(r[1][1]);
        getOrderHistory(r[1][1], r[0][1]);
      })
      .catch(err => {
        console.log(err);
      });
  };

  const handleRepeatOrder = async orderDetails => {
    const basket = await AsyncStorage.getItem('@basket');
    const parsedBasket = JSON.parse(basket);
    if (!parsedBasket?.length) return confirmRepeatOrder(orderDetails);
    Alert.alert(t('payment').warning, t('alert').cleanBasket, [
      {
        text: t('alert').okBtn,
        onPress: () => confirmRepeatOrder(orderDetails),
      },
      {
        text: t('alert').cancelBtn,
        onPress: () => {},
      },
    ]);
  };

  const confirmRepeatOrder = async orderDetails => {
    try {
      setOrderDetails(orderDetails);
      await AsyncStorage.removeItem('@basket');
      setIsSelectAddressModalVisible(true);
    } catch (error) {
      console.log('_repeat order catch', error);
    }
  };

  const processBasketItems = products => {
    let r = [];
    let response = {err: false, data: []};
    products.map(async product => {
      let subProductActive = '1';
      let productActive = product?.active;
      let menuOption = [];
      let menuOptionCount = 0;
      let menuOptionTotal = 0;
      product?.option?.map(option => {
        if (option?.active === 0) {
          subProductActive = 0;
        }
        menuOption.push({
          itemcode: option?.optionid,
          itemname: option?.productName,
          itemprice: option?.restprice,
        });
        menuOptionCount++;
        menuOptionTotal = menuOptionTotal + option?.restprice;
      });

      if (productActive === '1' && subProductActive === '1') {
        const basketTime = new Date();
        const basketItem = {
          id: product?.productId,
          sos: products.sos ? products.sos : '',
          title: product?.productName,
          price: product?.restPrice,
          count: parseInt(product?.number),
          menu: menuOption,
          optionTotal: menuOptionTotal,
          promoCode: '',
          promoId: '',
          basketTime: basketTime,
          action: 'add',
          is_repeat: 1,
          category: product?.menuName,
          desc: product?.desc,
        };
        r.push(basketItem);
        response = {...response, data: r};
        await AsyncStorage.setItem('@basket', JSON.stringify(r));
      } else {
        return (response = {...response, err: true});
      }
    });
    if (response.err && response.data.length > 0) {
      Alert.alert(t('payment').warning, t('repeatOrder.activeItemsAdded'), [
        {
          text: t('payment').ok,
          onPress: navigateToBasket,
        },
      ]);
      return response;
    }
    if (response.data.length === 0) {
      Alert.alert(t('payment').warning, t('alert').noBasket, [
        {
          text: t('payment').ok,
        },
      ]);
    }
    _calculateTotalAmount(r);
    return response;
  };

  const _calculateTotalAmount = basket => {
    let totalAmount = 0;
    if (basket.length > 0) {
      basket.map(i => {
        totalAmount +=
          i.count * (parseFloat(i.optionTotal) + parseFloat(i.price));
      });
    } else {
      totalAmount = 0;
    }
    return (state.totalAmount = totalAmount);
  };

  const getOrderHistory = async (memberid, token) => {
    await mobileApi
      .get(
        `/${locale}/orderHistory?memberid=${memberid}&token=${token}&type=1&t=${new Date().getTime()}`,
      )
      .then(response => {
        setLoading(false);
        if (!response.data.err) {
          setResults(response.data);
        } else {
          setResults([]);
        }
      });
  };

  const handleAddressConfirm = async () => {
    const processBasketItemsResponse = processBasketItems(orderDetails.item);
    const isBadgeSet = await getBadge(handleBadge);
    if (isBadgeSet.err) {
      return Alert.alert(t('payment').warning, 'Something went wrong');
    }
    if (!processBasketItemsResponse.err) {
      Alert.alert(t('login').congratulations, t('repeatsuccess'), [
        {
          text: t('payment').ok,
          onPress: navigateToBasket,
        },
      ]);
    }
  };

  const navigateToBasket = () => {
    setIsSelectAddressModalVisible(false);
    return navigate('Basket', {
      screen: 'BasketMain',
    });
  };

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    _readVal();
    if (route?.params?.page === 'HomePage') {
      navigation.setOptions({
        headerTruncatedBackTitle: '',
        headerBackTitle: '',
        headerLeft: () => null,
      });
    }
  }, [locale]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <ImageBackground
      source={require('../../../assets/img/back_cart.jpg')}
      resizeMode="cover"
      style={{flex: 1, marginTop: 0}}>
      <SafeAreaView style={{flex: 1}}>
        <ScrollView style={{flex: 1}}>
          {results &&
            results.map((item, i) => {
              let total = 0;
              item.item.map(val => {
                total = parseInt(total) + parseInt(val.price);
              });

              return (
                <View style={[styles.blokkutu, styles.bgrenk5]} key={i}>
                  <View style={styles.detay_kutu}>
                    <View>
                      <Text
                        style={[
                          styles.text20,
                          styles.black,
                          styles.kurumsalfont_bold,
                        ]}>
                        {item.restaurant}
                      </Text>
                    </View>
                    <View style={styles.detay_kutu_sag}>
                      <Text style={[sc.UrunAdi, {fontSize: 16}]}>
                        {' '}
                        {total > 0 ? `+${total} ${t('currency')}` : ''}{' '}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.detay_kutu_dar}>
                    <View style={styles.detay_kutu_sol}>
                      <Text style={[sc.UrunDesc, styles.black]}>
                        {item.orderDate}
                      </Text>
                    </View>
                  </View>

                  {item.item.map((subitem, j) => {
                    return (
                      <View style={styles.detay_kutu_dar} key={j}>
                        <View style={styles.detay_kutu_sol}>
                          <Text style={sc.UrunDesc}>
                            {subitem.number} x {subitem.productName}{' '}
                          </Text>
                          {subitem.option?.map((option, l) => {
                            return (
                              <View
                                key={l}
                                style={{
                                  display: 'flex',
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                }}>
                                <Text
                                  style={{fontStyle: 'italic', fontSize: 10}}>
                                  {option.productName}
                                </Text>

                                <Text
                                  style={{fontStyle: 'italic', fontSize: 10}}>
                                  {option.price > 0
                                    ? `+${option.price} ${t('currency')}`
                                    : ''}
                                </Text>
                              </View>
                            );
                          })}
                        </View>
                        <View style={styles.detay_kutu_sag}>
                          <Text style={sc.UrunDesc}>
                            {subitem.price > 0
                              ? `+${subitem.price} ${t('currency')}`
                              : ''}
                          </Text>
                        </View>
                      </View>
                    );
                  })}
                  {buttonView === 1 && (
                    <View style={styles.detay_kutu}>
                      <View style={styles.detay_kutu_sol}>
                        <TouchableOpacity onPress={() => _login()}>
                          <View
                            style={[
                              sc.SecondaryButton_small,
                              {width: 190, height: 40},
                            ]}>
                            <Text style={sc.SecondaryButton_text}>
                              <Icon
                                name="motorcycle"
                                color="white"
                                type="font-awesome"
                                size={18}
                                style={{marginRight: 10, top: 4}}
                              />
                              {t('button.orderStatus')}
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>
                  )}
                  <View style={styles.detay_kutu}>
                    {item.rate === '0' && (
                      <View style={styles.detay_kutu_center}>
                        <TouchableOpacity
                          onPress={() =>
                            navigate('OrderRate', {
                              id: item.orderId,
                              token: loginToken,
                              memberid: loginMemberId,
                              type: item.type,
                            })
                          }>
                          <View style={sc.PrimaryButtonOutline_small}>
                            <Text style={sc.PrimaryButtonOutline_text_small}>
                              <Icon
                                name="star"
                                color="black"
                                type="font-awesome"
                                size={14}
                                style={{marginRight: 10, marginTop: 3}}
                              />
                              {t('button.review')}
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                  <View style={[styles.detay_kutu_center, styles.mb10]}>
                    {item.rate === '2' ? (
                      <View
                        style={[
                          styles.bgwhite,
                          {
                            alignSelf: 'center',
                            borderRadius: 10,
                            padding: 10,
                          },
                        ]}>
                        <Text
                          style={[
                            styles.text18,
                            {color: 'red', textAlign: 'center'},
                          ]}>
                          {t('button.rateonehour')}
                        </Text>
                      </View>
                    ) : (
                      <View style={styles.detay_kutu_center}>
                        <View style={styles.buton_block}>
                          <TouchableOpacity
                            style={[styles.btn_1]}
                            onPress={() =>
                              navigate('SettingsPreviousOrderDetail', {
                                item: item,
                                total,
                              })
                            }>
                            <Text
                              style={[
                                styles.text14,
                                styles.black,
                                styles.ozelfont_bold,
                                {letterSpacing: -1},
                                
                              ]}>
                              {/* <Icon
                                name="share-alt" color="gray" type="simple-line-icon" size={14} style={{ marginRight: 6, marginTop: 5 }}
                              /> */}
                              {t('button.detail')}
                            </Text>
                          </TouchableOpacity>
                          {item.type === '1' && (
                            <TouchableOpacity
                              onPress={() => handleRepeatOrder(item)}>
                              <View style={styles.btn_13}>
                                <Text
                                  style={[
                                    styles.text14,
                                    styles.black,
                                    {color: '#78BAE7',letterSpacing: -1},
                                    styles.ozelfont_bold,
                                  ]}>
                                  {t('button.reorder')}
                                </Text>
                              </View>
                            </TouchableOpacity>
                          )}
                        </View>
                      </View>
                    )}
                  </View>
                </View>
              );
            })}
        </ScrollView>
        {results.length === 0 && (
          <View
            style={{
              display: 'flex',
              alignItems: 'center',
            }}>
            <Text style={styles.black}>{t('historyNotFound')}</Text>
          </View>
        )}
        <SelectAddressModal
          visible={isSelectAddressModalVisible}
          setVisible={setIsSelectAddressModalVisible}
          onConfirmAddress={handleAddressConfirm}
          orderDetails={orderDetails}
        />
      </SafeAreaView>
    </ImageBackground>
  );
};

export default SettingsPreviousOrder;

const SelectAddressModal = ({
  visible,
  setVisible = () => {},
  onConfirmAddress = callback => {},
  orderDetails,
}) => {
  if (!visible) return;

  const {t, locale} = useContext(LocalizationContext);
  const {setBranch} = useContext(UserContext);

  const [isModalReady, setIsModalReady] = useState(false);
  const [addresses, setAddresses] = useState([]);
  const [results, setResults] = useState([]);
  const [selectedAddress, setSelectedAddress] = useState({
    branchId: '',
    branchName: '',
    addressId: '',
    price: '',
    deliveryPrice: '',
    service: '',
    districtName: '',
    cityName: '',
  });

  const l = t('payment');

  const getRestaurants = async () => {
    try {
      const response = await mobileApi.get(
        `/${locale}/getRestaurants?t=${new Date().getTime()}`,
      );
      setResults(response.data);
    } catch (error) {
      console.log('getRestaurants Error', error);
    }
  };

  const getBranchIndex = (arr, id) => {
    if (arr?.err) {
      return -1;
    } else {
      return arr?.findIndex(obj => parseInt(obj?.id) === parseInt(id));
    }
  };

  const checkBranch = id => {
    const index = getBranchIndex(results, id);
    if (index > -1) {
      const {
        orderStatus,
        status,
        orderAlert,
        orderAlertMsg,
        outwork,
        orderHour,
      } = results[index];

      if (outwork === 1) {
        return (
          <Text
            style={[styles.SubeAdres, styles.ozelfont_medium, {color: 'red'}]}>
            {l.outofservice1}
            {orderHour}
            {l.outofservice2}
          </Text>
        );
      } else if (orderAlert === '1') {
        return (
          <Text
            style={[styles.SubeAdres, styles.ozelfont_medium, {color: 'red'}]}>
            {orderAlertMsg}
          </Text>
        );
      } else if (status === 'Y' && orderStatus === 1) {
        return null;
      } else {
        return (
          <Text
            style={[styles.SubeAdres, styles.ozelfont_medium, {color: 'red'}]}>
            {l.gecici_pakete_kapali_txt}
          </Text>
        );
      }
    } else {
      return (
        <Text
          style={[styles.SubeAdres, styles.ozelfont_medium, {color: 'red'}]}>
          {l.gecici_pakete_kapali_txt}
        </Text>
      );
    }
  };

  const _setBranch = async selectedAddress => {
    const service = '1';
    await setBranch(
      selectedAddress.branchId,
      selectedAddress.branchName,
      service,
      selectedAddress.addressId,
      selectedAddress.price,
      selectedAddress.deliveryPrice,
      selectedAddress.service,
    )
      .then(() => {
        const fdatas = {
          restaurant_id: selectedAddress.branchId,
          city: selectedAddress.cityName,
          district: selectedAddress.district,
        };
        analytics().logEvent('select_restaurant', fdatas);
      })
      .catch(err => {
        console.log(err);
      });
  };

  const onCancel = () => {
    setVisible(false);
    setSelectedAddress({
      branchId: '',
      branchName: '',
      addressId: '',
      price: '',
      deliveryPrice: '',
      service: '',
      districtName: '',
      cityName: '',
    });
  };

  useEffect(() => {
    const getAddress = async () => {
      try {
        setIsModalReady(false);
        const userData = await AsyncStorage.getItem('@userdata');
        const parsedUserData = JSON.parse(userData);
        const filteredAddressesyBranchId = parsedUserData?.address?.filter(
          _address =>
            _address?.branchs?.some(
              branch => branch.branchId === orderDetails.restaurantId,
            ),
        );
        setAddresses(filteredAddressesyBranchId);
        await getRestaurants();
      } catch (error) {
        console.log('SelectAddressModal Error', error);
      } finally {
        setIsModalReady(true);
      }
    };
    getAddress();
  }, [visible]);

  return (
    <Modal transparent visible={visible}>
      <View style={stylesRepeat.backDrop}>
        <View style={stylesRepeat.centeredView}>
          <TouchableOpacity onPress={onCancel} style={stylesRepeat.backButton}>
            <Icon
              name="x"
              type="feather"
              iconStyle={{color: 'white', fontSize: 24}}
            />
          </TouchableOpacity>
          {!isModalReady ? (
            <View style={{alignItems: 'center', justifyContent: 'center'}}>
              <ActivityIndicator size={'large'} />
            </View>
          ) : (
            <>
              {addresses.length > 0 ? (
                <View style={{padding: 8}}>
                  <Text style={stylesRepeat.headerTxt}>
                    {t('repeatOrder.selectTargetAddress')}
                  </Text>
                </View>
              ) : (
                <View style={{padding: 8}}>
                  <Text style={stylesRepeat.headerTxt}>
                    {t('repeatOrder.noMatchedAddress', {
                      branchName: orderDetails.restaurant,
                    })}
                  </Text>
                </View>
              )}
              <ScrollView contentContainerStyle={stylesRepeat.scrollView}>
                {addresses.map((address, i) => {
                  let branchs = address.branchs ? address.branchs : [];
                  return (
                    <View key={i}>
                      <View
                        style={{
                          width: '100%',
                          backgroundColor: '#f2f2f2',
                          paddingHorizontal: 15,
                          paddingVertical: 7,
                          marginBottom: 0,
                          borderTopRightRadius: 15,
                          borderTopLeftRadius: 15,
                        }}>
                        <Text
                          style={[
                            styles.text16,
                            styles.ozelfont_bold,
                            styles.black,
                          ]}>
                          {address.label}
                        </Text>
                        <Text
                          style={[
                            styles.text18,
                            styles.ozelfont_regular,
                            styles.gray,
                          ]}>
                          {address.districtName} - {address.cityName}
                        </Text>
                      </View>
                      {branchs.map((branch, j) => {
                        return (
                          branch.branchId == orderDetails.restaurantId && (
                            <View
                              style={{
                                width: '100%',
                              }}
                              key={j}>
                              <TouchableOpacity
                                style={{
                                  backgroundColor:
                                    selectedAddress.addressId == address?.id &&
                                    selectedAddress.branchId == branch.branchId
                                      ? '#000'
                                      : '#000',
                                }}
                                onPress={() => {
                                  if (branch.bdurum === 1) {
                                    mobileApi
                                      .get(
                                        `/${locale}/getRestaurants?id=${
                                          branch.branchId
                                        }&t=${new Date().getTime()}`,
                                      )
                                      .then(resp => {
                                        if (resp) {
                                          if (resp.data[0].outwork === 1) {
                                            Alert.alert(
                                              t('payment').warning,
                                              `${l.outofservice1}${resp.data[0].orderHour}${l.outofservice2}`,
                                              [
                                                {
                                                  text: t('payment').ok,
                                                },
                                              ],
                                            );
                                          } else if (
                                            resp.data[0].orderStatus === 0
                                          ) {
                                            Alert.alert(
                                              t('payment').warning,
                                              t('payment')
                                                .gecici_pakete_kapali_txt,
                                              [
                                                {
                                                  text: t('payment').ok,
                                                },
                                              ],
                                            );
                                          } else {
                                            setSelectedAddress({
                                              branchId: branch.branchId,
                                              branchName: branch.branchName,
                                              addressId: address?.id,
                                              price: branch.price.toString(),
                                              deliveryPrice:
                                                branch.deliveryPrice.toString(),
                                              service: branch.service,
                                              districtName:
                                                address.districtName,
                                              cityName: address.cityName,
                                            });
                                          }
                                        } else {
                                          Alert.alert(
                                            t('payment').warning,
                                            t('payment')
                                              .gecici_pakete_kapali_txt,
                                            [
                                              {
                                                text: t('payment').ok,
                                              },
                                            ],
                                          );
                                        }
                                      });
                                  } else {
                                    Alert.alert(
                                      t('payment').warning,
                                      t('closedistrict'),
                                      [
                                        {
                                          text: t('payment').ok,
                                        },
                                      ],
                                    );
                                  }
                                }}>
                                <View style={styles.kutu_full}>
                                  <View style={styles.listeleme_orta}>
                                    <Text
                                      style={[
                                        styles.SubeAd,
                                        styles.kurumsalfont_regular,
                                        styles.white,
                                      ]}>
                                      {branch.branchName}
                                    </Text>
                                    <Text
                                      style={[
                                        styles.SubeAdres,
                                        styles.ozelfont_medium,
                                        styles.silver,
                                        {letterSpacing: -1}
                                      ]}>
                                      {branch.service} {l.dk} -{' '}
                                      {t('payment.minamountshort')} :{' '}
                                      {branch.price} {t('currency')}
                                    </Text>
                                    {checkBranch(branch.branchId)}
                                  </View>
                                  <View style={styles.listeleme_sag}>
                                    <Icon
                                      name="chevron-right"
                                      color="gray"
                                      size={16}
                                      type="font-awesome-5"
                                    />
                                  </View>
                                </View>
                              </TouchableOpacity>
                            </View>
                          )
                        );
                      })}
                    </View>
                  );
                })}
              </ScrollView>
              <View style={stylesRepeat.footerContainer}>
                <TouchableOpacity
                  onPress={onCancel}
                  style={stylesRepeat.footerBtn}>
                  <Text style={stylesRepeat.footerCancelBtnTxt}>
                    {t('repeatOrder.cancelRepeatOrder')}
                  </Text>
                </TouchableOpacity>
                {addresses.length > 0 && (
                  <TouchableOpacity
                    disabled={!selectedAddress.branchId}
                    onPress={() => {
                      _setBranch(selectedAddress).then(() => {
                        onConfirmAddress();
                      });
                    }}
                    style={[
                      stylesRepeat.footerBtn,
                      {
                        backgroundColor: '#000',
                        opacity: selectedAddress.branchId ? 1 : 0.4,
                      },
                    ]}>
                    <Text style={stylesRepeat.footerConfirmBtnTxt}>
                      {t('repeatOrder.confirmRepeatOrder')}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const stylesRepeat = StyleSheet.create({
  backDrop: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  centeredView: {
    width: width * 0.95,
    maxHeight: height * 0.68,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 8,
  },
  backButton: {
    position: 'absolute',
    height: BUTTON_SIZE,
    width: BUTTON_SIZE,
    right: 0,
    top: -(1.2 * BUTTON_SIZE),
    backgroundColor: 'rgba(0,0,0,.7)',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BUTTON_SIZE / 2,
    borderWidth: 2,
    borderColor: 'white',
  },
  headerTxt: {fontWeight: 'bold', textAlign: 'center', color: '#000',fontFamily: 'AndaleMono', letterSpacing: -1},
  scrollView: {flexGrow: 1, gap: 12},
  modalContentContainer: {
    flex: 1,
    paddingVertical: 10,
  },
  footerContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    gap: 8,
  },
  footerBtn: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#000',
    paddingVertical: 10,
    borderRadius: 2,
    alignItems: 'center',
    justifyContent: 'center',
    
  },
  footerConfirmBtnTxt: {
    color: '#78BAE7',
    fontWeight: '500',
    fontFamily: 'AndaleMono',
  },
  footerCancelBtnTxt: {
    color: '#000',
    fontWeight: '500',
    fontFamily: 'AndaleMono',
  },
});
