import {useContext} from 'react';
import {LogBox} from 'react-native';
import PreviousOrderPackageScreen from './PreviousOrderPackageScreen';
import PreviousOrderTableScreen from './PreviousOrderTableScreen';
import LocalizationContext from '../../context/LocalizationContext';
import {
  SegmentedTab,
  SegmentedTabWrapper,
} from '../../components/SegmentedTabs';

LogBox.ignoreAllLogs();
const PreviousOrderScreen = ({navigation, route}) => {
  const {handleBadge} = route.params;

  const {t} = useContext(LocalizationContext);

  return (
    <SegmentedTabWrapper>
      <SegmentedTab title={t('profile.delivery')}>
        <PreviousOrderPackageScreen
          handleBadge={handleBadge}
          navigation={navigation}
        />
      </SegmentedTab>
      <SegmentedTab title={t('profile.table')}>
        <PreviousOrderTableScreen
          handleBadge={handleBadge}
          navigation={navigation}
        />
      </SegmentedTab>
    </SegmentedTabWrapper>
  );
};

export default PreviousOrderScreen;
