import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  Alert,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  ImageBackground,
} from 'react-native';

import {Icon} from 'react-native-elements';
import Loading from '../../components/Loading';
import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';

import AsyncStorage from '@react-native-async-storage/async-storage';

import {navigate} from '../../navigationRef';
import mobileApi from '../../api/mobileApi';

const SettingsPreviousOrder = ({navigation, route, handleBadge}) => {
  const {state} = React.useContext(UserContext);
  const [isLoading, setLoading] = useState(true);
  const [loginToken, setLoginToken] = React.useState();
  const [loginMemberId, setLoginMemberId] = React.useState();
  const [branchId, setBranchId] = useState();

  const {t, locale} = React.useContext(LocalizationContext);
  const [results, setResults] = React.useState([]);
  const _readVal = async () => {
    await AsyncStorage.multiGet(['@token', '@memberid', '@branchId'])
      .then(r => {
        setLoginToken(r[0][1]);
        setLoginMemberId(r[1][1]);
        setBranchId(r[2][1]);
        _getResults(r[1][1], r[0][1]);
      })
      .catch(err => {
        console.log(err);
      });
  };

  const _repeatOrder = async item => {
    Alert.alert(t('payment').warning, t('alert').cleanBasket, [
      {
        text: t('alert').okBtn,
        onPress: () => {
          AsyncStorage.removeItem('@basket');

          repeatOrder(item).then(r => {
            if (r?.length > 0) {
              AsyncStorage.setItem('@basket', JSON.stringify(r));
              _getBadge().then(() => {
                Alert.alert(t('payment').warning, t('repeatsuccess'), [
                  {
                    text: t('payment').ok,
                    onPress: () => {
                      //  navigate('Basket', { screen: 'BasketMain' });
                    },
                  },
                ]);
              });
            } else {
              Alert.alert(t('payment').warning, t('alert').noBasket, [
                {
                  text: t('payment').ok,
                },
              ]);
            }
          });
        },
      },
      {
        text: t('alert').cancelBtn,
        onPress: () => {},
      },
    ]);
  };

  const repeatOrder = async item => {
    if (branchId) {
      const r = [];
      item.map(subitem => {
        let subProductActive = '1';
        let productActive = subitem.active;
        let menuOption = [];
        let menuOptionCount = 0;
        let menuOptionTotal = 0;
        subitem.option?.map(option => {
          if (option.active === 0) {
            subProductActive = 0;
          }
          menuOption.push({
            itemcode: option.optionid,
            itemname: option.productName,
            itemprice: option.restprice,
          });
          menuOptionCount++;
          menuOptionTotal = menuOptionTotal + option.restprice;
        });
        if (productActive === '1' && subProductActive === '1') {
          const basketTime = new Date();
          const basketItem = {
            id: subitem.productId,
            sos: item.sos ? item.sos : '',
            title: subitem.productName,
            price: subitem.restPrice,
            count: parseInt(subitem.number),
            menu: menuOption,
            optionTotal: menuOptionTotal,
            promoCode: '',
            promoId: '',
            basketTime: basketTime,
            promoProductName: '',
          };

          r.push(basketItem);
        } else {
        }
      });
      let totalAmount = 0;
      if (r.length > 0) {
        r.map(i => {
          totalAmount +=
            i.count * (parseFloat(i.optionTotal) + parseFloat(i.price));
        });
      } else {
        totalAmount = 0;
      }
      state.totalAmount = totalAmount;

      return r;
    } else {
      Alert.alert(t('payment').warning, t('nobranchrepeat'), [
        {
          text: t('payment').ok,
        },
      ]);
    }
  };

  const _getBadge = async () => {
    await AsyncStorage.getItem('@basket')
      .then(response => {
        const r = response ? JSON.parse(response) : [];
        handleBadge(r.length);
      })
      .catch(e => {
        handleBadge(0);
      });
  };

  const _getResults = async (memberid, token) => {
    await mobileApi
      .get(
        `/${locale}/orderHistory?memberid=${memberid}&token=${token}&type=2&t=${new Date().getTime()}`,
      )
      .then(response => {
        setLoading(false);
        if (!response.data.err) {
          setResults(response.data);
        } else {
          setResults([]);
        }
      });
  };

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    _readVal();
    if (route?.params?.page === 'HomePage') {
      navigation.setOptions({
        headerTruncatedBackTitle: '',
        headerBackTitle: '',
        headerLeft: () => null,
      });
    }
  }, [locale]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <ImageBackground
      source={require('../../../assets/img/back_cart.jpg')}
      resizeMode="cover"
      style={{flex: 1}}>
      <SafeAreaView style={{flex: 1}}>
        <ScrollView style={{flex: 1}}>
          {results &&
            results.map((item, i) => {
              let total = 0;
              item.item.map(val => {
                total = parseInt(total) + parseInt(val.price);
                val.option?.map(o => {
                  total = parseInt(total) + parseInt(o.price);
                });
              });

              return (
                <View style={styles.blokkutu} key={i}>
                  <View style={styles.detay_kutu}>
                    <View>
                      <Text
                        style={[
                          styles.text20,
                          styles.black,
                          styles.kurumsalfont_bold,
                        ]}>
                        {item.restaurant}
                      </Text>
                    </View>
                    <View style={styles.detay_kutu_sag}>
                      <Text style={[sc.UrunAdi, {fontSize: 16}]}>
                        {total > 0 ? `+${total} ${t('currency')}` : ''}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.detay_kutu_dar}>
                    <View style={styles.detay_kutu_sol}>
                      <Text style={[sc.UrunDesc, styles.black]}>
                        {item.orderDate}
                      </Text>
                    </View>
                  </View>

                  {item.item.map((subitem, j) => {
                    return (
                      <View style={styles.detay_kutu_dar} key={j}>
                        <View style={styles.detay_kutu_sol}>
                          <Text style={sc.UrunDesc}>
                            {subitem.number} x {subitem.productName}{' '}
                          </Text>
                          {subitem.option?.map((option, l) => {
                            return (
                              <View
                                key={l}
                                style={{
                                  display: 'flex',
                                  flexDirection: 'row',
                                  justifyContent: 'space-between',
                                }}>
                                <Text
                                  style={{fontStyle: 'italic', fontSize: 10}}>
                                  {option.productName}
                                </Text>

                                <Text
                                  style={{fontStyle: 'italic', fontSize: 10}}>
                                  {option.price > 0
                                    ? `+${option.price} ${t('currency')}`
                                    : ''}
                                </Text>
                              </View>
                            );
                          })}
                        </View>
                        <View style={styles.detay_kutu_sag}>
                          <Text style={sc.UrunDesc}>
                            {subitem.price > 0
                              ? `+${subitem.price} ${t('currency')}`
                              : ''}
                          </Text>
                        </View>
                      </View>
                    );
                  })}
                  <View style={styles.detay_kutu}>
                    {item.rate === '0' && (
                      <View style={styles.detay_kutu_center}>
                        <TouchableOpacity
                          onPress={() =>
                            navigate('OrderRate', {
                              id: item.orderId,
                              token: loginToken,
                              memberid: loginMemberId,
                              type: item.type,
                            })
                          }>
                          <View style={sc.PrimaryButtonOutline_small}>
                            <Text style={sc.PrimaryButtonOutline_text_small}>
                              <Icon
                                name="star"
                                color="black"
                                type="feather"
                                size={12}
                                style={{marginRight: 10, marginTop: 5}}
                              />
                              {t('button.review')}
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                  <View style={styles.detay_kutu}>
                    {item.rate === '2' ? (
                      <View style={[{alignSelf: 'center'}]}>
                        <Text
                          style={[
                            styles.text16,
                            {color: 'red', textAlign: 'center'},
                          ]}>
                          {t('button.rateonehour')}
                        </Text>
                      </View>
                    ) : (
                      <View style={styles.detay_kutu_center}>
                        <View style={[styles.buton_block]}>
                          {item.type === '1' && (
                            <TouchableOpacity
                              onPress={() => _repeatOrder(item.item)}>
                              <View style={styles.btn_13}>
                                <Text
                                  style={[
                                    styles.text14,
                                    styles.white,
                                    styles.ozelfont_medium,
                                  ]}>
                                  <Icon
                                    name="reload"
                                    color="white"
                                    type="simple-line-icon"
                                    size={14}
                                    style={{marginRight: 10, marginTop: 5}}
                                  />
                                  {t('button.reorder')}
                                </Text>
                              </View>
                            </TouchableOpacity>
                          )}
                        </View>
                      </View>
                    )}
                  </View>
                </View>
              );
            })}
        </ScrollView>
        {results.length === 0 && (
          <View
            style={{
              display: 'flex',
              alignItems: 'center',
            }}>
            <Text style={styles.black}>{t('historyNotFound')}</Text>
          </View>
        )}
      </SafeAreaView>
    </ImageBackground>
  );
};

export default SettingsPreviousOrder;
