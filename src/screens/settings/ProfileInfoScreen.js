import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Pressable,
} from 'react-native';
import {CheckBox} from 'react-native-elements';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import LocalizationContext from '../../context/LocalizationContext';
import {Context as UserContext} from '../../context/UserContext';
import {useIsFocused} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {navigate} from '../../navigationRef';
import mobileApi from '../../api/mobileApi';
import VersionNumber from 'react-native-version-number';
import {PasswordChecklist} from '../login/components';

const ProfileInfo = ({navigation}) => {
  const isFocused = useIsFocused();
  const {login, usersave, logout, state} = React.useContext(UserContext);
  const {t, locale} = React.useContext(LocalizationContext);

  const [name, setName] = useState('');
  const [surname, setSurname] = useState('');
  const [email, setEmail] = useState('');
  const [removePassword, setRemovePassword] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [birthday, setBirthday] = useState('');
  const [sms, setSms] = useState(false);
  const [bulten, setBulten] = useState(false);
  const [loginEmail, setLoginEmail] = React.useState();
  const [loginPassword, setLoginPassword] = React.useState();
  const [loginToken, setLoginToken] = React.useState();
  const [loginMemberId, setLoginMemberId] = React.useState();
  const [modalVisible, setModalVisible] = useState(false);
  const [isPasswordInputFocused, setIsPasswordInputFocused] = useState(false);

  const l = t('login');

  const _removeUser = async () => {
    const request = {
      token: loginToken,
      memberId: loginMemberId,
      password: removePassword,
    };
    await mobileApi.post(`/${locale}/removeUser`, request).then(response => {
      setModalVisible(false);
      if (response.data.err === 1) {
        Alert.alert(t('payment').warning, response.data.msg, [
          {
            text: t('payment').ok,
          },
        ]);
      } else {
        logout().then(() => {
          navigate('SettingsMain');
        });
      }
    });
  };

  const _readVal = async () => {
    await AsyncStorage.multiGet([
      '@userdata',
      '@email',
      '@password',
      '@token',
      '@memberid',
    ])
      .then(r => {
        const data = JSON.parse(r[0][1]);
        setName(data.firstname ? data.firstname : '');
        setSurname(data.lastname ? data.lastname : '');
        setEmail(data.email ? data.email : '');
        setPhone(data.phone ? data.phone : '');
        setPassword(data.password ? data.password : '');
        setBirthday(data.birthday ? data.birthday : '');

        data.emsg === '0' ? setBulten('') : setBulten(1);
        data.sms === '0' ? setSms('') : setSms(1);

        setLoginEmail(r[1][1]);
        setLoginPassword(r[2][1]);
        setLoginToken(r[3][1]);
        setLoginMemberId(r[4][1]);
      })
      .catch(err => {
        console.log(err);
      });
  };

  const _userUpdate = () => {
    const data = {
      firstname: name,
      lastname: surname,
      email,
      phone,
      sms,
      emsg: bulten,
      password,
      token: loginToken,
      memberId: loginMemberId,
      sms_activation: 1,
      action: 'update',
      locale,
    };

    usersave({
      data: data,
      alert: t('alert'),
    }).then(resp => {
      if (resp.err === '') {
        const data = {
          email: loginEmail,
          password: loginPassword,
          locale,
          version: VersionNumber.appVersion,
        };
        login({data: data, alert: t('alert')})
          .then(() => {
            Alert.alert(t('login').congratulations, t('login').updateProfiles, [
              {
                text: t('payment').ok,
                onPress: () => {
                  navigate('Settings', {screen: 'SettingsMain'});
                },
              },
            ]);
          })
          .catch(err => {
            Alert.alert(t('payment').warning, t('login').error, [
              {
                text: t('payment').ok,
              },
            ]);
          });
      }
    });
  };

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    _readVal();
  }, [isFocused]);
  return (
    <View style={styles.container}>
      <SafeAreaView>
        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => {
            setModalVisible(!modalVisible);
          }}>
          <View style={pagestyles.centeredView}>
            <View style={pagestyles.modalView}>
              <Text
                style={[
                  styles.text16,
                  {marginVertical: 5, textAlign: 'left', width: '100%', },
                ]}>
                {t('login.removeText1')} {''} {t('login.removeText2')}
              </Text>
              <Text style={[styles.text18, {marginVertical: 5}]}>
                {t('login.removeConfirm')}{' '}
              </Text>

              <View style={styles.kutu_full}>
                <TextInput
                  value={removePassword}
                  onChangeText={value => setRemovePassword(value)}
                  style={styles.InputStyle}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>

              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  width: '90%',
                  justifyContent: 'space-between',
                }}>
                <Pressable onPress={() => _removeUser()}>
                  <View style={[sc.PrimaryButton, {width: 150}]}>
                    <Text style={sc.PrimaryButton_text}>
                      {t('login.removeBtn1')}
                    </Text>
                  </View>
                </Pressable>
                <Pressable onPress={() => setModalVisible(!modalVisible)}>
                  <View style={[sc.ThirdButton, {width: 100}]}>
                    <Text style={sc.ThirdButtonButton_text}>
                      {t('login.removeBtn2')}
                    </Text>
                  </View>
                </Pressable>
              </View>
            </View>
          </View>
        </Modal>
        <ScrollView>
          <View style={styles.topmargin}></View>

          {/* <View style={styles.labelline}>
            <Text style={styles.labelStyle}>{l.firstname}</Text>
          </View> */}

          <View style={styles.kutu_full}>
            <TextInput
              value={name}
              placeholder={l.firstname}
              onChangeText={value => setName(value)}
              style={styles.InputStyle}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.kutu_full}>
            <TextInput
              value={surname}
              placeholder={l.lastname}
              onChangeText={value => setSurname(value)}
              style={styles.InputStyle}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.kutu_full}>
            <TextInput
              value={email}
              placeholder={l.email}
              onChangeText={value => setEmail(value)}
              style={styles.InputStyle}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.kutu_full}>
            <TextInput
              value={phone}
              placeholder={l.phone}
              onChangeText={value => setPhone(value)}
              style={[
                styles.InputStyle,
                {borderColor: '#f1f1f1', color: 'gray'},
              ]}
              autoCapitalize="none"
              autoCorrect={false}
              keyboardType={'numeric'}
              editable={false}
            />
          </View>
          {birthday ? (
            <View>
              <View style={styles.kutu_full}>
                <TextInput
                  value={birthday}
                  placeholder={l.birthday}
                  style={[styles.InputStyle]}
                  autoCapitalize="none"
                  autoCorrect={false}
                  keyboardType={'numeric'}
                  editable={false}
                />
              </View>
            </View>
          ) : null}

          <View style={styles.kutu_full}>
            <TextInput
              onFocus={() => setIsPasswordInputFocused(true)}
              onBlur={() => setIsPasswordInputFocused(false)}
              value={password}
              placeholder={l.password}
              onChangeText={value => setPassword(value)}
              style={[styles.InputStyle]}
              secureTextEntry={true}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          {isPasswordInputFocused && (
            <View style={styles.kutu_full}>
              <PasswordChecklist />
            </View>
          )}

          {state.err && state.errormsg ? (
            <View style={{width: '100%', margin: 20}}>
              <Text style={{color: 'red'}}>{state?.errormsg}</Text>
            </View>
          ) : null}

          <View style={styles.kutu_full}>
            <Text style={[sc.UrunDesc, {color: '#9f00aa'}]}>{l.campaign}</Text>
          </View>

          <View>
            <CheckBox
              left
              title={l.emsgAccept}
              checkedIcon="check"
              uncheckedIcon="circle-o"
              checkedColor="green"
              textStyle={{fontWeight: 'normal', fontFamily: 'AndaleMono', letterSpacing: -1}}
              checked={bulten}
              onPress={bulten ? () => setBulten(false) : () => setBulten(true)}
            />
          </View>

          <View>
            <CheckBox
              left
              title={l.smsAccept}
              checkedIcon="check"
              uncheckedIcon="circle-o"
              textStyle={{fontWeight: 'normal', fontFamily: 'AndaleMono', letterSpacing: -1}}
              checkedColor="green"
              checked={sms}
              onPress={sms ? () => setSms(false) : () => setSms(true)}
            />
          </View>

          <View style={{marginVertical: 30}}>
            <TouchableOpacity onPress={() => _userUpdate()}>
              <View style={sc.PrimaryButton}>
                <Text style={sc.PrimaryButton_text}>{t('button.update')}</Text>
              </View>
            </TouchableOpacity>
          </View>
          <View
            style={{
              margin: 0,
              width: '100%',
              alignItems: 'center',
              marginBottom: 20,
            }}>
            <TouchableOpacity
              onPress={() => {
                setRemovePassword('');
                setModalVisible(true);
              }}>
              <Text
                style={[
                  styles.red,
                  styles.bold,
                  styles.text16,
                  styles.ozelfont_medium,
                  styles.ls1,
                  {marginBottom: 30},
                ]}>
                {l.remove}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

const pagestyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#00000099',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '100%',
  },
  button: {
    borderRadius: 10,
    padding: 10,
    elevation: 2,
  },
  buttonOpen: {
    backgroundColor: '#F194FF',
  },
  buttonClose: {
    backgroundColor: '#2196F3',
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontFamily: 'IBMPlexSans-Regular',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
});

export default ProfileInfo;
