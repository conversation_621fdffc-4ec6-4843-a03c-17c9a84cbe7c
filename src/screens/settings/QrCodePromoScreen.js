import React, {useState, useEffect, useCallback, useContext} from 'react';
import {
  View,
  Image,
  Text,
  TextInput,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Pressable,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import LocalizationContext from '../../context/LocalizationContext';
import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import mobileApi from '../../api/mobileApi';
import {Context as UserContext} from '../../context/UserContext';
import {Icon} from 'react-native-elements';
import VersionNumber from 'react-native-version-number';
import {parseDate} from '../../utils';
import {FlatList} from 'react-native-gesture-handler';
import {PromoCard} from './promo/components';
import Loading from '../../components/Loading';

const QrCodePromo = ({navigation, route}) => {
  const {t, locale} = useContext(LocalizationContext);
  const {token, memberid: memberId} = useContext(UserContext)?.state;

  const [isLoading, setIsLoading] = useState(false);
  const [allPromoCodes, setAllPromoCodes] = useState([]);
  const [tablePromoId, setTablePromoId] = useState();
  const [tablePromoCode, setTablePromoCode] = useState();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const _readVal = async () => {
    try {
      const storedValues = await AsyncStorage.multiGet([
        '@tablePromoId',
        '@tablePromoCode',
      ]);
      const tablePromoId = storedValues[0][1];
      const tablePromoCode = storedValues[1][1];
      setTablePromoId(tablePromoId);
      setTablePromoCode(tablePromoCode);
    } catch (error) {
      console.log(err);
    }
  };

  const fetchAllPromoCodes = useCallback(async () => {
    setIsLoading(true);
    const [promoCodes, userPromoCodes] = await Promise.all([
      fetchPromoCodes(),
      fetchUserPromoCodes(),
    ]);
    const mergedPromoCodes = [...promoCodes, ...userPromoCodes];
    const sortedPromoCodes = mergedPromoCodes?.sort((a, b) => {
      const dateA = parseDate(a.expireDate);
      const dateB = parseDate(b.expireDate);

      return dateA - dateB;
    });
    setAllPromoCodes(sortedPromoCodes);
    setIsLoading(false);
  }, []);

  const fetchPromoCodes = async () => {
    try {
      const getPromosPayload = {
        memberId: memberId,
        token: token,
        orderStatus: '1',
        restaurant: route?.params?.restaurant,
      };
      const getPromosResponse = await mobileApi.post(
        `/${locale}/getPromosV2`,
        getPromosPayload,
      );

      const promoCodes = getPromosResponse?.data?.promos;
      if (!Array.isArray(promoCodes) || !promoCodes.length) {
        return [];
      }
      const convertedPromoCodes = promoCodes?.map(promoCode => ({
        code: promoCode?.code,
        expireDate: promoCode?.sgt,
        minAmount: promoCode?.minprice,
        price: promoCode?.price,
        promoProduct: promoCode?.product,
        promoType: promoCode?.promotype,
        useCode: promoCode?.usePromo,
        useType: promoCode?.useType,
        shortdesc: promoCode?.shortdesc,
      }));
      return convertedPromoCodes;
    } catch (error) {
      console.log('catch getpromosv2:', error);
      return [];
    }
  };
  const fetchUserPromoCodes = async () => {
    try {
      const userPromoPayload = {
        token: token,
        memberId: memberId,
      };
      const userPromoResponse = await mobileApi.post(
        `/${locale}/getUserPromoV2`,
        userPromoPayload,
      );
      const userPromos = userPromoResponse?.data?.map(userPromo => ({
        ...userPromo,
        isSaved: true,
      }));
      if (!Array.isArray(userPromos) || !userPromos.length) {
        return [];
      }
      return userPromos;
    } catch (error) {
      console.log('catch user promos :', error);
      return [];
    }
  };

  const handlePromoUse = useCallback(promo => {
    handleCheckPromo(promo.code);
  }, []);

  const handleCheckPromo = async code => {
    if (tablePromoCode && tablePromoCode === code) {
      Alert.alert(t('payment').warning, t('alert').promoUse, [
        {
          text: t('payment').ok,
        },
      ]);
      return;
    }
    if (tablePromoId) {
      return Alert.alert(t('payment').warning, t('alert').promoUse2, [
        {
          text: t('payment').ok,
        },
      ]);
    }

    const checkPromoPayload = {
      code,
      token,
      memberId,
      locale,
      restaurant: route?.params?.qrBranch
        ? route?.params?.qrBranch
        : route?.params?.restaurant,
      eadisyon: route?.params?.qrBranch ? 0 : 1,
      amount: route?.params?.amount ? route?.params?.amount : 0,
      version: VersionNumber.appVersion,
    };
    try {
      const checkTablePromoResponse = await mobileApi.post(
        `/${locale}/checkTablePromo`,
        checkPromoPayload,
      );
      if (checkTablePromoResponse?.data?.err === 1) {
        Alert.alert(t('payment').warning, checkTablePromoResponse?.data?.msg, [
          {
            text: t('payment').ok,
          },
        ]);
      } else {
        await AsyncStorage.multiSet([
          ['@tablePromoId', checkTablePromoResponse?.data?.promoid],
          ['@tablePromoCode', code],
          ['@tablePromoPrice', checkTablePromoResponse?.data?.price],
        ]);
        if (checkTablePromoResponse?.data?.productName) {
          await AsyncStorage.multiSet([
            [
              '@tablePromoProductName',
              checkTablePromoResponse?.data?.productName,
            ],
            [
              '@tablePromoProductId',
              checkTablePromoResponse?.data?.productID?.toString(),
            ],
          ]);
        } else {
          await AsyncStorage.multiRemove([
            '@tablePromoProductName',
            '@tablePromoProductId',
          ]);
        }

        navigation.goBack();
      }
    } catch (error) {
      console.log('checkTablePromo error:', error);
    }
  };

  const renderPromoItem = useCallback(
    ({item: promo}) => {
      return (
        <PromoCard
          orderMode="qr"
          promo={promo}
          fetchUserPromos={fetchAllPromoCodes}
          allowedAction="use"
          onPromoUse={handlePromoUse}
        />
      );
    },
    [allPromoCodes],
  );

  useEffect(() => {
    navigation.setOptions({
      title: t('promoCode'),
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    _readVal();
    fetchAllPromoCodes();
  }, []);

  if (isLoading) return <Loading />;

  return (
    <SafeAreaView style={styles.container}>
      <AddPromoModal
        isModalVisible={isModalVisible}
        setIsModalVisible={setIsModalVisible}
        onCheckPromo={handleCheckPromo}
      />
      <AddPromoButton onPress={() => setIsModalVisible(true)} />
      <FlatList data={allPromoCodes} renderItem={renderPromoItem} />
    </SafeAreaView>
  );
};
const AddPromoModal = ({
  isModalVisible = false,
  setIsModalVisible = () => {},
  onCheckPromo = () => {},
}) => {
  const {t} = React.useContext(LocalizationContext);
  const [code, setCode] = useState('');

  const handleCloseModal = () => {
    setCode('');
    setIsModalVisible(false);
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isModalVisible}
      onRequestClose={handleCloseModal}>
      <View style={pagestyles.centeredView}>
        <View style={pagestyles.modalView}>
          <Pressable
            onPress={handleCloseModal}
            style={[
              sc.PrimaryButton_small,
              {width: undefined, right: 10, position: 'absolute'},
            ]}>
            <Text
              style={[
                sc.PrimaryButton_text_small,
                {lineHeight: 25, paddingHorizontal: 4},
              ]}>
              {t('button.close')}
            </Text>
          </Pressable>
          <View style={[styles.labelline, styles.mt15]}>
            <Text style={[styles.labelStyle, styles.text18]}>
              {t('payment').promocode}
            </Text>
          </View>
          <View style={styles.kutu_full}>
            <TextInput
              value={code}
              onChangeText={value => setCode(value)}
              style={[styles.InputStyle, {marginVertical: 20}]}
              autoCapitalize="characters"
              autoCorrect={false}
            />
          </View>
          <TouchableOpacity onPress={() => onCheckPromo(code)}>
            <View style={[styles.button100, styles.bgrenk1]}>
              <Text style={sc.ThirdButtonButton_text}>{t('button.check')}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const AddPromoButton = ({onPress = () => {}}) => {
  const {t} = React.useContext(LocalizationContext);

  return (
    <TouchableOpacity
      style={[styles.topmargin, pagestyles.shadow]}
      onPress={onPress}>
      <Icon
        name="plus"
        type="feather"
        iconStyle={{color: 'black', fontSize: 16, paddingHorizontal: 5}}
      />
      <Text style={[styles.text16, styles.black]}>{t('offeruse')}</Text>
    </TouchableOpacity>
  );
};

const pagestyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#00000090',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 30,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    borderRadius: 20,
    padding: 10,
    elevation: 2,
  },
  buttonOpen: {
    backgroundColor: '#F194FF',
  },
  buttonClose: {
    backgroundColor: '#2196F3',
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
  shadow: {
    shadowOffset: {width: 3, height: 3},
    shadowColor: 'black',
    shadowOpacity: 0.4,
    shadowRadius: 8,
    backgroundColor: 'white',
    display: 'flex',
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: 'silver',
    paddingVertical: 10,
    paddingHorizontal: 30,
    width: 250,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default QrCodePromo;
