import React, {useContext, useState, useEffect, useRef} from 'react';
import {SafeAreaView, Alert, Platform} from 'react-native';

import styles from '../../stylesheets/styles';
import LocalizationContext from '../../context/LocalizationContext';
import {useIsFocused} from '@react-navigation/native';
import {navigate} from '../../navigationRef';
import mobileApi from '../../api/mobileApi';
import {Camera, CameraType} from 'react-native-camera-kit';
import Loading from '../../components/Loading';
import {check, PERMISSIONS, openSettings} from 'react-native-permissions';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Context as UserContext} from '../../context/UserContext';
import VersionNumber from 'react-native-version-number';

const QrCodeScreen = ({navigation, route}) => {
  const cameraRef = useRef();
  const isFocused = useIsFocused();
  const {t, locale} = useContext(LocalizationContext);
  const [viewCamera, setViewCamera] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const {login} = useContext(UserContext);

  const _readVal = async () => {
    await AsyncStorage.multiGet(['@email', '@password']).then(response => {
      const data = {
        email: response[0][1],
        password: response[1][1],
        locale,
        version: VersionNumber.appVersion,
      };
      if (response[1][1] && response[0][1]) {
        login({data: data, alert: t('alert')})
          .then(resp => {
            if (resp.err === 1) {
              navigate('LoginStack', {Screen: 'Login'});
            } else {
              setLoading(false);
            }
          })
          .catch(err => {
            //console.log(err);
          });
      } else {
        setLoading(false);
      }
    });
  };

  const readQr = async code => {
    setLoading(true);
    setViewCamera(false);

    if (code) {
      await AsyncStorage.multiGet(['@token', '@memberid'])
        .then(r => {
          let rand = (Math.random() + 1).toString(36).substring(2, 12);
          const reqdata = {
            code,
            memberId: r[1][1],
            token: r[0][1],
          };

          const req = JSON.stringify(reqdata);
          const encText = rand + Base64.encode(req);
          mobileApi.post(`/${locale}/getTableOrder`, encText).then(r => {
            if (r?.data && r?.data?.err === 0) {
              navigate('QrCodeSuccessScreen', {
                item: r?.data?.order,
                product: r?.data?.urunlist,
                restaurant: r.data?.id,
                branchName: r.data?.name,
                qrBranch: r.data?.ropid,
                ropid: r.data?.newropid,
                qrOrderId: r.data?.orderid,
                amount: r.data?.order?.AdisyonTutari,
              });
            } else {
              Alert.alert(t('payment').warning, r.data.msg, [
                {
                  text: t('payment').ok,
                  onPress: () => {
                    navigate('Settings', {screen: 'SettingsMain'});
                  },
                },
              ]);
            }
          });
        })
        .catch(err => {
          //console.log(err);
        });
    }
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      if (Platform.OS === 'ios') {
        check(PERMISSIONS.IOS.CAMERA)
          .then(result => {
            if (result == 'blocked') {
              setViewCamera(false);
              setLoading(true);

              Alert.alert(t('payment').warning, t('cameraStatus'), [
                {
                  text: t('alert').okBtn,
                  onPress: () => {
                    openSettings().catch(() =>
                      console.warn('cannot open settings'),
                    );
                  },
                },
                {
                  text: t('alert').cancelBtn,
                  onPress: () => {
                    navigate('Settings', {screen: 'SettingsMain'});
                  },
                },
              ]);
            } else {
              setViewCamera(true);
              setLoading(false);
            }
          })
          .catch(error => {
            console.log(error);
            // …
          });
      }

      if (Platform.OS === 'android') {
        check(PERMISSIONS.ANDROID.CAMERA)
          .then(result => {
            if (result == 'blocked') {
              setViewCamera(false);
              setLoading(true);
              Alert.alert(t('payment').warning, t('cameraStatus'), [
                {
                  text: t('alert').okBtn,
                  onPress: () => {
                    openSettings().catch(() =>
                      console.warn('cannot open settings'),
                    );
                  },
                },
                {
                  text: t('alert').cancelBtn,
                  onPress: () => {
                    navigate('SettingsMain');
                  },
                },
              ]);
            } else {
              setViewCamera(true);
              setLoading(false);
            }
          })
          .catch(error => {
            console.log(error);
            // …
          });
      }
    });

    return unsubscribe;
  }, [navigation]);

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    //readQr('https://www.ropapi.com/V6/EA76001559630001750595');
    //readQr('0001630010571020231021124109');
    _readVal();
    if (route?.params?.page === 'HomePage') {
      navigation.setOptions({
        headerTruncatedBackTitle: '',
        headerBackTitle: '',
        headerLeft: () => null,
      });
    }
  }, [isFocused]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <SafeAreaView style={styles.container}>
      {viewCamera ? (
        <Camera
          style={{flex: 1}}
          ref={cameraRef}
          showFrame
          scanBarcode
          barcodeFrameSize={{
            height: 300,
            width: 300,
          }}
          onReadCode={event => {
            readQr(event.nativeEvent.codeStringValue);
          }}
        />
      ) : (
        <Loading />
      )}
    </SafeAreaView>
  );
};

var Base64 = {
  _keyStr: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
  encode: function (e) {
    var t = '';
    var n, r, i, s, o, u, a;
    var f = 0;
    e = Base64._utf8_encode(e);
    while (f < e.length) {
      n = e.charCodeAt(f++);
      r = e.charCodeAt(f++);
      i = e.charCodeAt(f++);
      s = n >> 2;
      o = ((n & 3) << 4) | (r >> 4);
      u = ((r & 15) << 2) | (i >> 6);
      a = i & 63;
      if (isNaN(r)) {
        u = a = 64;
      } else if (isNaN(i)) {
        a = 64;
      }
      t =
        t +
        this._keyStr.charAt(s) +
        this._keyStr.charAt(o) +
        this._keyStr.charAt(u) +
        this._keyStr.charAt(a);
    }
    return t;
  },
  decode: function (e) {
    var t = '';
    var n, r, i;
    var s, o, u, a;
    var f = 0;
    e = e.replace(/[^A-Za-z0-9\+\/\=]/g, '');
    while (f < e.length) {
      s = this._keyStr.indexOf(e.charAt(f++));
      o = this._keyStr.indexOf(e.charAt(f++));
      u = this._keyStr.indexOf(e.charAt(f++));
      a = this._keyStr.indexOf(e.charAt(f++));
      n = (s << 2) | (o >> 4);
      r = ((o & 15) << 4) | (u >> 2);
      i = ((u & 3) << 6) | a;
      t = t + String.fromCharCode(n);
      if (u != 64) {
        t = t + String.fromCharCode(r);
      }
      if (a != 64) {
        t = t + String.fromCharCode(i);
      }
    }
    t = Base64._utf8_decode(t);
    return t;
  },
  _utf8_encode: function (e) {
    e = e.replace(/\r\n/g, '\n');
    var t = '';
    for (var n = 0; n < e.length; n++) {
      var r = e.charCodeAt(n);
      if (r < 128) {
        t += String.fromCharCode(r);
      } else if (r > 127 && r < 2048) {
        t += String.fromCharCode((r >> 6) | 192);
        t += String.fromCharCode((r & 63) | 128);
      } else {
        t += String.fromCharCode((r >> 12) | 224);
        t += String.fromCharCode(((r >> 6) & 63) | 128);
        t += String.fromCharCode((r & 63) | 128);
      }
    }
    return t;
  },
  _utf8_decode: function (e) {
    var t = '';
    var n = 0;
    var r = (c1 = c2 = 0);
    while (n < e.length) {
      r = e.charCodeAt(n);
      if (r < 128) {
        t += String.fromCharCode(r);
        n++;
      } else if (r > 191 && r < 224) {
        c2 = e.charCodeAt(n + 1);
        t += String.fromCharCode(((r & 31) << 6) | (c2 & 63));
        n += 2;
      } else {
        c2 = e.charCodeAt(n + 1);
        c3 = e.charCodeAt(n + 2);
        t += String.fromCharCode(
          ((r & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63),
        );
        n += 3;
      }
    }
    return t;
  },
};

export default QrCodeScreen;
