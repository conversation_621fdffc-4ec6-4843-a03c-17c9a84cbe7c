import React, {useState, useEffect, useContext} from 'react';
import {
  SafeAreaView,
  TouchableOpacity,
  View,
  Text,
  Modal,
  StyleSheet,
  TextInput,
  Pressable,
  ScrollView,
  Alert,
} from 'react-native';

import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import {CommonActions, useIsFocused} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {Icon} from 'react-native-elements';

import LocalizationContext from '../../context/LocalizationContext';
import {navigate} from '../../navigationRef';
import mobileApi from '../../api/mobileApi';
import Loading from '../../components/Loading';
import analytics from '@react-native-firebase/analytics';

const QrCodeSuccessScreen = ({navigation, route}) => {
  const isFocused = useIsFocused();

  const {t, locale} = useContext(LocalizationContext);
  const {
    item,
    restaurant,
    qrBranch,
    qrOrderId,
    branchName,
    product,
    ropid,
    amount,
  } = route.params;
  const [modalVisible, setModalVisible] = useState(false);
  const [point, setPoint] = useState();
  const [userPoint, setUserPoint] = useState();
  const [loginToken, setLoginToken] = useState();
  const [loginMemberId, setLoginMemberId] = useState();
  const [loginVip, setLoginVip] = useState();
  const [random, setRandom] = useState();
  const [isLoading, setLoading] = useState(false);
  const [tablePromoCode, setTablePromoCode] = useState();
  const [tablePromoPrice, setTablePromoPrice] = useState();

  const [tablePromoProductName, setTablePromoProductName] = useState();
  const [tablePromoProductId, setTablePromoProductId] = useState();

  const _readVal = async () => {
    await AsyncStorage.multiGet([
      '@userdata',
      '@token',
      '@memberid',
      '@tablePromoCode',
      '@tablePromoPrice',
      '@vip',
      '@tablePromoProductName',
      '@tablePromoProductId',
    ])
      .then(r => {
        const data = JSON.parse(r[0][1]);
        setUserPoint(data.point);
        setLoginToken(r[1][1]);
        setLoginMemberId(r[2][1]);
        setTablePromoCode(r[3][1]);
        setTablePromoPrice(r[4][1]);
        setLoginVip(r[5][1]);
        let rand = (Math.random() + 1).toString(36).substring(2, 12);
        setRandom(rand);
        setPoint(0);
        setTablePromoProductName(r[6][1]);
        setTablePromoProductId(r[7][1]);
      })
      .catch(err => {
        console.log(err);
      });
  };
  const _onRemovePromo = async () => {
    AsyncStorage.multiRemove([
      '@tablePromoId',
      '@tablePromoCode',
      '@tablePromoPrice',
      '@tablePromoProductId',
      '@tablePromoProductName',
    ]);
    setTablePromoCode();
    setTablePromoPrice();
    setTablePromoProductName();
    setTablePromoProductId();
    setPoint(0);
  };

  const _onRemovePoint = async () => {
    setPoint(0);
  };

  // adisyonu görmek için
  // http://sushico.yazilimocagi.net:9090/api/loyality/activeorders?StoreId=XXXX

  // puanla ödeme için
  // http://sushico.yazilimocagi.net:9090/api/loyality/discount?StoreId=XXX&OrderId=YYYY&Discount=ZZZZ

  const _tableSavePoint = async () => {
    setLoading(true);
    let rand = (Math.random() + 1).toString(36).substring(2, 12);
    const reqdata = {
      Amount: item.AdisyonTutari,
      OrderId: item.AdisyonId,
      TableId: item.MasaNo,
      items: item.Siparis,
      ropid,
      product: product,
      memberId: loginMemberId,
      restaurant: restaurant,
      token: loginToken,
      usePoint: point,
      promoCode: tablePromoCode ? tablePromoCode : '',
      promoDiscount: tablePromoPrice ? tablePromoPrice : 0,
    };
    const req = JSON.stringify(reqdata);

    const encText = rand + Base64.encode(req);

    const tempdatas = {
      currency: 'TRY',
      value: item.AdisyonTutari,
      restaurant_id: restaurant,
      user_selected_country: 'Turkey',
    };
    analytics().logEvent('qr_purchase', tempdatas);
    mobileApi
      .post(`/${locale}/orderTableSaveV3`, encText)
      .then(response => {
        const isSuccess = response?.data?.err === 0;
        setModalVisible(false);
        AsyncStorage.multiRemove([
          '@tablePromoId',
          '@tablePromoCode',
          '@tablePromoPrice',
          '@tablePromoProductName',
          '@tablePromoProductId',
        ]).then(() => {
          setTablePromoCode();
          setTablePromoPrice();
          setTablePromoProductName();
          setTablePromoProductId();
          setPoint(0);
          if (!isSuccess) {
            return Alert.alert(t('payment').warning, response?.data?.msg);
          }
          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [
                {
                  name: 'Settings',
                  state: {
                    routes: [
                      {name: 'SettingsMain'},
                      {
                        name: 'QrCodeThankYouScreen',
                        params: {
                          winPoint: response?.data?.winPoint,
                          showFortuneWheel:
                            response?.data?.fortuneWheel == 1 ? true : false,
                        },
                      },
                    ],
                    index: 1,
                  },
                },
              ],
            }),
          );
        });
      })
      .catch(error => {
        console.error('❌ Error during orderTableSaveV3:', error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const _tableSaveUsePoint = async () => {
    setLoading(true);
    let err = 0;
    const promoDiscount = tablePromoPrice ? tablePromoPrice : 0;

    if (promoDiscount > 0) {
      Alert.alert(t('payment').warning, t('qr.pointErr6'), [
        {
          text: t('payment').ok,
        },
      ]);
      err = 1;
    }

    if (loginVip > 0 && err == 0) {
      if (parseInt(point) > parseInt(item.Amount)) {
        Alert.alert(t('payment').warning, t('qr.pointErr7'), [
          {
            text: t('payment').ok,
          },
        ]);
        err = 1;
      } else if (parseInt(point) > parseInt(userPoint)) {
        Alert.alert(t('payment').warning, t('qr.pointErr2'), [
          {
            text: t('payment').ok,
          },
        ]);
        err = 1;
      }
    }

    if (loginVip == 0 && err == 0) {
      if (parseInt(point) > parseInt(item.Amount / 2)) {
        Alert.alert(t('payment').warning, t('qr.pointErr1'), [
          {
            text: t('payment').ok,
          },
        ]);
      } else if (parseInt(point) > parseInt(userPoint)) {
        Alert.alert(t('payment').warning, t('qr.pointErr2'), [
          {
            text: t('payment').ok,
          },
        ]);
      }
    }
    if (err == 1) {
      setPoint(0);
    }
    setModalVisible(false);
    setLoading(false);
  };

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerLeft: () => (
        <TouchableOpacity
          onPress={() => {
            navigate('Settings', {screen: 'SettingsMain'});
          }}>
          <Icon
            name="angle-left"
            type="font-awesome"
            iconStyle={{color: 'white', fontSize: 40, paddingLeft: 8}}
          />
        </TouchableOpacity>
      ),
    });
    setPoint(0);
    setTablePromoCode();
    setTablePromoPrice();
    _readVal();
    //console.log("route", JSON.stringify(item.items));
  }, [isFocused]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {
          setModalVisible(!modalVisible);
        }}>
        <View style={pagestyles.centeredView}>
          <View style={pagestyles.modalView}>
            <Pressable
              onPress={() => {
                setPoint(0);
                setModalVisible(!modalVisible);
              }}
              style={{alignSelf: 'flex-end', bottom: 12, right: -12}}>
              <View
                style={[
                  {
                    alignItems: 'center',
                    width: '50%',
                    borderRadius: 10,
                    backgroundColor: '#ec1d23',
                    padding: 8,
                  },
                ]}>
                <Text style={[sc.PrimaryButton_text_small]}>
                  {t('button.close')}
                </Text>
              </View>
            </Pressable>

            <View>
              <Text
                style={{textAlign: 'center', fontSize: 20, ...styles.black}}>
                {t('qr.yourpoint')} {'\n'} {userPoint} {t('currency')}
              </Text>
            </View>
            <View style={{marginTop: 5}}>
              <View style={[styles.labelline, {alignSelf: 'flex-start'}]}>
                <Text style={[styles.labelStyle, styles.text18]}>
                  {t('qr.usepoint')}
                </Text>
              </View>
              <View style={styles.kutu_full}>
                <TextInput
                  style={[styles.InputStyle, {marginVertical: 5}]}
                  placeholder=""
                  autoCapitalize="none"
                  autoCorrect={false}
                  value={point}
                  keyboardType="numeric"
                  onChangeText={value => setPoint(value)}
                />
              </View>
            </View>
            <Pressable onPress={() => _tableSaveUsePoint()}>
              <View style={sc.PrimaryButton}>
                <Text style={sc.PrimaryButton_text}>
                  {t('button.paymentPoint')}
                </Text>
              </View>
            </Pressable>
          </View>
        </View>
      </Modal>
      <ScrollView style={{margin: 20, display: 'flex'}}>
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 20,
            paddingBottom: 5,
            borderBottomWidth: 1,
            borderBottomColor: 'red',
          }}>
          <Text style={[styles.text20, styles.black]}>
            {t('qr.branch')} : {branchName}
          </Text>
          <Text style={[styles.text20, styles.black]}>
            {item.AdisyonTutari} {t('currency')}
          </Text>
        </View>
        {product?.map((list, i) => {
          return (
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginBottom: 20,
              }}
              key={i}>
              <Text style={[styles.text16, styles.black]}>
                {list.adet} x {list.ad}
              </Text>
              <Text style={[styles.text16, styles.black]}>
                {list.fiyat} {t('currency')}
              </Text>
            </View>
          );
        })}
        {tablePromoProductId && (
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginBottom: 20,
            }}>
            <Text style={[styles.text16, {textDecorationLine: 'line-through'}]}>
              1 x {tablePromoProductName}
            </Text>
            <Text style={[styles.text16, {textDecorationLine: 'line-through'}]}>
              -{tablePromoPrice} {t('currency')}
            </Text>
          </View>
        )}
      </ScrollView>
      {tablePromoPrice && (
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 10,
            paddingTop: 10,
            borderTopWidth: 1,
            borderTopColor: 'red',
            marginHorizontal: 20,
          }}>
          <View>
            <TouchableOpacity
              onPress={() => _onRemovePromo()}
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <Icon
                name="close"
                type="simple-line-icon"
                iconStyle={[styles.Txt_renk3, {fontSize: 20, marginRight: 8}]}
              />
              <Text style={[styles.text18, styles.Txt_renk3]}>
                {t('usePromoDiscount')} - {tablePromoCode}
              </Text>
            </TouchableOpacity>
          </View>
          <Text style={[styles.text18, styles.Txt_renk3]}>
            - {tablePromoPrice} {t('currency')}
          </Text>
        </View>
      )}

      {point > 0 && (
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 10,
            marginHorizontal: 20,
          }}>
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginBottom: 20,
            }}>
            <TouchableOpacity onPress={() => _onRemovePoint()}>
              <Icon
                name="close"
                type="simple-line-icon"
                iconStyle={[styles.Txt_renk3, {fontSize: 20, marginRight: 8}]}
              />
            </TouchableOpacity>
            <Text style={[styles.text18, styles.Txt_renk3]}>
              {t('usePointDiscount')}
            </Text>
          </View>
          <Text style={[styles.text18, styles.Txt_renk3]}>
            - {point} {t('currency')}
          </Text>
        </View>
      )}

      {/* TOTAL SUM */}
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginHorizontal: 20,
        }}>
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 0,
          }}>
          <Text style={[styles.text18, styles.Txt_renk1]}>
            {t('amountTotal')}
          </Text>
        </View>
        <Text style={[styles.text18, styles.Txt_renk1]}>
          {parseInt(item.AdisyonTutari) -
            (parseInt(point ? point : 0) +
              parseInt(tablePromoPrice ? tablePromoPrice : 0))}{' '}
          {t('currency')}
        </Text>
      </View>

      {/* USE PROMO OR POINT */}
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-around',
          margin: 10,
        }}>
        <TouchableOpacity
          onPress={() => {
            setModalVisible(true);
            setPoint();
          }}>
          <View
            style={[styles.button100, styles.bgrenk2, {marginHorizontal: 5}]}>
            <Text style={sc.PrimaryButton_text}>
              {t('button.paymentPoint')}
            </Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => {
            if (parseInt(point) > 0) {
              Alert.alert(t('payment').warning, t('qr.pointErr6'), [
                {
                  text: t('payment').ok,
                },
              ]);
            } else {
              navigate('QrCodePromo', {
                qrBranch,
                qrOrderId,
                restaurant,
                amount: amount,
              });
            }
          }}>
          <View style={[styles.button100, styles.bgrenk2]}>
            <Text style={sc.PrimaryButton_text}>{t('button.usePromo')}</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* CONFIRM */}
      <View
        style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          marginBottom: 10,
        }}>
        <TouchableOpacity
          onPress={() => {
            setPoint();
            _tableSavePoint();
          }}>
          <View
            style={[styles.button100, styles.bgrenk1, {marginHorizontal: 5}]}>
            <Text style={sc.ThirdButtonButton_text}>
              {t('button.winPoint')}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const pagestyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'flex-start',
    paddingTop: 150,
    alignItems: 'center',
    backgroundColor: '#00000090',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 30,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    borderRadius: 20,
    padding: 10,
    elevation: 2,
  },
  buttonOpen: {
    backgroundColor: '#F194FF',
  },
  buttonClose: {
    backgroundColor: '#2196F3',
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
  shadow: {
    shadowOffset: {width: 3, height: 3},
    shadowColor: 'black',
    shadowOpacity: 0.4,
    shadowRadius: 8,
    backgroundColor: 'white',
    display: 'flex',
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: 'silver',
    paddingVertical: 10,
    paddingHorizontal: 30,
    width: 250,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

var Base64 = {
  _keyStr: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
  encode: function (e) {
    var t = '';
    var n, r, i, s, o, u, a;
    var f = 0;
    e = Base64._utf8_encode(e);
    while (f < e.length) {
      n = e.charCodeAt(f++);
      r = e.charCodeAt(f++);
      i = e.charCodeAt(f++);
      s = n >> 2;
      o = ((n & 3) << 4) | (r >> 4);
      u = ((r & 15) << 2) | (i >> 6);
      a = i & 63;
      if (isNaN(r)) {
        u = a = 64;
      } else if (isNaN(i)) {
        a = 64;
      }
      t =
        t +
        this._keyStr.charAt(s) +
        this._keyStr.charAt(o) +
        this._keyStr.charAt(u) +
        this._keyStr.charAt(a);
    }
    return t;
  },
  decode: function (e) {
    var t = '';
    var n, r, i;
    var s, o, u, a;
    var f = 0;
    e = e.replace(/[^A-Za-z0-9\+\/\=]/g, '');
    while (f < e.length) {
      s = this._keyStr.indexOf(e.charAt(f++));
      o = this._keyStr.indexOf(e.charAt(f++));
      u = this._keyStr.indexOf(e.charAt(f++));
      a = this._keyStr.indexOf(e.charAt(f++));
      n = (s << 2) | (o >> 4);
      r = ((o & 15) << 4) | (u >> 2);
      i = ((u & 3) << 6) | a;
      t = t + String.fromCharCode(n);
      if (u != 64) {
        t = t + String.fromCharCode(r);
      }
      if (a != 64) {
        t = t + String.fromCharCode(i);
      }
    }
    t = Base64._utf8_decode(t);
    return t;
  },
  _utf8_encode: function (e) {
    e = e.replace(/\r\n/g, '\n');
    var t = '';
    for (var n = 0; n < e.length; n++) {
      var r = e.charCodeAt(n);
      if (r < 128) {
        t += String.fromCharCode(r);
      } else if (r > 127 && r < 2048) {
        t += String.fromCharCode((r >> 6) | 192);
        t += String.fromCharCode((r & 63) | 128);
      } else {
        t += String.fromCharCode((r >> 12) | 224);
        t += String.fromCharCode(((r >> 6) & 63) | 128);
        t += String.fromCharCode((r & 63) | 128);
      }
    }
    return t;
  },
  _utf8_decode: function (e) {
    var t = '';
    var n = 0;
    var r = (c1 = c2 = 0);
    while (n < e.length) {
      r = e.charCodeAt(n);
      if (r < 128) {
        t += String.fromCharCode(r);
        n++;
      } else if (r > 191 && r < 224) {
        c2 = e.charCodeAt(n + 1);
        t += String.fromCharCode(((r & 31) << 6) | (c2 & 63));
        n += 2;
      } else {
        c2 = e.charCodeAt(n + 1);
        c3 = e.charCodeAt(n + 2);
        t += String.fromCharCode(
          ((r & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63),
        );
        n += 3;
      }
    }
    return t;
  },
};

export default QrCodeSuccessScreen;
