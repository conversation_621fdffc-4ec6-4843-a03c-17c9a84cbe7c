import React, {useContext, useEffect, useState} from 'react';
import {View, Text, Image, ScrollView, Pressable} from 'react-native';
import styles from '../../stylesheets/styles';
import * as sc from '../../stylesheets/stylesShort';
import {Context as UserContext} from '../../context/UserContext';
import LocalizationContext from '../../context/LocalizationContext';
import {CommonActions, useNavigation, useRoute} from '@react-navigation/native';
import mobileApi from '../../api/mobileApi';
import FortuneWheelModal from '../../components/FortuneWheelModal';
const QrCodeThankYouScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {winPoint, showFortuneWheel} = route?.params;
  const {t} = useContext(LocalizationContext);
  const l = t('payment');
  const {state} = useContext(UserContext);

  const [isWheelVisible, setIsWheelVisible] = useState(false);

  useEffect(() => {
    navigation.setOptions({
      title: l.thankyouTitle,
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
      headerLeft: () => null,
    });
    setTimeout(() => {
      setIsWheelVisible(showFortuneWheel);
    }, 1000);
  }, []);

  return (
    <ScrollView style={styles.container} bounces={false}>
      <View
        style={[
          styles.container,
          {
            paddingTop: 10,
            alignItems: 'center',
            textAlign: 'center',
            padding: 40,
            gap: 20,
          },
        ]}>
        <View style={{marginVertical: 20, alignItems: 'center'}}>
          <Image
            source={require('../../../assets/img/logo.png')}
            style={{height: 100, resizeMode: 'contain'}}
          />
        </View>
        <Image
          source={require('../../../assets/img/icon_check.png')}
          style={{height: 32, width: 32, marginLeft: 10, resizeMode: 'contain'}}
        />
        <Text style={[styles.text20, {color: '#29850d', textAlign: 'center'}]}>
          {t('qr.success')} {winPoint}
        </Text>
        <Text style={[styles.text32, styles.Txt_renk1]}>{l.thankyou_txt1}</Text>

        <Image
          source={require('../../../assets/img/icon_home_degerlendirme.png')}
          style={{
            height: 32,
            width: 32,
            marginLeft: 10,
            resizeMode: 'contain',
          }}
        />
        <Text style={[styles.text20, {textAlign: 'center', color: 'black'}]}>
          {l.thankyou_txt3}{' '}
        </Text>

        {showFortuneWheel && (
          <Pressable
            style={[{marginTop: 20}, sc.PrimaryButton]}
            onPress={() => setIsWheelVisible(true)}>
            <Text style={[sc.PrimaryButton_text, styles.ozelfont_medium]}>
              {t('settingsMenu.Coupons.showWheel')}
            </Text>
          </Pressable>
        )}
      </View>
      <FortuneWheelModal
        source={`${mobileApi.defaults.webURL}/fortuneWheel.php?token=${state?.token}`}
        isVisible={isWheelVisible}
        setIsVisible={setIsWheelVisible}
        onPress={() => {
          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [
                {
                  name: 'Settings',
                  state: {
                    routes: [{name: 'SettingsMain'}, {name: 'SettingsCoupons'}],
                    index: 1,
                  },
                },
              ],
            }),
          );
          // navigation.replace('SettingsCoupons');
          setIsWheelVisible(false);
        }}
      />
    </ScrollView>
  );
};

export default QrCodeThankYouScreen;
