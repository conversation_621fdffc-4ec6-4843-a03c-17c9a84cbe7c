import React, { useState, useEffect } from 'react';
import { SafeAreaView } from 'react-native';

import styles from '../../stylesheets/styles';
import LocalizationContext from '../../context/LocalizationContext';
import { Context as UserContext } from '../../context/UserContext';
import { useIsFocused } from '@react-navigation/native';
import mobileApi from '../../api/mobileApi';
import { WebView } from 'react-native-webview';

const SettingsReferer = ({ navigation }) => {
  const isFocused = useIsFocused();
  const { state } = React.useContext(UserContext);
  const { locale } = React.useContext(LocalizationContext);

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
  }, [isFocused]);

  return (
    <SafeAreaView style={styles.container}>
      <WebView
        source={{
          uri: `${mobileApi.defaults.baseURL}/page/affilate.php?userid=${state.memberid}&lang=${locale}`,
        }}
        startInLoadingState={true}
        scalesPageToFit={true}
        style={{
          flex: 1,
        }}
      />
    </SafeAreaView>
  );
};

export default SettingsReferer;
