import React, { useState, useEffect } from "react";
import { SafeAreaView, TouchableOpacity } from "react-native";

import styles from "../../stylesheets/styles";
import LocalizationContext from "../../context/LocalizationContext";
import { useIsFocused } from "@react-navigation/native";
import { WebView } from "react-native-webview";
import { Icon } from "react-native-elements";
import { navigate } from "../../navigationRef";

const TrackScreen = ({ navigation, route }) => {
  const isFocused = useIsFocused();
  const { locale } = React.useContext(LocalizationContext);

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: "",
      headerLeft: () => (
        <TouchableOpacity
          onPress={() => {
            navigate("Home", {});
          }}
        >
          <Icon name="angle-left" type="font-awesome" iconStyle={{ color: "white", fontSize: 40, paddingLeft: 8 }} />
        </TouchableOpacity>
      ),
    });
  }, [isFocused]);

  return (
    <SafeAreaView style={styles.container}>
      <WebView
        source={{
          uri: `https://www.trackonlive.com/t/${route.params.orderid}`,
        }}
        startInLoadingState={true}
        scalesPageToFit={true}
        style={{
          flex: 1,
        }}
      />
    </SafeAreaView>
  );
};

export default TrackScreen;
