import React, {useState, useEffect, useCallback, memo, useContext} from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Modal,
  SafeAreaView,
  TouchableOpacity,
  Pressable,
  Alert,
  FlatList,
} from 'react-native';

import styles from '../../../stylesheets/styles';
import * as sc from '../../../stylesheets/stylesShort';
import {Icon} from 'react-native-elements';
import mobileApi from '../../../api/mobileApi';
import Loading from '../../../components/Loading';
import {Context as UserContext} from '../../../context/UserContext';
import LocalizationContext from '../../../context/LocalizationContext';
import {PromoCard, ListHeaderComponent} from './components';
import {parseDate} from '../../../utils';
const PromoScreen = ({navigation}) => {
  const {locale} = useContext(LocalizationContext);
  const {token, memberid: memberId} = useContext(UserContext).state;
  const [isLoading, setIsLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);

  const [allPromoCodes, setAllPromoCodes] = useState([]);

  const fetchAllPromoCodes = useCallback(async () => {
    setIsLoading(true);
    const [promoCodes, userPromoCodes] = await Promise.all([
      fetchPromoCodes(),
      fetchUserPromoCodes(),
    ]);
    const mergedPromoCodes = [...promoCodes, ...userPromoCodes];
    const sortedPromoCodes = mergedPromoCodes.sort((a, b) => {
      const dateA = parseDate(a.expireDate);
      const dateB = parseDate(b.expireDate);

      return dateA - dateB;
    });

    setAllPromoCodes(sortedPromoCodes);
    setIsLoading(false);
  }, []);

  const fetchPromoCodes = async () => {
    try {
      const getPromosPayload = {
        memberId,
        token,
        myPromos: '1',
      };
      const getPromosResponse = await mobileApi.post(
        `/${locale}/getPromosV2`,
        getPromosPayload,
      );
      const promoCodes = getPromosResponse?.data?.promos;
      if (!Array.isArray(promoCodes) || !promoCodes.length) {
        return [];
      }
      const convertedPromoCodes = promoCodes?.map(promoCode => ({
        code: promoCode?.code,
        expireDate: promoCode?.sgt,
        minAmount: promoCode?.minprice,
        price: promoCode?.price,
        promoProduct: promoCode?.product,
        promoType: promoCode?.promotype,
        useCode: promoCode?.usePromo,
        useType: promoCode?.useType,
        shortdesc: promoCode?.shortdesc,
      }));
      return convertedPromoCodes;
    } catch (error) {
      return [];
    }
  };

  const fetchUserPromoCodes = async () => {
    try {
      const userPromosPayload = {
        token,
        memberId,
      };
      const userPromoResponse = await mobileApi.post(
        `/${locale}/getUserPromoV2`,
        userPromosPayload,
      );
      const userPromos = userPromoResponse?.data?.map(userPromo => ({
        ...userPromo,
        isSaved: true,
      }));
      if (!Array.isArray(userPromos) || !userPromos.length) {
        return [];
      }
      return userPromos;
    } catch (error) {
      return [];
    }
  };

  const handleAddPromo = useCallback(() => {
    setModalVisible(true);
  }, []);

  const renderPromoItem = useCallback(
    ({item: promo}) => {
      return <PromoCard promo={promo} fetchUserPromos={fetchAllPromoCodes} />;
    },
    [allPromoCodes],
  );

  const renderPromoHeader = useCallback(() => {
    return <ListHeaderComponent onAddPromo={handleAddPromo} />;
  }, []);

  useEffect(() => {
    navigation.setOptions({
      headerTruncatedBackTitle: '',
      headerBackTitle: '',
    });
    fetchAllPromoCodes();
  }, [locale, navigation]);

  if (isLoading) return <Loading />;

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        contentContainerStyle={{gap: 0}}
        data={allPromoCodes}
        renderItem={renderPromoItem}
        ListHeaderComponent={renderPromoHeader}
        // ItemSeparatorComponent={renderSeparator}
      />

      <AddPromoModal
        visible={modalVisible}
        setVisible={setModalVisible}
        onModalDismiss={() => {
          setModalVisible(!modalVisible);
        }}
        fetchUserPromos={fetchAllPromoCodes}
      />
    </SafeAreaView>
  );
};

export default PromoScreen;

const AddPromoModal = ({
  onModalDismiss,
  visible,
  setVisible,
  fetchUserPromos,
}) => {
  const {t, locale} = React.useContext(LocalizationContext);
  const {token, memberid: memberId} = React.useContext(UserContext).state;
  const [promoCode, setPromoCode] = useState('');

  const savePromo = async () => {
    try {
      const payload = {
        code: promoCode,
        token,
        memberId,
      };
      const response = await mobileApi.post(
        `/${locale}/saveUserPromo`,
        payload,
      );
      if (response.data.err === 1) {
        return Alert.alert(t('payment').warning, response.data.msg, [
          {
            text: t('payment').ok,
          },
        ]);
      }
      await fetchUserPromos();
      setPromoCode();
      setVisible(false);
    } catch (error) {
      console.log('SAVE PROMO ERROR:', error);
    }
  };

  const handleRequestClose = () => {
    setPromoCode('');
    onModalDismiss();
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={handleRequestClose}>
      <View style={modalStyles.centeredView}>
        <View style={modalStyles.modalView}>
          <Pressable
            onPress={handleRequestClose}
            style={modalStyles.dismissBtn}>
            <View style={[sc.PrimaryButton_small, {width: undefined}]}>
              <Text
                style={[sc.PrimaryButton_text_small, modalStyles.dismissTxt]}>
                {t('button.close')}
              </Text>
            </View>
          </Pressable>
          <View style={[styles.labelline, styles.mt15]}>
            <Text style={(styles.labelStyle, styles.text18, styles.black, {fontFamily: 'AndaleMono', letterSpacing: -1})}>
              {t('payment').promocode}
            </Text>
          </View>
          <View style={styles.kutu_full}>
            <TextInput
              value={promoCode}
              onChangeText={value => setPromoCode(value)}
              style={[styles.InputStyle, {marginVertical: 20,fontSize:24, fontWeight:500, textAlign: 'center'}]}
              autoCapitalize="characters"
              autoCorrect={false}
            />
          </View>
          <TouchableOpacity
            style={modalStyles.approveBtn}
            onPress={() => savePromo(promoCode)}>
            <View style={[styles.button100, styles.bgrenk1]}>
              <Text style={sc.ThirdButtonButton_text}>{t('button.save')}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const pagestyles = StyleSheet.create({
  headerContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  addPromoBtn: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  addPromoIcon: {
    color: 'white',
    fontSize: 16,
    flexDirection: 'row',
  },
});

const modalStyles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#00000090',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 30,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  dismissBtn: {
    right: 10,
    top: 0,
    paddingHorizontal: 15,
    position: 'absolute',
  },
  dismissTxt: {
    alignSelf: 'flex-start',
    lineHeight: 24,
    color: '#78bae7',
  },
  approveBtn: {
    marginHorizontal: 24,
    width: '90%',
    marginBottom: 30,
  },
});
