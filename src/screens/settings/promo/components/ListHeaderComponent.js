import React, {memo} from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {Icon} from 'react-native-elements';
import styles from '../../../../stylesheets/styles';
import * as sc from '../../../../stylesheets/stylesShort';
import LocalizationContext from '../../../../context/LocalizationContext';
const ListHeaderComponent = memo(({onAddPromo = callback => {}}) => {
  const {t} = React.useContext(LocalizationContext);

  return (
    <View>
      <View style={pagestyles.headerContainer}>
        <View
          style={[
            styles.alertbox,
            {
              justifyContent: 'center',
              gap: 10,
              backgroundColor: '#78BAE750',
            },
          ]}>
          <Icon
            name="present"
            type="simple-line-icon"
            iconStyle={[styles.black, {fontSize: 32}]}
          />
          <Text
            style={[
              styles.text14,
              styles.tcenter,
              styles.ozelfont_bold,
              styles.black,
            ]}>
            {' '}
            {t('profile.info_save_text')}
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.button100, styles.bgrenk1, pagestyles.addPromoBtn,{marginBottom:30}]}
        onPress={onAddPromo}>
        <Icon
          name="plus"
          type="font-awesome"
          iconStyle={pagestyles.addPromoIcon}
        />
        <Text style={[sc.PrimaryButton_text,{color: '#78BAE7'}]}>{t('profile.codedesc')} </Text>
      </TouchableOpacity>
    </View>
  );
});
export default ListHeaderComponent;

const pagestyles = StyleSheet.create({
  headerContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  addPromoBtn: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  addPromoIcon: {
    color: '#78BAE7',
    fontSize: 18,
    flexDirection: 'row',
  },
});
