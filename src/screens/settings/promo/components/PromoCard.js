import React, {useCallback, useContext} from 'react';
import {Alert, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {Context as UserContext} from '../../../../context/UserContext';
import LocalizationContext from '../../../../context/LocalizationContext';
import mobileApi from '../../../../api/mobileApi';
import PromoCode from './PromoCode';
import PromoExpiry from './PromoExpiry';
import PromoCondition from './PromoCondition';
import PromoContent from './PromoContent';
import PromoType from './PromoType';
import PromoDeleteButton from './PromoDeleteButton';
import styles from '../../../../stylesheets/styles';
import * as sc from '../../../../stylesheets/stylesShort';
//useType:1-->Masa
//useType:0--><PERSON><PERSON> ve <PERSON>et
//useType:-1-->Masa

//promoType:1-->Indirim
//promoType:2--><PERSON><PERSON><PERSON><PERSON>

const PromoCard = ({
  promo,
  fetchUser<PERSON>romos,
  allowedAction = 'delete',
  onPromoUse = () => {},
  orderMode = 'online-order',
}) => {
  if (promo?.useCode === 1) return;
  if (
    allowedAction === 'use' &&
    ((orderMode === 'online-order' && promo?.useType === 1) ||
      (orderMode === 'qr' && promo?.useType === -1))
  ) {
    return;
  }

  const {t, locale} = useContext(LocalizationContext);
  const {token, memberid: memberId} = React.useContext(UserContext).state;

  const handlePromoDeleteConfirmation = code => {
    return Alert.alert(t('payment').warning, t('alert.delPromo'), [
      {
        text: t('alert').cancelBtn,
        style: 'cancel',
      },
      {
        text: t('alert').okBtn,
        onPress: () => handlePromoDelete(code),
      },
    ]);
  };
  const handlePromoDelete = useCallback(async code => {
    try {
      const req = {
        code,
        token: token,
        memberId: memberId,
      };
      const response = await mobileApi.post(`/${locale}/removeUserPromo`, req);
      if (response.data.err === 1) {
        return Alert.alert(t('payment').warning, response.data.msg, [
          {
            text: t('payment').ok,
          },
        ]);
      }
      fetchUserPromos();
    } catch (error) {
      console.log('REMOVE PROMO ERROR:', error);
    }
  }, []);

  return (
    <View style={pagestyles.promoCardContainer}>
      <View style={pagestyles.promoHeaderContainer}>
        <View style={pagestyles.promoHeader}>
          <PromoCode
            title={promo?.shortdesc ? promo?.shortdesc : promo?.code}
          />
          <PromoExpiry date={promo?.expireDate} />
        </View>
        <View style={pagestyles.promoActionButtonContainer}>
          {allowedAction == 'use' ? (
            <TouchableOpacity
              style={[styles.bgrenk1, pagestyles.promoUseBtn]}
              onPress={() => onPromoUse(promo)}>
              <Text style={sc.PrimaryButton_text}>{t('button.use')}</Text>
            </TouchableOpacity>
          ) : (
            promo?.isSaved && (
              <PromoDeleteButton
                code={promo?.code}
                onPress={handlePromoDeleteConfirmation}
              />
            )
          )}
        </View>
      </View>
      <View style={pagestyles.promoContentContainer}>
        <PromoCondition condition={promo?.minAmount} />
        <PromoContent
          contentType={promo?.promoType}
          discount={promo?.price}
          product={promo?.promoProduct}
        />
        <PromoType type={promo?.useType} />
      </View>
    </View>
  );
};
export default PromoCard;
const pagestyles = StyleSheet.create({
  promoCardContainer: {
    marginHorizontal: 20,
    flex: 1,
    padding: 4,
   marginBottom: 24,
   paddingBottom: 12,
    borderBottomWidth: 0.8,
    borderBottomColor: '#ddd',
  },
  promoHeaderContainer: {
    flexDirection: 'row',
    gap: 4,
    // backgroundColor: 'green',
  },
  promoHeader: {
    flex: 3,
    // backgroundColor: 'orange'
  },
  promoUseBtn: {
    right: 0,
    top: 0,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 2,
    gap: 6,
  },
  promoActionButtonContainer: {
    alignItems: 'center',
  },
  promoContentContainer: {flex: 1},
});
