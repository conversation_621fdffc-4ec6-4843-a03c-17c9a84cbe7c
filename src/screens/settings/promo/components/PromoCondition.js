import React, {useContext} from 'react';
import {Text} from 'react-native';
import LocalizationContext from '../../../../context/LocalizationContext';
import styles from '../../../../stylesheets/styles';

const PromoCondition = ({condition}) => {
  const {t} = useContext(LocalizationContext);

  return (
    <Text style={[styles.ozelfont_medium, styles.text14, {color: 'black', marginVertical: 5}]}>
      {t('payment.minamountshort')} : {condition}
      {t('currency')}
    </Text>
  );
};
export default PromoCondition;
