import React, {useContext} from 'react';
import {Text} from 'react-native';
import LocalizationContext from '../../../../context/LocalizationContext';
import styles from '../../../../stylesheets/styles';

const PromoContent = ({contentType, discount, product}) => {
  const {t} = useContext(LocalizationContext);

  if (contentType === 1) {
    return (
      <Text style={[styles.ozelfont_medium, styles.text14, {color: 'black', marginVertical: 5}]}>
        {t('discount')} : {discount} {t('currency')}
      </Text>
    );
  }
  return (
    <Text style={[styles.ozelfont_medium, styles.text14, {color: 'black', marginVertical: 5}]}>
      {t('product')} : {product}
    </Text>
  );
};
export default PromoContent;
