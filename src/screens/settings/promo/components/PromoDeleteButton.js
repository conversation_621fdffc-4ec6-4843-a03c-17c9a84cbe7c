import React from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import {Icon} from 'react-native-elements';

const PromoDeleteButton = ({onPress = () => {}, code}) => {
  return (
    <TouchableOpacity onPress={() => onPress(code)} style={styles.deleteBtn}>
      <Icon name="trash-o" type="font-awesome" iconStyle={styles.icon} />
    </TouchableOpacity>
  );
};
export default PromoDeleteButton;

const styles = StyleSheet.create({
  deleteBtn: {
    right: 0,
    top: 0,
    borderWidth: 1,
    borderColor: '#000',
    backgroundColor: '#000',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 14,
    borderRadius: 50,
  },
  icon: {color: '#fff', fontSize: 18},
});
