import React from 'react';
import {Text} from 'react-native';
import {useContext} from 'react';
import LocalizationContext from '../../../../context/LocalizationContext';
import styles from '../../../../stylesheets/styles';

const PromoExpiry = ({date}) => {
  const {t} = useContext(LocalizationContext);

  return (
    <Text style={[styles.ozelfont_medium, styles.text14, {color: 'black', marginVertical: 5}]}>
      {t('profile.sgt')} : {date}
    </Text>
  );
};
export default PromoExpiry;
