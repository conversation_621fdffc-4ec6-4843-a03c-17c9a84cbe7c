import React, {useContext} from 'react';
import {StyleSheet, Text} from 'react-native';
import LocalizationContext from '../../../../context/LocalizationContext';
import styles from '../../../../stylesheets/styles';

const PromoType = ({type}) => {
  const {t} = useContext(LocalizationContext);
  const getTypeStyle = () => {
    switch (type) {
      case 0:
        return pagestyles.badge1;
      case 1:
        return pagestyles.badge2;
      case -1:
        return pagestyles.badge3;
      default:
        return pagestyles.badge1;
    }
  };

  return (
    <Text
      style={[
        styles.ozelfont_medium,
        styles.text14,
        getTypeStyle(),
        {color: 'black',paddingVertical: 8, paddingHorizontal: 10, borderRadius: 2},
      ]}>
      {t(`profile.kodtip${type}`)}
    </Text>
  );
};
export default PromoType;
const pagestyles = StyleSheet.create({
  badge1: {backgroundColor: '#d999dd'},
  badge2: {backgroundColor: '#fb55a6'},
  badge3: {backgroundColor: '#78bae7'},
});
