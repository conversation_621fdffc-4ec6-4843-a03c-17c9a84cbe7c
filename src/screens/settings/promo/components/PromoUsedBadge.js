import React, {useContext} from 'react';
import {Text, View} from 'react-native';
import LocalizationContext from '../../../../context/LocalizationContext';
import styles from '../../../../stylesheets/styles';
import * as sc from '../../../../stylesheets/stylesShort';
const PromoUsedBadge = () => {
  const {t} = useContext(LocalizationContext);

  return (
    <View
      style={[
        {
          position: 'absolute',
          right: 0,
          backgroundColor: 'gray',
          flexDirection: 'row',
          paddingVertical: 10,
          paddingHorizontal: 15,
          borderRadius: 2,
          gap: 6,
        },
      ]}>
      <Text
        style={[
          sc.PrimaryButton_text,
          styles.ozelfont_medium,
          styles.text14,
          styles.bold,
          {textAlign: 'center'},
        ]}>
        {t('profile.use_btn')}
      </Text>
    </View>
  );
};
export default PromoUsedBadge;
