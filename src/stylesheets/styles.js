import {StyleSheet, Dimensions} from 'react-native';
const windowWidth = Dimensions.get('window').width;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 0,
    backgroundColor: '#fff',
    zIndex: 0,
  },

  Layout_bottom: {
    position: 'absolute',
    width: '100%',
    zIndex: 1,
    bottom: 75,
  },

  ozelfont_medium: {fontFamily: 'AndaleMono'},
  ozelfont_regular: {fontFamily: 'AndaleMono'},
  ozelfont_bold: {fontFamily: 'Andale Mono Bold'},
  ozelfont_light: {fontFamily: 'AndaleMono'},

  kurumsalfont_regular: {fontFamily: 'FrontageCondensed-Regular'},
  kurumsalfont_bold: {fontFamily: 'FrontageCondensed-Bold'},

  text11: {fontSize: 11},
  text12: {fontSize: 12},
  text14: {fontSize: 14},
  text16: {fontSize: 16},
  text18: {fontSize: 18},
  text20: {fontSize: 20},
  text22: {fontSize: 22},
  text24: {fontSize: 24},
  text26: {fontSize: 26},
  text28: {fontSize: 28},
  text30: {fontSize: 30},
  text32: {fontSize: 32},

  mt5: {marginTop: 5},
  mt10: {marginTop: 10},
  mt15: {marginTop: 15},
  mb10: {marginBottom: 10},

  ls1: {letterSpacing: -1},

  normal: {fontWeight: 'normal'},
  bold: {fontWeight: 'bold'},

  bgrenk1: {backgroundColor: '#000'}, //Ana Kırmızı Renk
  bgrenk2: {backgroundColor: '#313e48'}, //İkinci Antresit Renk
  bgrenk3: {backgroundColor: '#2b4960'}, //Üçüncü Turkuaz Renk
  bgrenk4: {backgroundColor: '#E9EAEB'}, //ButonGrisi
  bgrenk5: {backgroundColor: '#f1f2f1'}, //Gri Renk
  bgrenk6: {backgroundColor: '#000000'}, //Buton krem Renk
  bgrenk7: {backgroundColor: '#78BAE750'}, //Buton krem Renk

  bgwhite: {backgroundColor: '#ffffff'},
  bgblack: {backgroundColor: '#000000'},

  Txt_renk1: {color: '#d40b11'},
  Txt_renk2: {color: '#808080'},
  Txt_renk3: {color: '#323E48'},
  Txt_renk4: {color: '#10968C'},
  Txt_renk5: {color: '#9f00aa'}, //purple

  white: {color: '#ffffff'},
  black: {color: '#000000'},
  gray: {color: '#757575'},
  darkgray: {color: '#555555'},
  silver: {color: '#9f9f9f'},
  green: {color: '#008e2c'},
  red: {color: '#ec1d23'},
  golden: {color: '#be8200'},
  purple: {color: '#9f00aa'},
  mavi: {color: '#78BAE7'},

  renk1: {color: '#d79f97'},
  renk2: {color: '#424e53'},

  medium: {height: 50, padding: 10},
  small: {height: 36, padding: 5},

  tcenter: {textAlign: 'center'},
  tcenter20: {textAlign: 'center', fontSize: 20},

  logokucuk: {height: 80, width: 160},

  badge1: {backgroundColor: '#b0e8f2', paddingHorizontal: 10},
  badge2: {backgroundColor: '#e7bbb8', paddingHorizontal: 10},
  badge3: {backgroundColor: '#c1dabc', paddingHorizontal: 10},

  // yeni ekler
  anasayfa_element_box: {
    flex: 1,
    paddingHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 5,
    height: 45,
  },
  anasayfa_element_ikon: {
    paddingTop: 5,
    paddingLeft: 5,
    borderRadius: 5,
    backgroundColor: '#E9EAEB',
    flexDirection: 'row',
    width: '48%',
    borderWidth: 1,
    borderColor: '#dcdee0',
  },
  anasayfa_element_txt: {
    paddingLeft: 8,
    flexDirection: 'row',
    alignItems: 'center',
    width: 120,
  },
  anasayfa_element_yazi: {
    fontSize: 15,
    paddingBottom: 8,
    color: '#000000',
    fontFamily: 'AndaleMono',
    
  },
  anasayfa_element_yazi_sm: {
    fontSize: 11,
    color: '#000000',
    fontFamily: 'AndaleMono',
  },

  anasayfa_element_genis_ikon: {
    paddingTop: 9,
    paddingLeft: 5,
    borderRadius: 5,
    backgroundColor: '#E9EAEB',
    flexDirection: 'row',
    width: '94%',
    alignSelf: 'center',
    borderWidth: 1,
    borderColor: '#dcdee0',
    marginVertical: 4,
  },

  // yeni ekler

  box: {
    textAlign: 'center',
    marginHorizontal: 40,
    marginVertical: 5,
    borderRadius: 6,
    padding: 20,
    alignItems: 'center',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 10,
  },

  //VIEW ALANLAR
  kutu_full: {
    marginHorizontal: 5,
    marginVertical: 0,
    margin: 2,
    padding: 5,
    flexDirection: 'row',
    alignContent: 'space-around',
  },
  labelline: {
    marginHorizontal: 20,
    marginTop: 10,
  },

  kutu_child: {
    padding: 5,
    flex: 1,
  },

  kutu_footer: {
    margin: 2,
    flexDirection: 'row',
    alignContent: 'space-around',
  },

  kutu_footer_sol: {
    paddingVertical: 5,
    flex: 1,
    alignItems: 'flex-end',
    backgroundColor: '#f8ece3',
  },
  kutu_footer_sag: {
    paddingVertical: 5,
    flex: 1,
    flexGrow: 1.6,
    backgroundColor: '#f8ece3',
  },

  kutu_footer_txt: {
    alignItems: 'center',
    height: 42,
    width: '100%',
    marginVertical: 10,
    borderTopLeftRadius: 5,
    borderBottomLeftRadius: 5,
    backgroundColor: 'white',
    fontSize: 24,
    color: 'black',
    textAlign: 'center',
    textAlignVertical: 'center',
    borderColor: 'red',
    borderWidth: 1,
  },

  alertbox: {
    padding: 15,
    marginVertical: 20,
    borderWidth: 0.2,
    width: '100%',
    backgroundColor: '#f8ecde',
    alignItems: 'center',
    borderRadius: 10,
    alignSelf: 'center',
  },

  //Sepetiniz
  sepetiniz_full_kutu: {
    marginHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 6,
    borderRadius: 6,
    borderColor: '#808080',
    backgroundColor: '#fff',
    marginVertical: 2,
  },

  shadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.15,
    shadowRadius: 1.2,
    elevation: 2,
  },

  sepetiniz_child_sol: {
    padding: 5,
    flex: 1,
    flexGrow: 1.4,
  },
  sepetiniz_child_sag: {
    padding: 5,
    flex: 1,
  },
  sepetiniz_fiyat: {
    padding: 5,
    flexGrow: 2,
    backgroundColor: '#fff',
    width: 40,
    alignItems: 'center',
  },

  //Ürün Detay
  detay_kutu: {
    flexDirection: 'row',
    padding: 10,
    marginHorizontal: 10,
    //backgroundColor:"#E2F4FF"
  },
  detay_kutu_dar: {
    flexDirection: 'row',
    paddingTop: 0,
    paddingLeft: 10,
    marginHorizontal: 10,
    //backgroundColor:"#E2F4FF"
  },

  detay_kutu_sol: {
    flex: 1,
  },

  detay_kutu_sag: {
    width: 100,
    alignItems: 'flex-end',
  },

  detay_kutu_center: {
    flex: 1,
  },

  fiyat_block: {
    display: 'flex',
    flexDirection: 'row',
    height: 72,
    justifyContent: 'space-between',
    backgroundColor: '#78BAE780',
  },

  fiyat_text: {
    //  flex: 1,
    //   flexDirection: 'row',
    //  backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    marginVertical: 11,
    marginLeft: 15,
    borderRadius: 4,
    marginRight: 6,
  },

  fiyat_btn: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    // width: 250,
    paddingHorizontal: 15,
    backgroundColor: '#000',
    borderRadius: 10,
    marginVertical: 10,
    marginRight: 10,
    fontFamily: 'Andale Mono Bold',
  },

  buton_block: {
    display: 'flex',
    flexDirection: 'row',
    height: 50,
    justifyContent: 'space-between',
  },

  btn_13: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 180,
    backgroundColor: '#000000',
    borderRadius: 2,
    marginVertical: 6,
    marginRight: 6,
    fontFamily: 'AndaleMono',
  },

  btn_1: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    backgroundColor: '#78BAE750',
    borderRadius: 2,
    marginVertical: 6,
    marginRight: 6,
    fontFamily: 'AndaleMono',
    // borderWidth: 0.3,
    // borderColor: '#aaa',
  },

  detay_kutu_adet: {
    flex: 1,
    width: 150,
    marginVertical: 20,
    flexDirection: 'row',
    alignContent: 'center',
    alignSelf: 'center',
  },

  DetayBaslik: {
    fontSize: 12,
    borderBottomWidth: 1,
    borderColor: 'silver',
    flex: 1,
  },
  DetayAraBaslik: {
    fontSize: 18,
    flex: 1,
    backgroundColor: '#f1f2f1',
    padding: 6,
    color: '#d40b11',
    paddingLeft: 10,
  },

  DetayUrunFoto: {
    width: '100%',
    height: 250,
    borderWidth: 0,
    borderRadius: 0,
    borderColor: '#ffffff',
  },

  subeFoto: {
    height: (windowWidth * 95) / 100,
    width: (windowWidth * 95) / 100,
    alignSelf: 'center',
    borderRadius: 10,
    marginBottom: 20,
    marginTop: 10,
  },

  //INPUT

  InputStyle: {
    height: 45,
    borderWidth: 1,
    borderRadius: 2,
    borderColor: '#cccccc80',
    fontSize: 16,
    backgroundColor: '#f2f2f2',
    width: '100%',
    paddingLeft: 20,
    marginVertical: 5,
    marginHorizontal: 0,
    alignSelf: 'center',
    color: 'black',
    fontFamily: 'AndaleMono',
  },

  InputStyle_Text: {
    fontSize: 15,
    paddingStart: 20,
    color: 'black',
  },

  InputStylePlaceholder: {
    height: 45,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: 'silver',
    fontSize: 14,
    width: '100%',
    paddingLeft: 20,
    paddingTop: 12,
    marginVertical: 6,
    marginHorizontal: 0,
    alignSelf: 'center',
    backgroundColor: 'white',
    color: 'black',
    marginVertical: 0,
  },

  //Modal

  // centeredView: {
  //   flex: 1,
  //   justifyContent: 'center',
  //   alignItems: 'center',
  //   marginTop: 20,
  // },
  // modalView: {
  //   margin: 20,
  //   backgroundColor: 'white',
  //   borderRadius: 8,
  //   padding: 35,
  //   alignItems: 'center',
  //   shadowColor: '#000',
  //   shadowOffset: {
  //     width: 0,
  //     height: 2,
  //   },
  //   shadowOpacity: 0.25,
  //   shadowRadius: 4,
  //   elevation: 5,
  // },
  // button_modal: {
  //   borderRadius: 20,
  //   padding: 10,
  //   elevation: 2,
  // },
  // buttonOpen: {
  //   backgroundColor: '#F194FF',
  // },
  // buttonClose: {
  //   backgroundColor: '#2196F3',
  // },
  // textStyle: {
  //   color: 'white',
  //   fontWeight: 'bold',
  //   textAlign: 'center',
  // },
  // modalText: {
  //   marginBottom: 15,
  //   textAlign: 'center',
  // },

  //BOX

  urunfoto: {
    width: 96,
    height: 60,
    marginRight: 5,
  },

  /*
  urunfoto: {
    width: 110,
    height: 70,
    marginRight: 10,
  },
*/

  //LİSTELEME
  listeleme: {
    backgroundColor: '#F1F2F1',
    paddingVertical: 8,
    borderRadius: 2,
    marginHorizontal: 20,
    marginVertical: 5,
    fontFamily: 'AndaleMono',
  },
  listeleme_sol: {
    width: 45,
    display: 'flex',
    paddingRight: 10,
    alignSelf: 'flex-start',
  },

  listeleme_orta: {
    alignItems: 'flex-start',
    flex: 1,
    fontFamily: 'AndaleMono',
  },

  listeleme_sag: {
    width: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  listeleme_sag2: {
    width: 150,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  listeleme_sag3: {
    width: 200,
    alignItems: 'center',
    justifyContent: 'center',
  },

  SubeAd: {
    fontSize: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 4,
    paddingLeft: 0,
    color: '#d40b11',
      letterSpacing: -1, 
    fontFamily: 'AndaleMono',
  },

  SubeDetayBaslik: {
    fontSize: 15,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 4,
    paddingLeft: 0,
    fontFamily: 'AndaleMono',
  },

  SubeAdres: {
    fontSize: 15,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 0,
    fontFamily: 'AndaleMono',
    letterSpacing: -1
  },

  OdemeBaslik: {
    fontSize: 18,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 0,
    paddingTop: 2,
    fontFamily: 'AndaleMono',
      letterSpacing: -1, 
  },

  km: {
    fontSize: 11,
    alignItems: 'center',
    marginVertical: 4,
  },
  //LİSTELEME LIST ITEM

  listeleme_LI_kutu: {
    backgroundColor: '#ffffff',
    borderRadius: 5,
    marginHorizontal: 20,
    marginVertical: 10,
  },

  listeleme_LI: {
    paddingTop: 15,
    borderRadius: 5,
    marginVertical: 1,
    backgroundColor: '#F1F2F1',
  },

  // sepete ekle sil

  basket_count_container: {
    display: 'flex',
    flexDirection: 'row',
    height: 30,
    width: 90,
    marginLeft: 50,
    justifyContent: 'space-between',
    backgroundColor: '#ffffff',
    borderRadius: 5,
  },
  basket_count_left: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 30,
    height: 30,
    backgroundColor: '#000',
    borderRadius: 2,
  },
  basket_count_middle: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    borderTopRightRadius: 5,
    borderBottomRightRadius: 5,
  },
  basket_count_right: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 30,
    height: 30,
    backgroundColor: '#000',
    borderRadius: 2,
  },

  basket_count_container2: {
    display: 'flex',
    flexDirection: 'row',
    height: 40,
    width: 120,
    marginVertical: 20,
    justifyContent: 'space-between',
    backgroundColor: '#ffffff',
    borderRadius: 10,
  },
  basket_count_left2: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
    backgroundColor: '#000',
    borderRadius: 3,
  },
  basket_count_middle2: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 100,
    borderTopRightRadius: 4,
    borderBottomRightRadius: 4,
  },
  basket_count_right2: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
    backgroundColor: '#000',
    borderRadius: 3,
  },

  //BUTONLAR
  button: {
    alignItems: 'center',
    alignSelf: 'center',
    width: '50%',
    padding: 10,
    marginVertical: 10,
    borderRadius: 0,
  },
  button100: {
    alignItems: 'center',
    alignSelf: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 0,
  },
  button_yeni: {
    alignItems: 'center',
    alignSelf: 'center',
    width: '90%',
    padding: 10,
    marginVertical: 5,
    borderRadius: 0,
  },
  button_yeni50: {
    alignItems: 'center',
    alignSelf: 'center',
    width: '50%',
    padding: 10,
    marginVertical: 10,
    borderRadius: 0,
  },
  button110: {
    alignItems: 'center',
    alignSelf: 'center',
    paddingVertical: 0,
    paddingHorizontal: 10,
    borderRadius: 4,
    height: 50,
    minWidth: 102,
  },
  button150: {
    alignItems: 'center',
    alignSelf: 'center',
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 4,
  },

  detay_button: {
    alignItems: 'center',
    alignSelf: 'center',
    padding: 10,
    width: '100%',
    marginVertical: 10,
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    borderTopRightRadius: 5,
    borderBottomRightRadius: 5,
  },

  buttonoutline: {
    borderWidth: 1,
    borderColor: 'silver',
  },

  labelStyle: {
    borderColor: 'silver',
    color: '#9f00aa',
  letterSpacing: -1, 
    fontSize: 14,
    fontFamily: 'AndaleMono',
  },

  Vertical0: {
    marginVertical: 0,
  },

  group: {
    borderRadius: 20,
    margin: 20,
  },

  list: {borderRadius: 20},

  ara: {borderWidth: 0, marginHorizontal: 10},

  topmargin: {marginTop: 20},

  kutu: {
    backgroundColor: '#000',
    width: '80%',
    alignItems: 'center',
    alignSelf: 'center',
    padding: 12,
    borderRadius: 2,
    marginBottom: 10,
  },

  blokkutu: {
    backgroundColor: '#f1f2f1',
    width: '94%',
    alignSelf: 'center',
    paddingHorizontal: 8,
    paddingTop: 8,
    borderRadius: 2,
    marginTop: 8,
  },

  servistipi_1: {
    color: '#000000',
    marginVertical: 0,
  },

  servistipi: {
    color: '#949494',
    marginRight: 30,
    marginLeft: 15,
    marginTop: 10,
    fontSize: 20,
  },

  // Yeni tasarım

  home_item_img: {
    width: '100%',
    height: '100%', 
  },

    home_item_img_icon: {
    width: '100%',
    height: '100%',
    borderRadius: 4,
    alignSelf: 'center',
  },

  home_box_title: {
    zIndex: 1,
    position: 'absolute',
    bottom: 20,
    paddingHorizontal: 25,
  },

  home_box_title_txt: {
    fontSize: 28,
    color: 'green',
    fontFamily: 'AndaleMono',
    lineHeight: 28,
  },

  listitem: {
    backgroundColor: '#f2f2f2',
    borderRadius: 10,
    marginBottom: 10,
    marginHorizontal: 10,
  },

  listitemprofil: {
    backgroundColor: '#ffffff',
    marginBottom: 0,
  },

  listelemedelivery: {
    backgroundColor: '#f8ecde',
    //  borderBottomEndRadius:15,
    //  borderBottomLeftRadius:15,
  },
});

export default styles;
