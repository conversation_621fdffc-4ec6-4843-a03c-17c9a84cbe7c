import { StyleSheet } from 'react-native';
import styles from './styles';

const PrimaryButton = StyleSheet.compose(styles.button, styles.bgrenk1, styles.medium);
const PrimaryButton_Detay = StyleSheet.compose(styles.detay_button, styles.bgrenk1, styles.medium);
const PrimaryButton_text = StyleSheet.compose([styles.text16, styles.white, styles.ls1, styles.ozelfont_bold]);

const SecondaryButton = StyleSheet.compose([styles.button, styles.bgrenk1, styles.medium]);
const SecondaryButton_text = StyleSheet.compose([styles.text16, styles.black,styles.ozelfont_bold]);

const ThirdButton = StyleSheet.compose([styles.button, styles.bgrenk3]);
const ThirdButtonButton_text = StyleSheet.compose([styles.text16, styles.white, styles.ozelfont_regular]);

const PrimaryButtonOutline = StyleSheet.compose([styles.button, styles.buttonoutline, styles.bgwhite, styles.medium]);
const PrimaryButtonOutline_text = StyleSheet.compose([styles.text16, styles.black]);

//Small
const PrimaryButton_small = StyleSheet.compose([styles.button, styles.bgrenk1, styles.small]);
const PrimaryButton_text_small = StyleSheet.compose([styles.text12, styles.white, styles.ozelfont_medium]);

const SecondaryButton_small = StyleSheet.compose([styles.button, styles.bgrenk2, styles.small]);
const SecondaryButton_text_small = StyleSheet.compose([styles.text12, styles.white, styles.ozelfont_medium]);

const PrimaryButtonOutline_small = StyleSheet.compose([styles.button, styles.buttonoutline, styles.bgwhite, styles.small]);
const PrimaryButtonOutline_text_small = StyleSheet.compose([styles.text14, styles.black, styles.ozelfont_medium]);

//Sade
const SadeLink = StyleSheet.compose([styles.button, styles.medium]);
const SadeLink_text = StyleSheet.compose([styles.text14, styles.red, styles.ozelfont_medium]);

//KATEGORİLER
const UrunAdi = StyleSheet.compose([styles.text14, styles.black, styles.ozelfont_bold]);
const UrunDesc = StyleSheet.compose([styles.text14, styles.gray, styles.normal, styles.mt5, styles.ls1, styles.ozelfont_regular]);
const Fiyat = StyleSheet.compose([styles.text14, styles.black, styles.ozelfont_medium]);
const Fiyat_sepet = StyleSheet.compose([styles.text14, styles.gray, styles.ozelfont_medium]);
const Adet = StyleSheet.compose([styles.text20, styles.purple, styles.bold, styles.ozelfont_medium]);
const Adet2 = StyleSheet.compose([styles.text20, styles.purple,styles.bold,  styles.ozelfont_medium]);

//delivery type
const tabBarTextStyle = StyleSheet.compose([styles.text18, styles.ozelfont_medium, { paddingTop: 10 }]);

//ÜRÜN DETAY
const DetayUrunAdi = StyleSheet.compose([styles.text20, styles.black, styles.ozelfont_bold]);
const DetayUrunDesc = StyleSheet.compose([styles.text18, styles.gray, styles.normal, styles.ozelfont_regular]);

//Adres

const AdresBaslik = StyleSheet.compose([styles.text22, styles.black, styles.kurumsalfont_regular]);
const AdresDetay = StyleSheet.compose([styles.text15, styles.black, styles.normal, styles.mt10, styles.ozelfont_regular]);
const AdresBaslikSecim = StyleSheet.compose([styles.text18, styles.black, styles.ozelfont_medium]);

//LİST ITEM

const ListeBaslik = StyleSheet.compose([styles.text14, styles.black, styles.ozelfont_medium]);
const ListeDesc = StyleSheet.compose([styles.text14, styles.gray, styles.normal, styles.ozelfont_medium]);

const ListIconStyle = StyleSheet.compose([styles.text16, styles.gray, styles.normal]);

const AcikSube = StyleSheet.compose([styles.text14, styles.green, styles.normal]);

const KapaliSube = StyleSheet.compose([styles.text14, styles.red, styles.normal]);

const PuanBilgi = StyleSheet.compose([styles.text24, styles.white, styles.kurumsalfont_regular, styles.ls1]);
const PuanHarcama = StyleSheet.compose([styles.text16, styles.black, styles.ozelfont_bold, { marginBottom: 5 }]);
const PuanKazanma = StyleSheet.compose([styles.text16, styles.green, styles.ozelfont_bold, { marginBottom: 5 }]);

const RightArrow = StyleSheet.compose([styles.text18, styles.gray, styles.normal]);

export {
  ThirdButton,
  ThirdButtonButton_text,
  PrimaryButton,
  PrimaryButton_text,
  PrimaryButton_Detay,
  SecondaryButton,
  SecondaryButton_text,
  PrimaryButtonOutline,
  PrimaryButtonOutline_text,
  PrimaryButton_small,
  PrimaryButton_text_small,
  SecondaryButton_small,
  SecondaryButton_text_small,
  PrimaryButtonOutline_small,
  PrimaryButtonOutline_text_small,
  SadeLink,
  SadeLink_text,
  UrunAdi,
  UrunDesc,
  Fiyat,
  Fiyat_sepet,
  Adet,
  Adet2,
  DetayUrunAdi,
  DetayUrunDesc,
  AdresBaslik,
  AdresDetay,
  AdresBaslikSecim,
  ListeBaslik,
  ListeDesc,
  ListIconStyle,
  AcikSube,
  KapaliSube,
  PuanBilgi,
  PuanHarcama,
  PuanKazanma,
  RightArrow,
};
