import Clipboard from '@react-native-clipboard/clipboard';
import {safeParse} from './safeParse';
export const readClipboardAndParse = async () => {
  try {
    const text = await Clipboard.getString();
    const parsed = safeParse(text, {
      id: null,
      kod: null,
      hash: null,
      action: null,
    });
    return parsed;
  } catch (err) {
    console.log('error on clipboard=>', err);
    return {
      id: null,
      kod: null,
      hash: null,
      action: null,
    };
  }
};
